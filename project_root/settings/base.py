# --- Standard Library Imports ---
import os
import sys
from pathlib import Path
from decouple import config

# --- Local App Imports ---
from config.logging import LOGGING


# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# --- Core Configuration ---
SECRET_KEY = config("SECRET_KEY")
PLATFORM_FEE_RATE = config("PLATFORM_FEE_RATE", default=0.05, cast=float)
DASHBOARD_CACHE_TIMEOUT = config("DASHBOARD_CACHE_TIMEOUT", default=300, cast=int)
NOTIFICATION_CACHE_TIMEOUT = config("NOTIFICATION_CACHE_TIMEOUT", default=60, cast=int)

# --- Application Configuration ---
APP_NAME = "CozyWish"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "A Django-based venue booking and management platform"


# --- Installed Applications ---
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',  # Required for allauth
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'storages',
    'widget_tweaks',
    'crispy_forms',
    'crispy_bootstrap5',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',

    # Project apps
    'accounts_app',
    'home_app',
    'utils',  # Required by accounts_app
    # 'notifications_app',
    'utility_app',  # Enable for welcome emails
    # 'venues_app',
    # 'discount_app',
    # 'booking_cart_app',
    # 'payments_app',
    # 'dashboard_app',
    # 'review_app',
    # 'admin_app',
]


# --- Middleware ---
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    # 'admin_app.middleware.AdminCSPMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'accounts_app.middleware.security.AccountLockoutMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'accounts_app.middleware.security.PasswordHistoryMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'dashboard_app.middleware.DashboardAccessMiddleware',
]


# --- URL Configuration ---
ROOT_URLCONF = 'project_root.urls'
WSGI_APPLICATION = 'project_root.wsgi.application'


# --- Templates ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                # 'admin_app.context_processors.recent_admin_links',
                # 'booking_cart_app.context_processors.cart_context',
                # 'notifications_app.context_processors.notifications_context',
                # 'dashboard_app.context_processors.provider_context',
            ],
        },
    },
]


# --- Database Configuration (Default) ---
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# --- Test Database Configuration ---
if 'test' in sys.argv:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


# --- Password Validation ---
# Only enforce a minimum length of 12 characters for passwords.
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'accounts_app.utils.validators.CustomMinimumLengthValidator',
        'OPTIONS': {
            'min_length': 12,
        }
    },
]


# --- Internationalization ---
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = 'test' in sys.argv


# --- Static Files Configuration ---
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'accounts_app.CustomUser'


# --- Authentication Configuration ---
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]

# --- Django Allauth Configuration ---

# 1. Account Adapter Configuration
ACCOUNT_ADAPTER = 'accounts_app.allauth.adapters.CozyWishAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.allauth.adapters.CozyWishSocialAccountAdapter'

# 2. Form Configuration
ACCOUNT_FORMS = {
    'signup': 'accounts_app.allauth.forms.CozyWishSignupForm',
    'login': 'accounts_app.allauth.forms.CozyWishLoginForm',
    'change_password': 'accounts_app.allauth.forms.CozyWishPasswordChangeForm',
    'reset_password': 'accounts_app.allauth.forms.CozyWishPasswordResetForm',
}

SOCIALACCOUNT_FORMS = {
    'signup': 'accounts_app.allauth.forms.CozyWishSocialSignupForm',
}

# 3. Authentication Configuration
# Updated Django Allauth settings (using new format)
ACCOUNT_LOGIN_METHODS = {'email'}  # Use email for authentication (replaces ACCOUNT_AUTHENTICATION_METHOD)
ACCOUNT_SIGNUP_FIELDS = ['email*', 'password1*', 'password2*']  # Required fields for signup
ACCOUNT_EMAIL_VERIFICATION = 'optional'  # Let custom adapter handle role-based verification
ACCOUNT_EMAIL_SUBJECT_PREFIX = '[CozyWish] '
ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https'
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 1
ACCOUNT_LOGOUT_ON_PASSWORD_CHANGE = True
ACCOUNT_PRESERVE_USERNAME_CASING = False

# Critical setting for automatic login after signup
ACCOUNT_LOGIN_ON_SIGNUP = True  # Automatically log in users after successful signup

# Username configuration for email-only authentication
ACCOUNT_USERNAME_BLACKLIST = []
ACCOUNT_USER_MODEL_USERNAME_FIELD = None

# Rate limiting configuration (new format)
ACCOUNT_RATE_LIMITS = {
    'login_failed': '5/5m',  # 5 attempts per 5 minutes
}

# Redirect URLs
ACCOUNT_LOGOUT_REDIRECT_URL = '/'
ACCOUNT_LOGIN_REDIRECT_URL = '/'
ACCOUNT_SIGNUP_REDIRECT_URL = '/'

# Logout configuration
ACCOUNT_LOGOUT_ON_GET = False  # Require confirmation page for logout

# 5. Social Account Configuration
SOCIALACCOUNT_AUTO_SIGNUP = False
SOCIALACCOUNT_EMAIL_REQUIRED = True
SOCIALACCOUNT_EMAIL_VERIFICATION = 'mandatory'
SOCIALACCOUNT_QUERY_EMAIL = True

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': ['profile', 'email'],
        'AUTH_PARAMS': {'access_type': 'online'},
        'OAUTH_PKCE_ENABLED': True,
    },
    'apple': {
        'SCOPE': ['name', 'email'],
        'AUTH_PARAMS': {'response_mode': 'form_post'},
    }
}

# --- Django Sites Framework (Required for allauth) ---
SITE_ID = 1

# --- Session Configuration ---
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 3600  # 1 hour in seconds
SESSION_SAVE_EVERY_REQUEST = True  # Update session expiry on each request
SESSION_EXPIRE_AT_BROWSER_CLOSE = True  # Expire when browser closes

# --- Authentication URLs ---
LOGIN_URL = '/accounts/customer/login/'
LOGOUT_REDIRECT_URL = '/'


# --- Cache Configuration (Default) ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True


# --- Logs Configuration ---
LOG_LEVEL = str(config('LOG_LEVEL', default='INFO')).upper() 