# --- Django Imports ---
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse, JsonResponse

# --- Local App Imports ---
# (No direct imports needed - using include() for home_app)


def favicon_view(request):
    """Return empty response for favicon.ico to prevent 404 errors."""
    return HttpResponse(status=204)  # No Content


def health_check_view(request):
    """Return health status for monitoring and load balancer health checks."""
    return JsonResponse({"status": "healthy"})


# --- URL Configuration ---
urlpatterns = [ 
    path('admin/', admin.site.urls),                    # Django admin dashboard
    # path('admin-panel/', include('admin_app.urls')),    # Custom admin panel
    # Django allauth provides the primary authentication endpoints (login, logout, signup, password reset, etc.)
    path('accounts/', include('accounts_app.allauth_urls')),  # Custom allauth URLs with role-based verification

    # Business-specific account features (profiles, privacy, GDPR, AJAX, etc.)
    path('accounts/', include('accounts_app.urls')),    # User profiles and other custom account features
    # path('venues/', include('venues_app.urls')),        # Venue management and search
    # path('discounts/', include('discount_app.urls')),   # Discount management
    # path('bookings/', include('booking_cart_app.urls')), # Booking and cart management
    # path('payments/', include('payments_app.urls')),    # Payment processing and refunds
    # path('dashboard/', include('dashboard_app.urls')),  # Customer and provider dashboards
    # path('reviews/', include('review_app.urls')),       # Review and rating management
    # path('notifications/', include('notifications_app.urls')), # Notification management
    path('health/', health_check_view, name='health_check'), # Health check endpoint
    path('', include('home_app.urls')),                 # Main landing page (home_app)
    # path('utility/', include('utility_app.urls')),      # Public landing pages and utilities
    path('favicon.ico', favicon_view),  # Favicon handler to prevent 404 errors
]

# --- Debug Toolbar URLs ---
if settings.DEBUG:
    try:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
    except ImportError:
        pass


# --- Media and Static Files Configuration ---
if settings.DEBUG:
    # Serve media files (user uploads) during development
    urlpatterns += static(
        settings.MEDIA_URL,
        document_root=settings.MEDIA_ROOT
    )

    # Serve static files (CSS, JS, images) during development
    urlpatterns += static(
        settings.STATIC_URL,
        document_root=settings.STATIC_ROOT
    )
    
    # Add debug toolbar URLs
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns


# --- Error Handlers ---
handler404 = 'home_app.error_handlers.custom_404_view'
handler500 = 'home_app.error_handlers.custom_500_view'
handler403 = 'home_app.error_handlers.custom_403_view'
handler400 = 'home_app.error_handlers.custom_400_view'
