===== BOOKING_CART_APP IMPLEMENTING =====


===== 01. MODELS =====
1. Cart Model
	Customer (linked to user)
	Created At (Timestamp)
	Expiration Timestamp (24 hours after creation)

2. CartItem Model
	Cart (Foreign Key)
	Service (linked to service)
	Selected Date & Time Slot (dropdown selection)
	Price (including applicable discounts)

3. Booking Model
	Customer (linked to user)
	Service Provider (linked to venue/provider)
	Total Price
	Booking Status (Pending, Confirmed, Canceled, Declined)
	Booking Date & Time
	Created At (Timestamp)



===== 02. VIEWS and FUNCTIONS ===== 
1. Customer Features:
	Add/Remove Services: Modify cart before confirming.
	Time Slot Selection: Dropdown of available time slots.
	Real-Time Availability: Simple daily schedule-based slot checks.
	Booking Confirmation: Email & dashboard notifications.
	Cancellation: Request within 6 hours, manual refund by admin.
	Booking History: View past bookings and their statuses.

2. Service Provider Features
	Set Availability: Simple daily schedule availability.
	Booking Management: View upcoming bookings (max 10 future bookings).
	Accept/Decline Bookings: Notify customers instantly upon changes.
	
3. Admin Features
	Monitor Bookings: All booking statuses dashboard.
	Booking Dispute Resolution: Manually resolve issues.
	Booking Analytics: Reports on trends, cancellations, confirmations.

4. Notifications (Email & Dashboard)
	Customer: Booking created, canceled, provider declined, confirmed.
	Service Provider: New booking created, canceled by customer, daily booking summary.
	

===== 03.PAGES and URLs =====  
/cart/ (view/edit cart)
/checkout/ (confirm and create booking)
/bookings/ (customer booking history)
/provider/bookings/ (provider management page)



===== 06. TASK LIST =====
- Create cart and booking models and relationships.
- Implement add/remove/edit cart functionalities.
- Cart expiry logic (automatic 24-hour expiration).
- Service availability dropdown logic.
- Booking creation and notification flows.
- Provider booking acceptance/decline workflows.
- Admin booking monitoring and dispute resolution tools.
- Booking analytics and reporting.



===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
- Adding/removing/editing cart items
- 24-hour cart expiration logic
- Booking creation and time slot selection logic
- Booking management by providers (accept/decline)
	



===== 08. KEY PAGES HTML PAGE CONTENT =====







===== 10. SCREEN STATES =====




