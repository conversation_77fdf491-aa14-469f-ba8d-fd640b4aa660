===== VENUES_APP IMPLEMENTING =====


===== 01. MODELS =====
1. Venue Model
	- Service Provider (one-to-one relationship with User)
	- Venue Name
	- State
	- County
	- City
	- Street Number
	- Street Name
	- Latitude
	- Longitude
	- Short Description (text field, limited characters)
	- Main/Featured Image (S3 storage)
	- Operating Hours (e.g., 9AM-5PM daily schedule)
	- Opening Times (custom notes, e.g., "Closed on holidays")
	- Tags (keywords for search optimization, maximum 5)
	- Approval Status (Pending / Approved / Rejected)
	- Visibility (Active / Inactive)

2. VenueImage Model
	- Venue (foreign key to Venue)
	- Image (up to 7 images per venue, including main image)
	- Order (numeric to manage image sequence)

3. VenueFAQ Model
	- Venue (foreign key to Venue)
	- Question (short text)
	- Answer (short text)
	- Limit: 5 FAQs per venue

4. Service Model
	- Venue (foreign key to Venue)
	- Service Title
	- Short Description (text field, limited characters)
	- Price (minimum and maximum for variable pricing)
	- Duration (in minutes)

5. Category Model (flat structure)
	- Name (e.g., Spa, Massage, Salon)


6. VenueCategory Relationship Model (Many-to-Many between Venue and Category)

7. FlaggedVenue Model
	- Venue (foreign key)
	- Flagged By (Customer reference)
	- Reason (text)
	- Status (Pending / Reviewed)


===== 02. VIEWS and FUNCTIONALITIES ===== 
1. Customer Features:
	- Advanced Venue Search (keyword, category selection, hierarchical location filter: state → county → city)
	- Filtering Options:
		* Promotions/Discounts (integrated into results)
		* Price Range
		* Venue Type (e.g., Male-only, Female-only, All)
	- Sorting Options:
		* Lowest Price
		* Highest Rating (future integration with review app)
		* Newest Venues
	- Venue Detail View: Display full venue details (description, address, images, FAQs, services, pricing, operating hours).
	- Browse Services within Venues
	- Flag Venue: Submit a form to flag inappropriate venues, pending admin review.

2. Service Provider Features:
	- Manage One Venue: Providers can create, update, or completely delete their venue.
	- Venue Creation & Editing: Includes detailed forms for venue fields, uploading up to 7 images, setting main featured image.
	- Manage Services: Add/edit/delete up to 7 services with details (title, description, variable price range, duration).
	- Manage FAQs: Add/edit/delete up to 5 FAQs for their venue.
	- Request Approval: Any venue or service changes trigger admin approval before appearing live.

3. Admin Features (Django Admin Panel)
	- Approve/Reject Venues: Review submitted venues or updates, approve or reject with optional comments.
	- Manage Categories: Create, edit, delete categories, assign venues to multiple categories.
	- Moderate Flagged Venues: Review flags from customers, take action (e.g., deactivate venue).
	- Dashboard Alerts: Dashboard notification for new or updated venues awaiting approval.



===== 03. URL STRUCTURE =====  
/venues/search/ (Advanced Search & Filter page)
/venues/<venue_id>/ (Venue Detail page)
/provider/venue/create/ (Service Provider Venue creation)
/provider/venue/edit/ (Venue Edit page)
/provider/venue/delete/ (Venue Deletion confirmation page)
/provider/services/manage/ (Manage venue services page)



===== 04. LIST OF PAGES =====
1. Customer Pages:
	Venue Search & Filter Results Page
	Venue Detail Page (info, images, FAQs, services list)
2. Service Provider Pages
	Venue Creation/Edit Form Page
	Service Management Page (add/edit/remove)
	FAQ Management Page (add/edit/remove)
3. Admin Pages (Django Admin Panel):
Venue Approval Dashboard
Venue Category Management
Flagged Venue Moderation Page



===== 05. IMPLEMENTATION NOTES =====
- Image Management: Venue images stored on AWS S3, max 7 per venue, easy ordering functionality (first image set as main featured image).
- Venue Deletion: Providers can completely delete their venue, including associated images, FAQs, and services.
- Advanced Search: Combination of keyword search with explicit filtering by categories and location.
- Admin Approval Workflow: All venue/service submissions trigger a dashboard notification for admin review before becoming active.







===== 06. TASK LIST =====
Preparation
	Define and create all required models and relationships
	Setup AWS S3 media uploads specifically for venue images

Service Provider Functionalities
	Venue create/edit/delete views and forms
	Service create/edit/delete views and forms
	FAQ management views

Customer Functionalities
	Advanced venue search & filter functionality
	Venue detail view, including services and FAQs
	Venue flagging functionality

Admin Functionalities
	Approval dashboard for venues
	Category management (create/edit/delete/assign)
	Flagged venue moderation views
	Dashboard notification for new venue approval requests
	
Testing & Validation
	Write unit tests for all venue-related models/views
	Integration tests for venue management workflows
	Manual smoke test checklist preparation
 



===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
- Venue creation/editing by service provider
- Venue deletion functionality
- Service addition/editing/deletion (max 7)
- FAQ management (max 5)
- Advanced search/filtering and sorting of venues/services
- Venue flagging by customers
- Admin venue approval workflow
- Admin moderation of flagged venues
- Venue/category assignments management by admin
	



===== 08. KEY PAGES HTML PAGE CONTENT =====
- Advanced Search & Filter Page: Clear search fields, intuitive filters, sortable result listings.
- Venue Detail Page: Well-organized display of all venue details, images, services, and FAQs.
- Service Provider Management Pages: Forms and tables for easy venue and service management.



===== 09. SCREEN STATES =====
01. Customer Venue Search Page
State 1: Blank advanced search form (fields: keywords, categories, locations).
State 2 (Results Displayed): Display venues based on criteria.
State 3 (No Results): Display friendly "No matching venues" message, suggest modifying criteria.

02.  Venue Detail Page (Customer)
State 1: Display venue info, services, images, FAQs clearly.
State 2: Flagging inappropriate venue (popup/modal confirmation form).

03. Provider Venue Creation/Edit Page
State 1: Empty form or existing venue details pre-filled.
State 2 (Validation Errors): Inline error messages for missing or incorrect fields.
State 3 (Submission Success): Message displayed: "Venue submitted successfully, awaiting admin approval."

04. Provider Service Management Page
State 1: Display current services (up to 7).
State 2 (Adding/Editing): Form with fields pre-filled for edit or empty for add.
State 3 (Validation Errors): Inline errors for validation issues.
State 4 (Successful Addition/Editing/Removal): Toast notifications for user feedback.

05. Admin Venue Approval Dashboard
State 1: List pending venues for approval.
State 2 (Review): Detailed venue info, approve/reject buttons.
State 3 (Approved/Rejected): Status updated accordingly, optional comment field displayed.
