===== NOTIFICATIONS_APP IMPLEMENTING =====


===== 01. MODELS =====
1. Notification
	User (linked to User)
	Type (Booking, Payment, Review, Announcement)
	Message (text content)
	Created At
	Read Status (Unread/Read)

2. AdminAnnouncement
	Target Audience (All, Customers, Providers, Specific Groups)
	Announcement Text
	Created At
	Status (Sent/Pending)



===== 02. VIEWS and FUNCTIONALITIES ===== 
Customer Notifications (Mandatory):
	Booking confirmations/updates/cancellations.
	Payment successful notifications.
	Provider responses to reviews.
	Platform announcements/promotions.
	Unread notification count and history view.

Service Provider Notifications (Mandatory):
	New booking requests, updates, cancellations.
	Customer review notifications.
	Payment received notifications.
	Platform announcements.
	Unread notification count and history view.

Admin Notifications:
	Send instant announcements targeted by groups (no scheduling).
	View delivery status and notification history.

Notification Styles:
	Email detailed, dashboard concise and informative.




===== 03. URL STRUCTURE =====  


===== 04. LIST OF PAGES =====

===== 05. IMPLEMENTATION NOTES =====







===== 06. TASK LIST =====


===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====



===== 08. KEY PAGES HTML PAGE CONTENT =====



===== 09. SCREEN STATES =====

