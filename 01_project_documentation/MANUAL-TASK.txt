===============================================================================
                          PART 1: TESTING FOUNDATION & OVERVIEW
===============================================================================


### 📊 TESTING METRICS & SUCCESS CRITERIA

**Critical Success Factors:**
- 100% of authentication flows working correctly
- 100% of booking and payment workflows functional
- Zero security vulnerabilities in access control
- All email notifications delivered successfully
- Admin approval workflows operating correctly
- Mobile responsiveness across all pages


**Django Applications:**
1. accounts_app - User authentication and profiles
2. venues_app - Venue and service management
3. booking_cart_app - Booking and cart functionality
4. payments_app - Payment processing and refunds
5. review_app - Review and rating system
6. notifications_app - Notification management
7. discount_app - Discount and promotion system
8. dashboard_app - User dashboards and analytics
9. admin_app - Administrative controls
10. utility_app - Shared utilities and homepage

### 👥 USER ROLES & PERMISSIONS MATRIX

**Customer Role:**
- Browse and search venues/services
- Create bookings and manage cart
- Process payments and view history
- Submit reviews and ratings
- Manage personal profile and preferences
- Receive notifications and communications

**Service Provider Role:**
- Create and manage ONE venue (business constraint)
- Add up to 7 services per venue
- Manage team members and staff profiles
- Set availability and accept/decline bookings
- View earnings and analytics
- Respond to customer reviews
- Create venue-specific discounts

**Admin Role:**
- Approve/reject venue submissions
- Manage all user accounts and permissions
- Monitor platform-wide analytics
- Handle dispute resolution
- Create platform-wide discounts
- Manage content and announcements
- Access system health monitoring


### 🔄 CORE BUSINESS WORKFLOWS

**Primary Customer Journey:**
1. Registration/Login → Profile Setup
2. Venue Search → Service Discovery
3. Cart Management → Booking Creation
4. Payment Processing → Confirmation
5. Service Completion → Review Submission

**Primary Provider Journey:**
1. Registration → Email Verification
2. Venue Creation → Admin Approval
3. Service Setup → Availability Management
4. Booking Management → Service Delivery
5. Earnings Tracking → Performance Analytics

**Primary Admin Journey:**
1. Platform Monitoring → User Management
2. Venue Approval → Content Management
3. Dispute Resolution → Analytics Review
4. System Health → Performance Optimization


### 🚨 CRITICAL TESTING PRIORITIES

**Priority 1 - CRITICAL (Must Pass 100%):**
- User authentication and authorization
- Payment processing and financial transactions
- Booking creation and management
- Data security and privacy protection
- Email notification delivery

**Priority 2 - HIGH (Must Pass 95%):**
- Venue creation and approval workflow
- Service management and availability
- Review and rating system
- Admin controls and user management
- Mobile responsiveness

**Priority 3 - MEDIUM (Must Pass 90%):**
- Search and filtering functionality
- Dashboard analytics and reporting
- Discount system and promotions
- Notification preferences and management
- Content management features

**Priority 4 - LOW (Must Pass 80%):**
- Advanced filtering options
- Export functionality
- System health monitoring
- Performance optimization features
- Non-critical UI enhancements


### 🔧 PRE-TESTING SETUP CHECKLIST

**Environment Preparation:**
- [ ] Test database seeded with required data
- [ ] Email service configured for test notifications
- [ ] Payment gateway in test mode
- [ ] Media storage accessible and functional
- [ ] All Django apps properly configured
- [ ] Test user accounts created and verified
- [ ] Browser developer tools configured
- [ ] Screen recording software ready (if needed)
- [ ] Issue tracking system prepared
- [ ] Test data backup created



===============================================================================
                    PART 2: AUTHENTICATION & USER MANAGEMENT TESTING
                                    (accounts_app)
===============================================================================

## 🔐 AUTHENTICATION & USER MANAGEMENT OVERVIEW

The accounts_app is the foundation of the CozyWish platform, handling user authentication, profile management, and role-based access control. This section covers comprehensive testing for all user types: Customers, Service Providers, and Admin users.

### 📋 ACCOUNTS_APP TESTING SCOPE

**Core Features to Test:**
- User registration and email verification
- Login/logout functionality with session management
- Password reset and change workflows
- Profile management and image uploads
- Team/staff management for service providers
- Security features and access control
- Login history and suspicious activity detection

**URL Patterns Covered:**
- /accounts/customer/* - Customer authentication and profile
- /accounts/provider/* - Service provider authentication and profile
- /accounts/for-business/ - Business landing page
- /accounts/logout/ - Unified logout functionality

---


## 🧪 SECTION 2A: CUSTOMER AUTHENTICATION TESTING

### TEST SUITE 2A.1: Customer Registration
**Priority: CRITICAL | Estimated Time: 45 minutes**


#### Test Case 2A.1.1: Valid Customer Registration
**Objective:** Verify customers can successfully register with valid information
**Prerequisites:** None
**Test Data:**
- Email: <EMAIL>
- Password: SecurePass123!
- Confirm Password: SecurePass123!

**Steps:**
1. Navigate to /accounts/customer/signup/
2. Fill in registration form with test data
3. Submit form
4. Verify redirect to appropriate success page
5. Check database for new user record
6. Verify welcome email sent

**Expected Results:**
- [ ] Registration form displays correctly
- [ ] Form validation works for all fields
- [ ] User account created with 'customer' role
- [ ] Welcome email sent to registered address
- [ ] User automatically logged in after registration
- [ ] Redirect to customer dashboard or profile setup

**Validation Points:**
- User record exists in CustomUser table
- CustomerProfile record created and linked
- Email field used as username (no separate username)
- Password properly hashed and stored
- User role set to 'customer'



#### Test Case 2A.1.2: Invalid Email Format Registration
**Objective:** Verify proper validation for invalid email formats
**Test Data:**
- Email: invalid-email-format
- Password: SecurePass123!

**Steps:**
1. Navigate to /accounts/customer/signup/
2. Enter invalid email format
3. Enter valid password
4. Submit form

**Expected Results:**
- [ ] Form validation prevents submission
- [ ] Clear error message displayed for email field
- [ ] User remains on registration page
- [ ] No user account created



#### Test Case 2A.1.3: Duplicate Email Registration
**Objective:** Verify system prevents duplicate email registration
**Prerequisites:** Existing customer account with test email
**Test Data:**
- Email: <EMAIL> (already registered)
- Password: NewPassword123!

**Steps:**
1. Navigate to /accounts/customer/signup/
2. Enter email that already exists in system
3. Enter valid password
4. Submit form

**Expected Results:**
- [ ] Form validation prevents submission
- [ ] Error message: "A user with this email already exists"
- [ ] User remains on registration page
- [ ] No duplicate user account created

#### Test Case 2A.1.4: Weak Password Registration
**Objective:** Verify password strength requirements enforced
**Test Data:**
- Email: <EMAIL>
- Password: 123 (weak password)

**Steps:**
1. Navigate to /accounts/customer/signup/
2. Enter valid email
3. Enter weak password
4. Submit form

**Expected Results:**
- [ ] Form validation prevents submission
- [ ] Password strength error message displayed
- [ ] Requirements clearly shown (length, complexity)
- [ ] No user account created



### TEST SUITE 2A.2: Customer Login/Logout
**Priority: CRITICAL | Estimated Time: 30 minutes**

#### Test Case 2A.2.1: Valid Customer Login
**Objective:** Verify customers can login with correct credentials
**Prerequisites:** Existing active customer account
**Test Data:**
- Email: <EMAIL>
- Password: CorrectPassword123!

**Steps:**
1. Navigate to /accounts/customer/login/
2. Enter valid credentials
3. Submit login form
4. Verify successful login and redirect

**Expected Results:**
- [ ] Login form displays correctly
- [ ] Successful authentication with valid credentials
- [ ] User session established
- [ ] Redirect to customer dashboard or intended page
- [ ] Navigation shows logged-in state
- [ ] Login history record created

#### Test Case 2A.2.2: Invalid Customer Login
**Objective:** Verify proper handling of invalid login attempts
**Test Data:**
- Email: <EMAIL>
- Password: WrongPassword123!

**Steps:**
1. Navigate to /accounts/customer/login/
2. Enter valid email with incorrect password
3. Submit login form
4. Verify error handling

**Expected Results:**
- [ ] Login rejected with invalid credentials
- [ ] Generic error message displayed (security best practice)
- [ ] User remains on login page
- [ ] No session established
- [ ] Failed login attempt logged

#### Test Case 2A.2.3: Customer Logout
**Objective:** Verify customers can logout successfully
**Prerequisites:** Logged-in customer account

**Steps:**
1. Login as customer
2. Navigate to /accounts/logout/
3. Confirm logout action
4. Verify session termination

**Expected Results:**
- [ ] User session terminated
- [ ] Redirect to homepage or login page
- [ ] Navigation shows logged-out state
- [ ] Protected pages no longer accessible
- [ ] Logout confirmation message displayed

### TEST SUITE 2A.3: Customer Password Management
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 2A.3.1: Password Reset Request
**Objective:** Verify customers can request password reset
**Prerequisites:** Existing customer account
**Test Data:**
- Email: <EMAIL>

**Steps:**
1. Navigate to /accounts/customer/password-reset/
2. Enter registered email address
3. Submit password reset request
4. Check email for reset link

**Expected Results:**
- [ ] Password reset form displays correctly
- [ ] Reset request accepted for valid email
- [ ] Password reset email sent
- [ ] Reset link contains valid token
- [ ] Confirmation message displayed
- [ ] User redirected to "done" page

#### Test Case 2A.3.2: Password Reset Completion
**Objective:** Verify customers can complete password reset
**Prerequisites:** Valid password reset token
**Test Data:**
- New Password: NewSecurePass123!
- Confirm Password: NewSecurePass123!

**Steps:**
1. Click valid password reset link from email
2. Enter new password
3. Confirm new password
4. Submit password reset form
5. Attempt login with new password

**Expected Results:**
- [ ] Reset form displays with valid token
- [ ] New password accepted and saved
- [ ] Password properly hashed in database
- [ ] Success confirmation displayed
- [ ] User can login with new password
- [ ] Old password no longer works

#### Test Case 2A.3.3: Password Change from Profile
**Objective:** Verify customers can change password while logged in
**Prerequisites:** Logged-in customer account
**Test Data:**
- Current Password: CurrentPass123!
- New Password: UpdatedPass123!

**Steps:**
1. Login as customer
2. Navigate to /accounts/customer/change-password/
3. Enter current password
4. Enter new password
5. Confirm new password
6. Submit form

**Expected Results:**
- [ ] Password change form displays correctly
- [ ] Current password validation required
- [ ] New password meets strength requirements
- [ ] Password updated successfully
- [ ] Success confirmation message
- [ ] User can login with new password

---

## 🧪 SECTION 2B: SERVICE PROVIDER AUTHENTICATION TESTING

### TEST SUITE 2B.1: Service Provider Registration
**Priority: CRITICAL | Estimated Time: 60 minutes**

#### Test Case 2B.1.1: Valid Provider Registration
**Objective:** Verify service providers can register with business information
**Test Data:**
- Email: <EMAIL>
- Password: BusinessPass123!
- Business Name: Test Spa & Wellness
- Business Phone: (*************
- Business Type: Spa

**Steps:**
1. Navigate to /accounts/provider/signup/
2. Fill in all required business information
3. Submit registration form
4. Check for email verification requirement

**Expected Results:**
- [ ] Registration form displays with business fields
- [ ] All business information fields validate correctly
- [ ] User account created with 'service_provider' role
- [ ] ServiceProviderProfile record created
- [ ] Email verification sent
- [ ] User redirected to verification pending page
- [ ] Account inactive until email verified

#### Test Case 2B.1.2: Provider Email Verification
**Objective:** Verify email verification process for service providers
**Prerequisites:** Newly registered provider account
**Test Data:** Valid verification token from email

**Steps:**
1. Register new service provider account
2. Check email for verification link
3. Click verification link
4. Verify account activation

**Expected Results:**
- [ ] Verification email sent with valid token
- [ ] Verification link leads to confirmation page
- [ ] Account status changed to active
- [ ] User can now login
- [ ] Welcome message displayed
- [ ] Redirect to provider dashboard

#### Test Case 2B.1.3: Provider Login Before Verification
**Objective:** Verify unverified providers cannot access system
**Prerequisites:** Unverified provider account
**Test Data:**
- Email: <EMAIL>
- Password: CorrectPassword123!

**Steps:**
1. Register provider account (don't verify email)
2. Attempt to login with correct credentials
3. Verify access restriction

**Expected Results:**
- [ ] Login attempt rejected
- [ ] Message indicating email verification required
- [ ] Link to resend verification email
- [ ] No access to provider features
- [ ] User redirected to verification page

### TEST SUITE 2B.2: Service Provider Profile Management
**Priority: HIGH | Estimated Time: 45 minutes**

#### Test Case 2B.2.1: Provider Profile Viewing
**Objective:** Verify providers can view their business profile
**Prerequisites:** Verified provider account

**Steps:**
1. Login as verified service provider
2. Navigate to /accounts/provider/profile/
3. Verify all profile information displays

**Expected Results:**
- [ ] Profile page displays correctly
- [ ] Business information shown accurately
- [ ] Profile image displays (if uploaded)
- [ ] Contact information visible
- [ ] Edit profile link available
- [ ] Professional layout and formatting

#### Test Case 2B.2.2: Provider Profile Editing
**Objective:** Verify providers can update business information
**Prerequisites:** Verified provider account
**Test Data:**
- Updated Business Name: Updated Spa & Wellness Center
- Updated Phone: (*************
- Updated Description: Premium wellness services

**Steps:**
1. Login as service provider
2. Navigate to /accounts/provider/profile/edit/
3. Update business information
4. Upload new business logo (within 5MB limit)
5. Submit changes

**Expected Results:**
- [ ] Edit form pre-populated with current data
- [ ] All fields editable and validate correctly
- [ ] Image upload works with size/format validation
- [ ] Changes saved successfully
- [ ] Updated information displays on profile
- [ ] Success confirmation message

#### Test Case 2B.2.3: Provider Logo Upload
**Objective:** Verify business logo upload functionality
**Prerequisites:** Verified provider account
**Test Data:**
- Valid image: business_logo.jpg (2MB)
- Invalid image: large_file.jpg (10MB)

**Steps:**
1. Navigate to provider profile edit
2. Upload valid business logo
3. Submit form
4. Attempt upload of oversized image
5. Verify validation

**Expected Results:**
- [ ] Valid image uploads successfully
- [ ] Image displays in profile
- [ ] File size validation enforced (5MB limit)
- [ ] Format validation (JPG/PNG only)
- [ ] Error messages for invalid files
- [ ] Previous image replaced correctly

### TEST SUITE 2B.3: Team Management
**Priority: MEDIUM | Estimated Time: 50 minutes**

#### Test Case 2B.3.1: Add Team Member
**Objective:** Verify providers can add team/staff members
**Prerequisites:** Verified provider account
**Test Data:**
- Name: Sarah Johnson
- Role: Massage Therapist
- Specialties: Deep Tissue, Swedish Massage
- Bio: 5 years experience in therapeutic massage

**Steps:**
1. Login as service provider
2. Navigate to team management section
3. Click "Add Team Member"
4. Fill in team member information
5. Upload team member photo
6. Submit form

**Expected Results:**
- [ ] Add team member form displays correctly
- [ ] All fields validate properly
- [ ] Photo upload works (5MB limit)
- [ ] Team member record created
- [ ] Member appears in team list
- [ ] Success confirmation message

#### Test Case 2B.3.2: Edit Team Member
**Objective:** Verify providers can update team member information
**Prerequisites:** Existing team member record
**Test Data:**
- Updated Role: Senior Massage Therapist
- Updated Specialties: Deep Tissue, Hot Stone, Prenatal

**Steps:**
1. Navigate to team management
2. Select existing team member
3. Click edit option
4. Update information
5. Submit changes

**Expected Results:**
- [ ] Edit form pre-populated with current data
- [ ] Changes save successfully
- [ ] Updated information displays correctly
- [ ] Team member list reflects changes
- [ ] Success message displayed

#### Test Case 2B.3.3: Remove Team Member
**Objective:** Verify providers can remove team members
**Prerequisites:** Existing team member record

**Steps:**
1. Navigate to team management
2. Select team member to remove
3. Click remove/delete option
4. Confirm removal action

**Expected Results:**
- [ ] Confirmation dialog appears
- [ ] Team member removed from database
- [ ] Member no longer appears in team list
- [ ] Associated photo deleted from storage
- [ ] Success confirmation message

---

## 🧪 SECTION 2C: SECURITY & ACCESS CONTROL TESTING

### TEST SUITE 2C.1: Login Security Features
**Priority: CRITICAL | Estimated Time: 40 minutes**

#### Test Case 2C.1.1: Multiple Failed Login Attempts
**Objective:** Verify security alerts for suspicious login activity
**Prerequisites:** Valid user account
**Test Data:**
- Email: <EMAIL>
- Wrong Password: WrongPass123! (use 5+ times)

**Steps:**
1. Attempt login with wrong password 5 times
2. Check for security alert generation
3. Verify admin notification
4. Check login history records

**Expected Results:**
- [ ] Failed attempts logged in LoginHistory
- [ ] Security alert created after threshold reached
- [ ] Admin receives notification of suspicious activity
- [ ] IP address tracked and flagged
- [ ] Account temporarily locked (if implemented)

#### Test Case 2C.1.2: Login History Tracking
**Objective:** Verify all login attempts are properly logged
**Prerequisites:** User account with login activity

**Steps:**
1. Login successfully as customer
2. Logout and login again
3. Attempt failed login
4. Check login history records

**Expected Results:**
- [ ] All login attempts recorded
- [ ] Successful logins marked correctly
- [ ] Failed attempts marked correctly
- [ ] IP addresses captured
- [ ] Timestamps accurate
- [ ] User agent information stored

#### Test Case 2C.1.3: Cross-Role Access Prevention
**Objective:** Verify users cannot access other role features
**Prerequisites:** Customer and Provider accounts

**Steps:**
1. Login as customer
2. Attempt to access provider-only URLs
3. Login as provider
4. Attempt to access admin-only URLs
5. Verify access restrictions

**Expected Results:**
- [ ] Customer cannot access /accounts/provider/* URLs
- [ ] Provider cannot access admin features
- [ ] Proper 403 Forbidden responses
- [ ] Redirect to appropriate login page
- [ ] Error messages displayed
- [ ] No data exposure across roles

### TEST SUITE 2C.2: Account Security Features
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 2C.2.1: Account Deactivation
**Objective:** Verify users can deactivate their accounts
**Prerequisites:** Active user account

**Steps:**
1. Login as customer
2. Navigate to account deactivation page
3. Confirm deactivation request
4. Verify account status change
5. Attempt login with deactivated account

**Expected Results:**
- [ ] Deactivation confirmation required
- [ ] Account marked as inactive
- [ ] Login attempts rejected
- [ ] User data preserved but inaccessible
- [ ] Reactivation process available
- [ ] Email confirmation sent

#### Test Case 2C.2.2: Password Security Requirements
**Objective:** Verify password complexity requirements enforced
**Test Data:**
- Weak passwords: "123", "password", "abc123"
- Strong password: "SecureP@ssw0rd123!"

**Steps:**
1. Attempt registration with weak passwords
2. Attempt password change with weak passwords
3. Verify enforcement of requirements

**Expected Results:**
- [ ] Minimum length requirement enforced
- [ ] Complexity requirements enforced
- [ ] Common passwords rejected
- [ ] Clear error messages displayed
- [ ] Requirements listed for user guidance

---

## 🧪 SECTION 2D: ADMIN USER MANAGEMENT TESTING

### TEST SUITE 2D.1: Admin Authentication
**Priority: CRITICAL | Estimated Time: 30 minutes**

#### Test Case 2D.1.1: Admin Login Access
**Objective:** Verify admin users can access administrative features
**Prerequisites:** Admin user account

**Steps:**
1. Login as admin user
2. Verify access to admin-only features
3. Check navigation options
4. Test admin dashboard access

**Expected Results:**
- [ ] Admin login successful
- [ ] Admin-specific navigation visible
- [ ] Access to user management features
- [ ] Access to system monitoring
- [ ] Admin dashboard loads correctly

#### Test Case 2D.1.2: Admin User Management
**Objective:** Verify admin can manage user accounts
**Prerequisites:** Admin account and test user accounts

**Steps:**
1. Login as admin
2. Navigate to user management
3. View customer and provider lists
4. Test user search and filtering
5. Test account activation/deactivation

**Expected Results:**
- [ ] All user accounts visible to admin
- [ ] Search and filter functions work
- [ ] Admin can activate/deactivate accounts
- [ ] Admin can reset user passwords
- [ ] Admin can view user details
- [ ] Actions properly logged

---



## 📊 SECTION 2E: ACCOUNTS_APP TESTING SUMMARY

### Test Execution Checklist
**Total Test Cases: 25 | Estimated Time: 5.5 hours**

**Critical Tests (Must Pass 100%):**
- [ ] Customer registration and login (2A.1.1, 2A.2.1)
- [ ] Provider registration and verification (2B.1.1, 2B.1.2)
- [ ] Password reset functionality (2A.3.1, 2A.3.2)
- [ ] Security access controls (2C.1.3)
- [ ] Admin authentication (2D.1.1)

**High Priority Tests (Must Pass 95%):**
- [ ] Profile management (2A.1.2-2A.1.4, 2B.2.1-2B.2.3)
- [ ] Team management (2B.3.1-2B.3.3)
- [ ] Login security features (2C.1.1-2C.1.2)
- [ ] Account security (2C.2.1-2C.2.2)

**Medium Priority Tests (Must Pass 90%):**
- [ ] Email validation and error handling
- [ ] Image upload functionality
- [ ] Login history tracking

### Common Issues to Watch For
1. **Email Delivery**: Ensure all notification emails are sent
2. **Image Upload**: Verify file size and format validation
3. **Session Management**: Check session timeout and security
4. **Cross-Role Access**: Ensure proper permission enforcement
5. **Password Security**: Verify strength requirements enforced


===============================================================================
                    PART 3: VENUE & SERVICE MANAGEMENT TESTING
                                    (venues_app)
===============================================================================

## 🏢 VENUE & SERVICE MANAGEMENT OVERVIEW

The venues_app is the core business functionality of CozyWish, handling venue creation, service management, and customer discovery. This section covers comprehensive testing for venue lifecycle management, service offerings, and the critical business constraint of one venue per service provider.

### 📋 VENUES_APP TESTING SCOPE

**Core Features to Test:**
- Venue creation and approval workflow
- Single venue constraint enforcement
- Service creation and management (up to 7 services)
- Venue image management and optimization
- Operating hours and availability settings
- FAQ and amenity management
- Location-based search and filtering
- Category management and assignment
- Venue approval and moderation system

**URL Patterns Covered:**
- /venues/ - Public venue search and listing
- /venues/search/ - Advanced search functionality
- /venues/create/ - Provider venue creation
- /venues/manage/ - Provider venue management
- /venues/<slug>/ - Individual venue detail pages
- /venues/admin/ - Admin venue approval system

**Business Constraints to Validate:**
- Each service provider can create only ONE venue
- Maximum 7 services per venue
- Venue approval required before public visibility
- Image upload limits (5MB per image)
- Professional image upload interfaces

---

## 🧪 SECTION 3A: VENUE CREATION & MANAGEMENT TESTING

### TEST SUITE 3A.1: Venue Creation Process
**Priority: CRITICAL | Estimated Time: 60 minutes**

#### Test Case 3A.1.1: First Venue Creation (Valid)
**Objective:** Verify service providers can create their first venue successfully
**Prerequisites:** Verified service provider account with no existing venue
**Test Data:**
- Venue Name: Serenity Spa & Wellness
- Address: 123 Main Street, Anytown, CA 90210
- Phone: (*************
- Description: Premium spa services in a tranquil environment
- Categories: Spa, Massage Therapy
- Operating Hours: Mon-Fri 9AM-8PM, Sat-Sun 10AM-6PM

**Steps:**
1. Login as verified service provider
2. Navigate to /venues/create/
3. Fill in all required venue information
4. Select appropriate categories
5. Set operating hours
6. Upload main venue image (within 5MB limit)
7. Upload 4 additional gallery images
8. Submit venue creation form

**Expected Results:**
- [ ] Venue creation form displays correctly with all fields
- [ ] Address validation works properly
- [ ] Category selection allows multiple categories
- [ ] Operating hours interface is intuitive
- [ ] Image upload shows instant preview
- [ ] Main image upload works (optional field)
- [ ] Gallery images upload successfully (5 image limit)
- [ ] Venue saved with "pending" approval status
- [ ] Provider redirected to dashboard with success message
- [ ] Venue not visible to customers until approved


#### Test Case 3A.1.2: Single Venue Constraint Enforcement
**Objective:** Verify providers cannot create multiple venues
**Prerequisites:** Service provider with existing venue (any status)

**Steps:**
1. Login as provider who already has a venue
2. Attempt to navigate to /venues/create/
3. Verify access restriction
4. Check for appropriate redirect and messaging

**Expected Results:**
- [ ] Access to venue creation blocked
- [ ] Clear message explaining single venue limit
- [ ] Redirect to existing venue management page
- [ ] No ability to create second venue
- [ ] Business constraint properly enforced


#### Test Case 3A.1.3: Venue Creation with Image Validation
**Objective:** Verify image upload validation and limits
**Prerequisites:** Verified service provider account
**Test Data:**
- Valid images: venue1.jpg (2MB), venue2.png (3MB)
- Invalid images: large_image.jpg (8MB), document.pdf
- Oversized set: 6 images (exceeding 5-image limit)

**Steps:**
1. Navigate to venue creation form
2. Attempt to upload oversized image (>5MB)
3. Attempt to upload invalid file format
4. Upload 6 images (exceeding limit)
5. Upload valid images within limits

**Expected Results:**
- [ ] File size validation enforced (5MB limit)
- [ ] Format validation (JPG/PNG only)
- [ ] Maximum 5 images enforced (main + 4 gallery)
- [ ] Clear error messages for invalid uploads
- [ ] Valid images upload successfully with preview
- [ ] Image optimization applied automatically


#### Test Case 3A.1.4: Required Field Validation
**Objective:** Verify all required fields are properly validated
**Test Data:** Incomplete venue information (missing required fields)

**Steps:**
1. Navigate to venue creation form
2. Submit form with missing venue name
3. Submit form with missing address
4. Submit form with missing phone number
5. Verify field-specific error messages

**Expected Results:**
- [ ] Form submission blocked for missing required fields
- [ ] Field-specific error messages displayed
- [ ] Bootstrap invalid-feedback classes applied
- [ ] User guided to complete all required information
- [ ] Form retains entered data after validation errors

### TEST SUITE 3A.2: Venue Information Management
**Priority: HIGH | Estimated Time: 45 minutes**


#### Test Case 3A.2.1: Venue Profile Editing
**Objective:** Verify providers can update venue information
**Prerequisites:** Approved venue owned by provider
**Test Data:**
- Updated Description: Enhanced spa services with new treatment rooms
- Updated Phone: (*************
- New Operating Hours: Extended weekend hours

**Steps:**
1. Login as venue owner
2. Navigate to venue management page
3. Click "Edit Venue Information"
4. Update venue details
5. Submit changes
6. Verify updates reflected

**Expected Results:**
- [ ] Edit form pre-filled with current venue data
- [ ] All fields editable except venue name (if restricted)
- [ ] Changes save successfully
- [ ] Updated information displays on venue page
- [ ] Success confirmation message shown
- [ ] Significant changes trigger re-approval (if implemented)


#### Test Case 3A.2.2: FAQ Management
**Objective:** Verify providers can manage venue FAQs
**Prerequisites:** Existing venue
**Test Data:**
- FAQ 1: "What should I bring?" / "Please bring comfortable clothing"
- FAQ 2: "Do you offer parking?" / "Yes, free parking available"

**Steps:**
1. Navigate to venue management
2. Access FAQ management section
3. Add new FAQ entries
4. Edit existing FAQ
5. Delete FAQ entry
6. Verify FAQ display on venue page

**Expected Results:**
- [ ] FAQ management interface accessible
- [ ] Can add multiple FAQ entries
- [ ] FAQ editing works correctly
- [ ] FAQ deletion with confirmation
- [ ] FAQs display properly on public venue page
- [ ] FAQ limits enforced (if any)


#### Test Case 3A.2.3: Operating Hours Management
**Objective:** Verify providers can manage venue operating hours
**Prerequisites:** Existing venue
**Test Data:**
- Monday: 9:00 AM - 8:00 PM
- Tuesday: 9:00 AM - 8:00 PM
- Wednesday: Closed
- Weekend: 10:00 AM - 6:00 PM

**Steps:**
1. Navigate to operating hours management
2. Set different hours for each day
3. Mark specific days as closed
4. Set special holiday hours (if supported)
5. Save operating hours

**Expected Results:**
- [ ] Operating hours interface intuitive and clear
- [ ] Can set different hours for each day
- [ ] Can mark days as closed
- [ ] Hours validation (open time before close time)
- [ ] Operating hours display correctly on venue page
- [ ] Changes save successfully


### TEST SUITE 3A.3: Venue Image Management
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 3A.3.1: Image Upload and Management
**Objective:** Verify comprehensive image management functionality
**Prerequisites:** Existing venue
**Test Data:**
- New images: spa_interior.jpg (3MB), treatment_room.png (2MB)
- Replacement image: updated_lobby.jpg (4MB)

**Steps:**
1. Navigate to venue image management
2. Upload additional venue images
3. Set primary image selection
4. Reorder images using drag-drop or buttons
5. Delete existing image
6. Replace main venue image

**Expected Results:**
- [ ] Image upload interface user-friendly
- [ ] Can select primary image (only one primary)
- [ ] Image reordering functionality works
- [ ] Image deletion with confirmation
- [ ] Automatic storage cleanup on deletion
- [ ] Images display in correct order on venue page
- [ ] Primary image prominently displayed


#### Test Case 3A.3.2: Image Optimization and Performance
**Objective:** Verify image optimization and thumbnail generation
**Prerequisites:** Venue with uploaded images

**Steps:**
1. Upload high-resolution images
2. Check for automatic optimization
3. Verify thumbnail generation
4. Test image loading performance
5. Check different image sizes served

**Expected Results:**
- [ ] Images automatically optimized for web
- [ ] Thumbnails generated for gallery view
- [ ] Different sizes served based on context
- [ ] Fast image loading on venue pages
- [ ] Proper image compression applied
- [ ] Original image quality preserved when needed

---

## 🧪 SECTION 3B: SERVICE MANAGEMENT TESTING

### TEST SUITE 3B.1: Service Creation and Management
**Priority: CRITICAL | Estimated Time: 50 minutes**

#### Test Case 3B.1.1: Service Creation (Valid)
**Objective:** Verify providers can create services for their venue
**Prerequisites:** Approved venue
**Test Data:**
- Service Name: Deep Tissue Massage
- Category: Massage Therapy
- Duration: 60 minutes
- Price Range: $80 - $120
- Description: Therapeutic massage targeting muscle tension

**Steps:**
1. Navigate to venue service management
2. Click "Add New Service"
3. Fill in service information
4. Select service category
5. Set duration and pricing
6. Upload service image (optional)
7. Submit service creation form

**Expected Results:**
- [ ] Service creation form displays correctly
- [ ] Category selection from predefined list
- [ ] Duration and pricing validation
- [ ] Service image upload optional
- [ ] Service created and linked to venue
- [ ] Service appears in venue service list
- [ ] Success confirmation message


#### Test Case 3B.1.2: Seven Service Limit Enforcement
**Objective:** Verify maximum 7 services per venue constraint
**Prerequisites:** Venue with 7 existing services

**Steps:**
1. Navigate to service management for venue with 7 services
2. Attempt to add 8th service
3. Verify limit enforcement
4. Check error messaging

**Expected Results:**
- [ ] "Add Service" option disabled or hidden
- [ ] Clear message about 7-service limit
- [ ] Cannot create additional services
- [ ] Business constraint properly enforced
- [ ] Suggestion to edit existing services instead


#### Test Case 3B.1.3: Service Editing and Updates
**Objective:** Verify providers can update service information
**Prerequisites:** Existing service
**Test Data:**
- Updated Price: $90 - $130
- Updated Description: Enhanced therapeutic massage with aromatherapy

**Steps:**
1. Navigate to service management
2. Select existing service to edit
3. Update service information
4. Change pricing
5. Update description
6. Submit changes

**Expected Results:**
- [ ] Service edit form pre-populated
- [ ] All service fields editable
- [ ] Price validation (min <= max)
- [ ] Changes save successfully
- [ ] Updated information displays on venue page
- [ ] Service availability unaffected by edits


#### Test Case 3B.1.4: Service Deletion
**Objective:** Verify providers can remove services
**Prerequisites:** Service with no active bookings

**Steps:**
1. Navigate to service management
2. Select service to delete
3. Confirm deletion action
4. Verify service removal

**Expected Results:**
- [ ] Deletion confirmation dialog appears
- [ ] Service removed from venue
- [ ] Service no longer bookable
- [ ] Associated images cleaned up
- [ ] Booking history preserved (if any)
- [ ] Success confirmation message


### TEST SUITE 3B.2: Service Categories and Organization
**Priority: MEDIUM | Estimated Time: 30 minutes**

#### Test Case 3B.2.1: Service Category Assignment
**Objective:** Verify services can be properly categorized
**Prerequisites:** Venue with services
**Test Data:**
- Categories: Massage, Facial, Body Treatment, Wellness

**Steps:**
1. Create services in different categories
2. Verify category assignment
3. Check category filtering on venue page
4. Test category-based search

**Expected Results:**
- [ ] Services properly categorized
- [ ] Category filters work on venue page
- [ ] Search by category returns correct results
- [ ] Category hierarchy respected (if applicable)
- [ ] Multiple categories per service (if supported)

#### Test Case 3B.2.2: Service Availability Settings
**Objective:** Verify service availability can be managed
**Prerequisites:** Existing services

**Steps:**
1. Navigate to service management
2. Temporarily disable a service
3. Set service-specific operating hours
4. Configure service capacity limits
5. Test availability display

**Expected Results:**
- [ ] Can temporarily disable services
- [ ] Disabled services not bookable
- [ ] Service-specific hours override venue hours
- [ ] Capacity limits enforced
- [ ] Availability clearly displayed to customers

---

## 🧪 SECTION 3C: VENUE APPROVAL WORKFLOW TESTING

### TEST SUITE 3C.1: Admin Venue Approval Process
**Priority: CRITICAL | Estimated Time: 45 minutes**

#### Test Case 3C.1.1: Venue Approval Queue
**Objective:** Verify admin can view and manage pending venues
**Prerequisites:** Admin account and pending venues

**Steps:**
1. Login as admin
2. Navigate to venue approval dashboard
3. View list of pending venues
4. Access venue details for review
5. Check venue information completeness

**Expected Results:**
- [ ] Pending venues visible in admin queue
- [ ] Venue details accessible for review
- [ ] All venue information displayed clearly
- [ ] Images viewable in admin interface
- [ ] Venue status clearly indicated
- [ ] Approval actions available

#### Test Case 3C.1.2: Venue Approval Process
**Objective:** Verify admin can approve venues
**Prerequisites:** Pending venue for approval
**Test Data:**
- Approval Notes: "Venue meets all requirements. Approved for listing."

**Steps:**
1. Select pending venue for approval
2. Review venue information thoroughly
3. Add approval notes
4. Approve venue
5. Verify venue status change
6. Check provider notification

**Expected Results:**
- [ ] Venue status changed to "approved"
- [ ] Venue becomes visible to customers
- [ ] Provider receives approval notification
- [ ] Approval notes saved in system
- [ ] Venue appears in public search results
- [ ] Admin action logged

#### Test Case 3C.1.3: Venue Rejection Process
**Objective:** Verify admin can reject venues with reasons
**Prerequisites:** Pending venue for rejection
**Test Data:**
- Rejection Reason: "Incomplete business information. Please provide valid business license."

**Steps:**
1. Select pending venue for rejection
2. Provide detailed rejection reason
3. Reject venue
4. Verify venue status change
5. Check provider notification

**Expected Results:**
- [ ] Venue status changed to "rejected"
- [ ] Venue remains hidden from customers
- [ ] Provider receives rejection notification with reason
- [ ] Rejection reason saved in system
- [ ] Provider can edit and resubmit venue
- [ ] Admin action logged

### TEST SUITE 3C.2: Venue Re-approval Triggers
**Priority: HIGH | Estimated Time: 30 minutes**

#### Test Case 3C.2.1: Significant Change Re-approval
**Objective:** Verify significant venue changes trigger re-approval
**Prerequisites:** Approved venue
**Test Data:**
- Significant changes: Business name, address, primary services

**Steps:**
1. Make significant changes to approved venue
2. Submit changes
3. Verify re-approval requirement
4. Check venue status change

**Expected Results:**
- [ ] Significant changes trigger re-approval
- [ ] Venue status changed to "pending"
- [ ] Venue remains visible with old information
- [ ] Admin notified of changes requiring review
- [ ] Provider notified of re-approval requirement

#### Test Case 3C.2.2: Minor Change Approval
**Objective:** Verify minor changes don't require re-approval
**Prerequisites:** Approved venue
**Test Data:**
- Minor changes: Description updates, FAQ additions, operating hours

**Steps:**
1. Make minor changes to approved venue
2. Submit changes
3. Verify immediate approval
4. Check venue status remains approved

**Expected Results:**
- [ ] Minor changes applied immediately
- [ ] Venue status remains "approved"
- [ ] Changes visible immediately
- [ ] No admin intervention required
- [ ] Provider receives confirmation

---

## 🧪 SECTION 3D: CUSTOMER VENUE DISCOVERY TESTING

### TEST SUITE 3D.1: Venue Search and Filtering
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 3D.1.1: Basic Venue Search
**Objective:** Verify customers can search for venues
**Prerequisites:** Multiple approved venues in database
**Test Data:**
- Search terms: "massage", "spa", "facial"
- Location: "Los Angeles, CA"

**Steps:**
1. Navigate to /venues/ or /venues/search/
2. Enter search terms
3. Specify location
4. Submit search
5. Review search results

**Expected Results:**
- [ ] Search form displays correctly
- [ ] Search returns relevant results
- [ ] Results show venue name, image, rating
- [ ] Location filtering works
- [ ] Search terms match venue/service descriptions
- [ ] Results paginated if many venues

#### Test Case 3D.1.2: Advanced Filtering
**Objective:** Verify advanced search filters work correctly
**Prerequisites:** Venues with various attributes
**Test Data:**
- Category filter: Spa, Massage
- Price range: $50-$150
- Rating filter: 4+ stars
- Distance: Within 10 miles

**Steps:**
1. Access advanced search filters
2. Apply category filters
3. Set price range
4. Filter by rating
5. Set distance limit
6. Apply filters and review results

**Expected Results:**
- [ ] All filters apply correctly
- [ ] Results match filter criteria
- [ ] Multiple filters work together
- [ ] Filter options clearly displayed
- [ ] Results update dynamically (if AJAX)
- [ ] Clear filter options available

#### Test Case 3D.1.3: Location-Based Search
**Objective:** Verify location search and autocomplete
**Prerequisites:** Venues in different locations
**Test Data:**
- Locations: "Beverly Hills, CA", "Santa Monica, CA"

**Steps:**
1. Use location autocomplete feature
2. Search by specific city
3. Search by ZIP code
4. Test location radius search
5. Verify distance calculations

**Expected Results:**
- [ ] Location autocomplete works
- [ ] City-based search returns local venues
- [ ] ZIP code search functions
- [ ] Distance calculations accurate
- [ ] Results sorted by distance
- [ ] Map integration (if implemented)

### TEST SUITE 3D.2: Venue Detail Pages
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 3D.2.1: Venue Information Display
**Objective:** Verify venue detail pages show complete information
**Prerequisites:** Approved venue with full information

**Steps:**
1. Navigate to venue detail page
2. Verify all venue information displays
3. Check image gallery functionality
4. Review service listings
5. Check operating hours display

**Expected Results:**
- [ ] Venue name and description prominent
- [ ] Image gallery functional with navigation
- [ ] All services listed with prices
- [ ] Operating hours clearly displayed
- [ ] Contact information visible
- [ ] FAQs section accessible
- [ ] Professional layout and design

#### Test Case 3D.2.2: Service Booking Integration
**Objective:** Verify customers can initiate bookings from venue page
**Prerequisites:** Venue with bookable services

**Steps:**
1. Navigate to venue detail page
2. Select service for booking
3. Check availability calendar
4. Initiate booking process
5. Verify cart integration

**Expected Results:**
- [ ] Services clearly bookable
- [ ] Availability calendar displays
- [ ] Booking process initiates smoothly
- [ ] Service added to cart correctly
- [ ] Pricing displayed accurately
- [ ] Booking flow intuitive

---

## 📊 SECTION 3E: VENUES_APP TESTING SUMMARY

### Test Execution Checklist
**Total Test Cases: 22 | Estimated Time: 5.5 hours**

**Critical Tests (Must Pass 100%):**
- [ ] Venue creation process (3A.1.1)
- [ ] Single venue constraint (3A.1.2)
- [ ] Service creation and 7-service limit (3B.1.1, 3B.1.2)
- [ ] Venue approval workflow (3C.1.1-3C.1.3)
- [ ] Customer venue search (3D.1.1)

**High Priority Tests (Must Pass 95%):**
- [ ] Image management and validation (3A.1.3, 3A.3.1-3A.3.2)
- [ ] Venue information management (3A.2.1-3A.2.3)
- [ ] Service management (3B.1.3-3B.1.4)
- [ ] Advanced search and filtering (3D.1.2-3D.1.3)
- [ ] Venue detail pages (3D.2.1-3D.2.2)

**Medium Priority Tests (Must Pass 90%):**
- [ ] Required field validation (3A.1.4)
- [ ] Service categories and availability (3B.2.1-3B.2.2)
- [ ] Re-approval triggers (3C.2.1-3C.2.2)

### Common Issues to Watch For
1. **Single Venue Constraint**: Ensure providers cannot create multiple venues
2. **Image Upload Limits**: Verify 5MB limit and format validation
3. **Service Limits**: Ensure 7-service maximum enforced
4. **Approval Workflow**: Check venue visibility before/after approval
5. **Search Performance**: Verify search results load quickly
6. **Mobile Responsiveness**: Test venue pages on mobile devices



===============================================================================
                    PART 4: BOOKING, PAYMENT & REVIEW TESTING
                        (booking_cart_app, payments_app, review_app)
===============================================================================

## 🛒 BOOKING, PAYMENT & REVIEW OVERVIEW

This section covers the core customer transaction flow of CozyWish: from service discovery to booking completion and post-service review. These three apps work together to create the complete customer experience and revenue generation for the platform.

### 📋 PART 4 TESTING SCOPE

**Core Features to Test:**
- Shopping cart functionality with 24-hour expiration
- Real-time service availability checking
- Booking creation and management workflows
- Provider booking acceptance/decline process
- Payment processing with Stripe integration
- Refund request and processing system
- Review and rating submission
- Provider review response system
- Review moderation and flagging

**URL Patterns Covered:**
- /bookings/cart/ - Shopping cart management
- /bookings/checkout/ - Booking creation process
- /bookings/list/ - Customer booking history
- /payments/checkout/ - Payment processing
- /payments/history/ - Payment and refund history
- /reviews/submit/ - Review submission
- /reviews/venue/ - Venue review display

**Business Rules to Validate:**
- Cart items expire after 24 hours
- 6-hour cancellation window for bookings
- Provider capacity limits (up to 10 concurrent bookings)
- Payment processing with platform commission
- Review submission only after service completion

---

## 🧪 SECTION 4A: BOOKING & CART MANAGEMENT TESTING

### TEST SUITE 4A.1: Shopping Cart Functionality
**Priority: CRITICAL | Estimated Time: 50 minutes**

#### Test Case 4A.1.1: Add Service to Cart
**Objective:** Verify customers can add services to their shopping cart
**Prerequisites:** Customer account and available services
**Test Data:**
- Service: Deep Tissue Massage (60 min, $100)
- Date: Tomorrow at 2:00 PM
- Special Requests: "Please use light pressure"

**Steps:**
1. Login as customer
2. Navigate to venue with available services
3. Select service for booking
4. Choose date and time from availability calendar
5. Add special requests/notes
6. Click "Add to Cart"
7. Verify cart contents

**Expected Results:**
- [ ] Service selection interface displays correctly
- [ ] Availability calendar shows real-time slots
- [ ] Date/time selection validates availability
- [ ] Special requests field accepts text input
- [ ] Service added to cart successfully
- [ ] Cart icon/counter updates immediately
- [ ] Cart contents display service details correctly
- [ ] Pricing calculated accurately


#### Test Case 4A.1.2: Cart Item Management
**Objective:** Verify customers can manage items in their cart
**Prerequisites:** Cart with multiple services
**Test Data:**
- Original quantity: 1 service
- Updated quantity: 2 services
- Time change: 3:00 PM instead of 2:00 PM

**Steps:**
1. Navigate to cart page (/bookings/cart/)
2. Modify service quantity
3. Change appointment time
4. Update special requests
5. Remove one service from cart
6. Verify total price updates

**Expected Results:**
- [ ] Cart displays all added services
- [ ] Quantity modification works correctly
- [ ] Time slot changes validate availability
- [ ] Special requests can be edited
- [ ] Service removal works with confirmation
- [ ] Total price recalculates automatically
- [ ] Cart persists across browser sessions
- [ ] Empty cart state displays appropriately


#### Test Case 4A.1.3: Cart Expiration (24-Hour Rule)
**Objective:** Verify cart items expire after 24 hours
**Prerequisites:** Cart with services added 24+ hours ago
**Test Data:** Services added more than 24 hours ago

**Steps:**
1. Add services to cart
2. Wait 24+ hours (or simulate with database)
3. Return to cart page
4. Verify expired items handling
5. Check availability re-validation

**Expected Results:**
- [ ] Expired cart items automatically removed
- [ ] Clear notification about expired items
- [ ] Remaining valid items preserved
- [ ] Availability re-checked for valid items
- [ ] User prompted to re-add expired services
- [ ] Cart total updates correctly


#### Test Case 4A.1.4: Real-Time Availability Checking
**Objective:** Verify availability updates in real-time
**Prerequisites:** Service with limited availability
**Test Data:** Time slots with limited capacity

**Steps:**
1. Open service booking in two browser windows
2. Select same time slot in both windows
3. Complete booking in first window
4. Attempt to book same slot in second window
5. Verify availability conflict handling

**Expected Results:**
- [ ] Availability calendar updates in real-time
- [ ] Conflicting bookings prevented
- [ ] Clear error message for unavailable slots
- [ ] Alternative time slots suggested
- [ ] Cart validates availability before checkout
- [ ] Provider capacity limits respected

### TEST SUITE 4A.2: Booking Creation Process
**Priority: CRITICAL | Estimated Time: 45 minutes**


#### Test Case 4A.2.1: Checkout Process
**Objective:** Verify customers can create bookings from cart
**Prerequisites:** Cart with valid services
**Test Data:**
- Customer contact: (*************
- Emergency contact: Jane Doe (*************
- Special instructions: "First-time customer"

**Steps:**
1. Navigate to cart with services
2. Click "Proceed to Checkout"
3. Fill in required contact information
4. Add special instructions
5. Review booking summary
6. Confirm booking creation

**Expected Results:**
- [ ] Checkout form displays with cart summary
- [ ] Contact information fields validate properly
- [ ] Special instructions field available
- [ ] Booking summary shows all details
- [ ] Total cost calculated with any discounts
- [ ] Booking created with unique ID
- [ ] Customer receives booking confirmation
- [ ] Provider receives new booking notification


#### Test Case 4A.2.2: Booking Confirmation and Notifications
**Objective:** Verify booking confirmation process
**Prerequisites:** Completed booking

**Steps:**
1. Complete booking creation
2. Check booking confirmation page
3. Verify email notifications sent
4. Check booking appears in customer history
5. Verify provider receives notification

**Expected Results:**
- [ ] Booking confirmation page displays
- [ ] Unique booking reference number shown
- [ ] Customer receives confirmation email
- [ ] Provider receives new booking notification
- [ ] Booking appears in customer booking list
- [ ] Booking status set to "pending" or "confirmed"
- [ ] Calendar slots marked as booked

#### Test Case 4A.2.3: Double Booking Prevention
**Objective:** Verify system prevents double bookings
**Prerequisites:** Service with single availability slot

**Steps:**
1. Create booking for specific time slot
2. Attempt to create another booking for same slot
3. Verify conflict detection
4. Check error handling

**Expected Results:**
- [ ] Double booking attempt blocked
- [ ] Clear error message displayed
- [ ] Alternative time slots suggested
- [ ] Original booking preserved
- [ ] Availability calendar updated
- [ ] Provider capacity limits enforced

### TEST SUITE 4A.3: Provider Booking Management
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 4A.3.1: Provider Booking Dashboard
**Objective:** Verify providers can view and manage bookings
**Prerequisites:** Provider account with incoming bookings

**Steps:**
1. Login as service provider
2. Navigate to booking management dashboard
3. View today's bookings
4. Check upcoming bookings calendar
5. Review booking details

**Expected Results:**
- [ ] Booking dashboard displays clearly
- [ ] Today's bookings prominently shown
- [ ] Calendar view available for upcoming bookings
- [ ] Booking details accessible
- [ ] Customer contact information visible
- [ ] Special requests/notes displayed
- [ ] Booking status clearly indicated

#### Test Case 4A.3.2: Booking Acceptance/Decline
**Objective:** Verify providers can accept or decline bookings
**Prerequisites:** Pending booking requests
**Test Data:**
- Acceptance note: "Looking forward to your visit!"
- Decline reason: "Fully booked for that time slot"

**Steps:**
1. Navigate to pending booking
2. Review booking details
3. Accept booking with note
4. Decline different booking with reason
5. Verify status updates and notifications

**Expected Results:**
- [ ] Booking acceptance updates status to "confirmed"
- [ ] Booking decline updates status to "declined"
- [ ] Customer receives acceptance/decline notification
- [ ] Provider notes/reasons saved and sent
- [ ] Calendar availability updates accordingly
- [ ] Booking history maintains all status changes

#### Test Case 4A.3.3: Provider Availability Management
**Objective:** Verify providers can manage their availability
**Prerequisites:** Provider account with services
**Test Data:**
- Available slots: Mon-Fri 9AM-5PM
- Blocked time: Dec 25 (Holiday)
- Capacity limit: 3 concurrent bookings

**Steps:**
1. Navigate to availability management
2. Set available time slots
3. Block specific dates/times
4. Configure capacity limits
5. Save availability settings

**Expected Results:**
- [ ] Availability calendar interface intuitive
- [ ] Can set different availability per service
- [ ] Date/time blocking works correctly
- [ ] Capacity limits enforced
- [ ] Changes reflect immediately in booking system
- [ ] Bulk availability setting available

### TEST SUITE 4A.4: Booking Cancellation
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 4A.4.1: Customer Cancellation (Within 6 Hours)
**Objective:** Verify customers can cancel bookings within 6-hour window
**Prerequisites:** Booking scheduled more than 6 hours in future
**Test Data:** Booking scheduled for tomorrow

**Steps:**
1. Navigate to customer booking history
2. Select booking to cancel
3. Initiate cancellation process
4. Provide cancellation reason
5. Confirm cancellation

**Expected Results:**
- [ ] Cancellation option available for eligible bookings
- [ ] Cancellation reason field provided
- [ ] Booking status updated to "cancelled"
- [ ] Provider receives cancellation notification
- [ ] Refund process initiated automatically
- [ ] Time slot becomes available again
- [ ] Cancellation confirmation sent to customer

#### Test Case 4A.4.2: Cancellation Window Enforcement
**Objective:** Verify 6-hour cancellation window is enforced
**Prerequisites:** Booking scheduled within 6 hours
**Test Data:** Booking scheduled in 4 hours

**Steps:**
1. Navigate to booking scheduled within 6 hours
2. Attempt to cancel booking
3. Verify cancellation restriction
4. Check alternative options

**Expected Results:**
- [ ] Cancellation option disabled/hidden
- [ ] Clear message about 6-hour policy
- [ ] Contact information provided for exceptions
- [ ] Booking remains confirmed
- [ ] Policy explanation available
- [ ] Alternative contact methods suggested

---

## 🧪 SECTION 4B: PAYMENT PROCESSING TESTING

### TEST SUITE 4B.1: Payment Checkout Process
**Priority: CRITICAL | Estimated Time: 45 minutes**

#### Test Case 4B.1.1: Valid Payment Processing
**Objective:** Verify customers can complete payment successfully
**Prerequisites:** Confirmed booking ready for payment
**Test Data:**
- Test Card: 4242 4242 4242 4242 (Stripe test card)
- Expiry: 12/25
- CVC: 123
- ZIP: 90210

**Steps:**
1. Navigate to payment checkout for booking
2. Review payment summary
3. Enter valid test card information
4. Submit payment
5. Verify payment confirmation

**Expected Results:**
- [ ] Payment form displays booking summary
- [ ] Total amount calculated correctly (including fees)
- [ ] Stripe payment form loads properly
- [ ] Valid card information accepted
- [ ] Payment processes successfully
- [ ] Payment confirmation page displays
- [ ] Customer receives payment receipt email
- [ ] Provider receives payment notification

#### Test Case 4B.1.2: Invalid Payment Handling
**Objective:** Verify proper handling of payment failures
**Prerequisites:** Booking ready for payment
**Test Data:**
- Invalid Card: 4000 0000 0000 0002 (Stripe test decline card)
- Expired Card: 4000 0000 0000 0069
- Insufficient Funds: 4000 0000 0000 9995

**Steps:**
1. Attempt payment with declined card
2. Try payment with expired card
3. Test insufficient funds scenario
4. Verify error handling for each case

**Expected Results:**
- [ ] Payment declined appropriately
- [ ] Clear error messages displayed
- [ ] Booking remains unpaid but reserved
- [ ] Customer can retry with different card
- [ ] No partial charges processed
- [ ] Error details logged for admin review

#### Test Case 4B.1.3: Payment Receipt and History
**Objective:** Verify payment receipts and history tracking
**Prerequisites:** Completed payment

**Steps:**
1. Complete successful payment
2. Check payment confirmation page
3. Verify receipt email sent
4. Navigate to payment history
5. Download/view receipt

**Expected Results:**
- [ ] Payment receipt generated immediately
- [ ] Receipt email sent to customer
- [ ] Payment appears in customer history
- [ ] Receipt downloadable as PDF
- [ ] All payment details accurate
- [ ] Transaction ID provided
- [ ] Platform fee breakdown shown (if applicable)

### TEST SUITE 4B.2: Refund Processing
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 4B.2.1: Refund Request Submission
**Objective:** Verify customers can request refunds
**Prerequisites:** Paid booking eligible for refund
**Test Data:**
- Refund Reason: "Service provider cancelled appointment"
- Additional Details: "Received cancellation 2 hours before appointment"

**Steps:**
1. Navigate to eligible paid booking
2. Click "Request Refund"
3. Select refund reason
4. Provide additional details
5. Submit refund request

**Expected Results:**
- [ ] Refund request form displays
- [ ] Reason selection required
- [ ] Additional details field available
- [ ] Refund request submitted successfully
- [ ] Provider receives refund request notification
- [ ] Admin receives refund request for review
- [ ] Request status trackable by customer

#### Test Case 4B.2.2: Provider Refund Response
**Objective:** Verify providers can respond to refund requests
**Prerequisites:** Pending refund request
**Test Data:**
- Provider Response: "Approved - appointment was cancelled due to emergency"

**Steps:**
1. Login as service provider
2. Navigate to refund requests
3. Review refund request details
4. Approve or decline refund
5. Provide response notes

**Expected Results:**
- [ ] Refund requests visible to provider
- [ ] Request details clearly displayed
- [ ] Approve/decline options available
- [ ] Response notes field provided
- [ ] Decision updates request status
- [ ] Customer receives provider response notification
- [ ] Admin receives decision for processing

#### Test Case 4B.2.3: Admin Refund Processing
**Objective:** Verify admin can process approved refunds
**Prerequisites:** Approved refund request
**Test Data:** Approved refund ready for processing

**Steps:**
1. Login as admin
2. Navigate to refund processing queue
3. Review approved refund
4. Process refund through payment system
5. Confirm refund completion

**Expected Results:**
- [ ] Approved refunds visible in admin queue
- [ ] Refund details and approval notes shown
- [ ] Process refund option available
- [ ] Refund processed through Stripe
- [ ] Customer receives refund confirmation
- [ ] Refund amount reflects in customer account
- [ ] Transaction recorded in payment history

### TEST SUITE 4B.3: Provider Earnings Management
**Priority: MEDIUM | Estimated Time: 30 minutes**

#### Test Case 4B.3.1: Earnings Dashboard
**Objective:** Verify providers can view earnings information
**Prerequisites:** Provider with completed paid bookings

**Steps:**
1. Login as service provider
2. Navigate to earnings dashboard
3. View daily/weekly/monthly earnings
4. Check individual payment details
5. Review platform fee deductions

**Expected Results:**
- [ ] Earnings dashboard displays clearly
- [ ] Multiple time period views available
- [ ] Individual payment details accessible
- [ ] Platform fees clearly shown
- [ ] Net earnings calculated correctly
- [ ] Payment dates and booking references shown

#### Test Case 4B.3.2: Payout History
**Objective:** Verify providers can track payout history
**Prerequisites:** Provider with payout history

**Steps:**
1. Navigate to payout history section
2. View past payouts
3. Check payout details and dates
4. Verify payout amounts

**Expected Results:**
- [ ] Payout history clearly displayed
- [ ] Payout dates and amounts accurate
- [ ] Payout methods shown
- [ ] Transaction references provided
- [ ] Outstanding balance visible
- [ ] Next payout date indicated (if scheduled)

---

## 🧪 SECTION 4C: REVIEW & RATING SYSTEM TESTING

### TEST SUITE 4C.1: Review Submission
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 4C.1.1: Valid Review Submission
**Objective:** Verify customers can submit reviews after service completion
**Prerequisites:** Completed booking/service
**Test Data:**
- Rating: 5 stars
- Review Title: "Excellent massage therapy"
- Review Text: "Sarah provided an amazing deep tissue massage. Very professional and skilled."

**Steps:**
1. Navigate to completed booking
2. Click "Write Review" option
3. Select star rating (1-5 stars)
4. Enter review title and text
5. Submit review

**Expected Results:**
- [ ] Review form accessible only after service completion
- [ ] Star rating selection works (1-5 stars)
- [ ] Review title and text fields validate
- [ ] Review submission successful
- [ ] Provider receives new review notification
- [ ] Review appears on venue page
- [ ] Customer receives submission confirmation

#### Test Case 4C.1.2: Review Submission Restrictions
**Objective:** Verify review submission rules are enforced
**Prerequisites:** Various booking statuses
**Test Data:** Bookings in different states (pending, cancelled, completed)

**Steps:**
1. Attempt review on pending booking
2. Attempt review on cancelled booking
3. Attempt multiple reviews for same venue
4. Verify restriction enforcement

**Expected Results:**
- [ ] Cannot review pending bookings
- [ ] Cannot review cancelled bookings
- [ ] Cannot submit multiple reviews for same venue
- [ ] Clear messages explaining restrictions
- [ ] Only completed bookings allow reviews
- [ ] One review per customer per venue enforced

#### Test Case 4C.1.3: Review Content Validation
**Objective:** Verify review content meets requirements
**Prerequisites:** Completed booking ready for review
**Test Data:**
- Valid review: Appropriate length and content
- Invalid review: Extremely short or inappropriate content

**Steps:**
1. Submit review with minimum content
2. Submit review with maximum content
3. Test content validation rules
4. Verify character limits

**Expected Results:**
- [ ] Minimum content requirements enforced
- [ ] Maximum character limits enforced
- [ ] Inappropriate content flagged (if implemented)
- [ ] Clear validation messages displayed
- [ ] Review quality maintained

### TEST SUITE 4C.2: Review Management
**Priority: MEDIUM | Estimated Time: 35 minutes**

#### Test Case 4C.2.1: Customer Review History
**Objective:** Verify customers can manage their reviews
**Prerequisites:** Customer with submitted reviews

**Steps:**
1. Navigate to customer review history
2. View all submitted reviews
3. Edit existing review
4. Delete review (if allowed)

**Expected Results:**
- [ ] All customer reviews displayed
- [ ] Review details and dates shown
- [ ] Edit functionality available (within time limit)
- [ ] Delete option available (if policy allows)
- [ ] Changes update venue page immediately
- [ ] Edit history maintained (if implemented)

#### Test Case 4C.2.2: Review Flagging
**Objective:** Verify inappropriate reviews can be flagged
**Prerequisites:** Existing reviews on venue pages
**Test Data:**
- Flag Reason: "Inappropriate language"
- Additional Details: "Contains offensive content"

**Steps:**
1. Navigate to venue review page
2. Find review to flag
3. Click "Flag Review" option
4. Select flag reason
5. Submit flag report

**Expected Results:**
- [ ] Flag option available on all reviews
- [ ] Flag reasons provided for selection
- [ ] Additional details field available
- [ ] Flag report submitted successfully
- [ ] Admin receives flag notification
- [ ] Flagged review marked for moderation

### TEST SUITE 4C.3: Provider Review Response
**Priority: MEDIUM | Estimated Time: 30 minutes**

#### Test Case 4C.3.1: Provider Response to Reviews
**Objective:** Verify providers can respond to customer reviews
**Prerequisites:** Provider with customer reviews
**Test Data:**
- Response: "Thank you for the wonderful feedback! We're delighted you enjoyed your experience."

**Steps:**
1. Login as service provider
2. Navigate to venue reviews
3. Select review to respond to
4. Write response
5. Submit response

**Expected Results:**
- [ ] All venue reviews visible to provider
- [ ] Response option available for each review
- [ ] Response text field provided
- [ ] Response submission successful
- [ ] Response appears under original review
- [ ] Customer receives response notification
- [ ] Response timestamp displayed

#### Test Case 4C.3.2: Provider Review Analytics
**Objective:** Verify providers can view review analytics
**Prerequisites:** Provider with multiple reviews

**Steps:**
1. Navigate to provider review dashboard
2. View average rating
3. Check rating distribution
4. Review recent reviews
5. View review trends

**Expected Results:**
- [ ] Average rating calculated correctly
- [ ] Rating distribution chart/display
- [ ] Recent reviews prominently shown
- [ ] Review trends over time visible
- [ ] Total review count displayed
- [ ] Response rate shown (if applicable)

### TEST SUITE 4C.4: Review Moderation
**Priority: MEDIUM | Estimated Time: 25 minutes**

#### Test Case 4C.4.1: Admin Review Moderation
**Objective:** Verify admin can moderate flagged reviews
**Prerequisites:** Admin account and flagged reviews

**Steps:**
1. Login as admin
2. Navigate to review moderation queue
3. Review flagged content
4. Approve or remove flagged review
5. Provide moderation notes

**Expected Results:**
- [ ] Flagged reviews visible in moderation queue
- [ ] Flag reasons and details displayed
- [ ] Approve/remove options available
- [ ] Moderation notes field provided
- [ ] Actions update review status
- [ ] Flagging user receives resolution notification

#### Test Case 4C.4.2: Review Quality Control
**Objective:** Verify review quality standards maintained
**Prerequisites:** Various review submissions

**Steps:**
1. Review admin moderation tools
2. Check review quality filters
3. Test bulk moderation actions
4. Verify review guidelines enforcement

**Expected Results:**
- [ ] Quality control tools available
- [ ] Bulk actions for efficiency
- [ ] Review guidelines clearly defined
- [ ] Consistent moderation standards
- [ ] Appeal process available (if implemented)

---

## 📊 SECTION 4D: PART 4 TESTING SUMMARY

### Test Execution Checklist
**Total Test Cases: 28 | Estimated Time: 6.5 hours**

**Critical Tests (Must Pass 100%):**
- [ ] Add service to cart and checkout (4A.1.1, 4A.2.1)
- [ ] Real-time availability checking (4A.1.4)
- [ ] Payment processing (4B.1.1)
- [ ] Booking confirmation and notifications (4A.2.2)
- [ ] 6-hour cancellation window (4A.4.2)

**High Priority Tests (Must Pass 95%):**
- [ ] Cart management and expiration (4A.1.2, 4A.1.3)
- [ ] Provider booking management (4A.3.1-4A.3.3)
- [ ] Payment error handling (4B.1.2)
- [ ] Refund processing (4B.2.1-4B.2.3)
- [ ] Review submission and restrictions (4C.1.1-4C.1.2)

**Medium Priority Tests (Must Pass 90%):**
- [ ] Double booking prevention (4A.2.3)
- [ ] Payment receipts and history (4B.1.3)
- [ ] Provider earnings dashboard (4B.3.1-4B.3.2)
- [ ] Review management and flagging (4C.2.1-4C.2.2)
- [ ] Provider review responses (4C.3.1-4C.3.2)
- [ ] Review moderation (4C.4.1-4C.4.2)

### Integration Points to Validate
1. **Cart → Booking → Payment Flow**: Seamless progression through entire customer journey
2. **Booking → Notification System**: All parties receive appropriate notifications
3. **Payment → Provider Earnings**: Accurate commission calculations and payouts
4. **Booking Completion → Review Eligibility**: Reviews only available after service completion
5. **Discount Integration**: Discounts apply correctly in cart and payment

### Common Issues to Watch For
1. **Cart Expiration**: Ensure 24-hour expiration works correctly
2. **Real-Time Availability**: Verify slots update immediately across sessions
3. **Payment Security**: Ensure sensitive data handled securely
4. **Notification Delivery**: Check all email notifications are sent
5. **Review Timing**: Verify reviews only available after service completion
6. **Refund Processing**: Ensure refunds process correctly through payment system




===============================================================================
                    PART 5: ADMIN, DASHBOARD & INTEGRATION TESTING
                    (admin_app, dashboard_app, notifications_app, discount_app)
===============================================================================

## 🎛️ ADMIN, DASHBOARD & INTEGRATION OVERVIEW

This final section covers the administrative controls, user dashboards, notification system, and discount management that tie the entire CozyWish platform together. These apps provide the management tools, analytics, and promotional features essential for platform operation and business growth.

### 📋 PART 5 TESTING SCOPE

**Core Features to Test:**
- Admin platform management and user controls
- Customer and provider dashboard functionality
- Comprehensive notification system
- Discount creation and management
- Platform-wide analytics and reporting
- System health monitoring
- Cross-app integration workflows
- End-to-end user journey validation

**URL Patterns Covered:**
- /admin-panel/ - Custom admin dashboard and controls
- /dashboard/customer/ - Customer dashboard and analytics
- /dashboard/provider/ - Provider dashboard and business tools
- /dashboard/admin/ - Admin platform overview
- /notifications/ - Notification management system
- /discounts/ - Discount creation and discovery

**Integration Points to Validate:**
- Cross-app notification delivery
- Dashboard data aggregation from multiple apps
- Discount application in booking/payment flow
- Admin controls affecting all user types
- Real-time updates across platform

---

## 🧪 SECTION 5A: ADMIN PLATFORM MANAGEMENT TESTING

### TEST SUITE 5A.1: Admin Dashboard and Overview
**Priority: CRITICAL | Estimated Time: 45 minutes**

#### Test Case 5A.1.1: Admin Dashboard Access and Overview
**Objective:** Verify admin can access comprehensive platform overview
**Prerequisites:** Admin account with full permissions

**Steps:**
1. Login as admin user
2. Navigate to /admin-panel/
3. Review platform statistics dashboard
4. Check user registration trends
5. Verify booking volume metrics
6. Review revenue analytics

**Expected Results:**
- [ ] Admin dashboard loads correctly
- [ ] Platform statistics display accurately
- [ ] User metrics show current data
- [ ] Booking analytics are up-to-date
- [ ] Revenue tracking displays correctly
- [ ] Charts and graphs render properly
- [ ] Data refresh functionality works
- [ ] Export options available

#### Test Case 5A.1.2: User Management Controls
**Objective:** Verify admin can manage all user accounts
**Prerequisites:** Admin account and various user types in system
**Test Data:**
- Customer accounts to manage
- Provider accounts to review
- Deactivated accounts to reactivate

**Steps:**
1. Navigate to user management section
2. Search and filter users by type/status
3. View individual user details
4. Activate/deactivate user accounts
5. Reset user passwords
6. View user activity logs

**Expected Results:**
- [ ] All user accounts visible to admin
- [ ] Search and filter functions work correctly
- [ ] User details comprehensive and accurate
- [ ] Account activation/deactivation works
- [ ] Password reset functionality available
- [ ] User activity logs accessible
- [ ] Bulk actions available for efficiency
- [ ] All actions properly logged

#### Test Case 5A.1.3: System Health Monitoring
**Objective:** Verify admin can monitor system health and performance
**Prerequisites:** Admin account with system monitoring access

**Steps:**
1. Navigate to system health dashboard
2. Check application performance metrics
3. Review error logs and reports
4. Monitor database performance
5. Check email delivery status
6. Review security alerts

**Expected Results:**
- [ ] System health status clearly displayed
- [ ] Performance metrics accurate and current
- [ ] Error logs accessible and filterable
- [ ] Database performance indicators shown
- [ ] Email delivery statistics available
- [ ] Security alerts prominently displayed
- [ ] Critical issues highlighted
- [ ] Historical data available

### TEST SUITE 5A.2: Content and Platform Management
**Priority: HIGH | Estimated Time: 40 minutes**

#### Test Case 5A.2.1: Static Content Management
**Objective:** Verify admin can manage platform content
**Prerequisites:** Admin account with content management permissions
**Test Data:**
- About page content updates
- Terms & Conditions modifications
- FAQ additions

**Steps:**
1. Navigate to content management section
2. Edit static pages (About, Terms, etc.)
3. Create new FAQ entries
4. Update homepage content blocks
5. Manage site announcements
6. Publish/unpublish content

**Expected Results:**
- [ ] Content management interface intuitive
- [ ] Rich text editor available for content
- [ ] Changes save correctly
- [ ] Content preview functionality works
- [ ] Publish/unpublish controls function
- [ ] Content versioning available (if implemented)
- [ ] SEO settings accessible
- [ ] Changes reflect immediately on site

#### Test Case 5A.2.2: Media File Management
**Objective:** Verify admin can manage platform media assets
**Prerequisites:** Admin account with media management access

**Steps:**
1. Navigate to media management section
2. Upload new media files
3. Organize files into folders
4. Delete unused media files
5. Check file size and format restrictions
6. Verify media optimization

**Expected Results:**
- [ ] Media upload interface user-friendly
- [ ] File organization tools available
- [ ] File deletion with confirmation
- [ ] Size and format restrictions enforced
- [ ] Media optimization applied automatically
- [ ] Unused file detection available
- [ ] Bulk operations supported
- [ ] Media usage tracking shown

#### Test Case 5A.2.3: Announcement System
**Objective:** Verify admin can create and manage announcements
**Prerequisites:** Admin account
**Test Data:**
- Platform announcement: "New features coming soon!"
- Targeted announcement: "Special offer for spa services"

**Steps:**
1. Navigate to announcement creation
2. Create platform-wide announcement
3. Create targeted announcement for specific user groups
4. Schedule announcement publication
5. Monitor announcement engagement

**Expected Results:**
- [ ] Announcement creation interface clear
- [ ] Targeting options available (all users, customers, providers)
- [ ] Scheduling functionality works
- [ ] Announcements display correctly to users
- [ ] Engagement metrics tracked
- [ ] Announcement editing/deletion available
- [ ] Notification integration works

---

## 🧪 SECTION 5B: DASHBOARD FUNCTIONALITY TESTING

### TEST SUITE 5B.1: Customer Dashboard
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 5B.1.1: Customer Dashboard Overview
**Objective:** Verify customer dashboard displays relevant information
**Prerequisites:** Customer account with booking history
**Test Data:** Customer with past and upcoming bookings

**Steps:**
1. Login as customer
2. Navigate to /dashboard/customer/
3. Review upcoming bookings section
4. Check booking status updates
5. View favorite venues list
6. Review recent payment history

**Expected Results:**
- [ ] Dashboard loads quickly and displays clearly
- [ ] Upcoming bookings prominently shown
- [ ] Booking statuses accurate and current
- [ ] Favorite venues easily accessible
- [ ] Payment history comprehensive
- [ ] Quick actions available (book again, review)
- [ ] Personalized recommendations displayed
- [ ] Navigation intuitive and responsive

#### Test Case 5B.1.2: Customer Profile Management
**Objective:** Verify customers can manage profile from dashboard
**Prerequisites:** Customer account

**Steps:**
1. Access profile editing from dashboard
2. Update personal information
3. Change profile picture
4. Update notification preferences
5. Manage favorite venues list

**Expected Results:**
- [ ] Profile editing accessible from dashboard
- [ ] All profile fields editable
- [ ] Image upload works correctly
- [ ] Notification preferences save properly
- [ ] Favorite venues management functional
- [ ] Changes reflect immediately
- [ ] Success confirmations displayed

#### Test Case 5B.1.3: Customer Booking Management
**Objective:** Verify customers can manage bookings from dashboard
**Prerequisites:** Customer with various booking statuses

**Steps:**
1. View all bookings (past, upcoming, cancelled)
2. Filter bookings by status/date
3. Access booking details
4. Cancel eligible bookings
5. Submit reviews for completed services

**Expected Results:**
- [ ] All bookings visible with clear status
- [ ] Filtering options work correctly
- [ ] Booking details comprehensive
- [ ] Cancellation rules enforced properly
- [ ] Review submission available for completed bookings
- [ ] Booking history preserved
- [ ] Quick rebooking options available

### TEST SUITE 5B.2: Provider Dashboard
**Priority: HIGH | Estimated Time: 45 minutes**

#### Test Case 5B.2.1: Provider Dashboard Overview
**Objective:** Verify provider dashboard shows business metrics
**Prerequisites:** Provider account with business activity
**Test Data:** Provider with bookings, earnings, and reviews

**Steps:**
1. Login as service provider
2. Navigate to /dashboard/provider/
3. Review today's bookings summary
4. Check earnings overview
5. View recent reviews
6. Access venue performance metrics

**Expected Results:**
- [ ] Dashboard provides comprehensive business overview
- [ ] Today's bookings clearly displayed
- [ ] Earnings summary accurate and current
- [ ] Recent reviews prominently shown
- [ ] Performance metrics meaningful and actionable
- [ ] Quick access to management tools
- [ ] Data visualization clear and helpful
- [ ] Mobile-responsive design

#### Test Case 5B.2.2: Provider Business Analytics
**Objective:** Verify providers can access detailed business analytics
**Prerequisites:** Provider with historical business data

**Steps:**
1. Navigate to earnings reports section
2. View booking analytics by date range
3. Check service performance metrics
4. Review customer demographics
5. Export analytics reports

**Expected Results:**
- [ ] Earnings reports detailed and accurate
- [ ] Date range filtering works correctly
- [ ] Service performance data meaningful
- [ ] Customer insights valuable for business
- [ ] Export functionality works (PDF/CSV)
- [ ] Charts and graphs render correctly
- [ ] Historical data comparison available
- [ ] Trends and patterns highlighted

#### Test Case 5B.2.3: Provider Team Management
**Objective:** Verify providers can manage team from dashboard
**Prerequisites:** Provider account with team members

**Steps:**
1. Access team management from dashboard
2. View all team members
3. Add new team member
4. Edit existing team member information
5. Manage team member schedules/availability

**Expected Results:**
- [ ] Team management easily accessible
- [ ] All team members displayed clearly
- [ ] Adding team members straightforward
- [ ] Editing team information works correctly
- [ ] Schedule management functional
- [ ] Team member photos display properly
- [ ] Role and specialty information clear

### TEST SUITE 5B.3: Admin Dashboard
**Priority: CRITICAL | Estimated Time: 40 minutes**

#### Test Case 5B.3.1: Admin Platform Analytics
**Objective:** Verify admin dashboard provides comprehensive platform insights
**Prerequisites:** Admin account with platform data

**Steps:**
1. Navigate to /dashboard/admin/
2. Review user registration trends
3. Check booking volume analytics
4. Monitor revenue tracking
5. View platform growth metrics

**Expected Results:**
- [ ] Platform analytics comprehensive and accurate
- [ ] User growth trends clearly visualized
- [ ] Booking volume data current and detailed
- [ ] Revenue tracking includes all income streams
- [ ] Growth metrics actionable for business decisions
- [ ] Data export capabilities available
- [ ] Real-time updates functioning
- [ ] Historical comparison data available

#### Test Case 5B.3.2: Admin Operational Dashboard
**Objective:** Verify admin can monitor daily operations
**Prerequisites:** Admin account with operational data

**Steps:**
1. Check pending approvals queue
2. Review recent user activity
3. Monitor system alerts
4. Check support ticket status
5. Review content moderation queue

**Expected Results:**
- [ ] Pending approvals clearly listed
- [ ] User activity monitoring comprehensive
- [ ] System alerts prominently displayed
- [ ] Support ticket integration functional
- [ ] Content moderation queue accessible
- [ ] Priority items highlighted
- [ ] Quick action buttons available
- [ ] Status updates real-time

---

## 🧪 SECTION 5C: NOTIFICATION SYSTEM TESTING

### TEST SUITE 5C.1: Notification Delivery and Management
**Priority: CRITICAL | Estimated Time: 50 minutes**

#### Test Case 5C.1.1: Email Notification Delivery
**Objective:** Verify all email notifications are delivered correctly
**Prerequisites:** Various user accounts and triggering events
**Test Data:**
- New user registration
- Booking confirmation
- Payment receipt
- Review notification

**Steps:**
1. Trigger various notification events
2. Check email delivery for each event
3. Verify email content and formatting
4. Test email template rendering
5. Check delivery timing

**Expected Results:**
- [ ] All notification emails delivered promptly
- [ ] Email content accurate and well-formatted
- [ ] Templates render correctly across email clients
- [ ] Personalization data populated correctly
- [ ] Unsubscribe links functional
- [ ] Email delivery logs maintained
- [ ] Failed delivery handling works
- [ ] Bounce handling implemented

#### Test Case 5C.1.2: In-App Notification System
**Objective:** Verify in-app notifications work correctly
**Prerequisites:** User accounts with notification-triggering activities

**Steps:**
1. Generate various in-app notifications
2. Check notification display in navigation
3. Test notification dropdown functionality
4. Mark notifications as read/unread
5. Test notification detail pages

**Expected Results:**
- [ ] Notifications appear immediately in navigation
- [ ] Notification count badge updates correctly
- [ ] Dropdown displays recent notifications
- [ ] Read/unread status management works
- [ ] Notification details accessible
- [ ] Bulk actions available (mark all read)
- [ ] Notification history preserved
- [ ] Real-time updates functioning

#### Test Case 5C.1.3: Notification Preferences
**Objective:** Verify users can manage notification preferences
**Prerequisites:** User accounts with notification settings

**Steps:**
1. Navigate to notification preferences
2. Enable/disable email notifications
3. Configure in-app notification settings
4. Test preference enforcement
5. Verify preference persistence

**Expected Results:**
- [ ] Notification preferences easily accessible
- [ ] All notification types configurable
- [ ] Email/in-app preferences independent
- [ ] Preference changes save correctly
- [ ] Disabled notifications not sent
- [ ] Default preferences appropriate
- [ ] Preference changes immediate effect
- [ ] User guidance clear and helpful

### TEST SUITE 5C.2: Cross-App Notification Integration
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 5C.2.1: Booking-Related Notifications
**Objective:** Verify booking workflow triggers appropriate notifications
**Prerequisites:** Customer and provider accounts
**Test Data:** Complete booking workflow

**Steps:**
1. Create new booking as customer
2. Verify provider receives booking notification
3. Provider accepts/declines booking
4. Verify customer receives status notification
5. Complete booking and check completion notifications

**Expected Results:**
- [ ] New booking notifications sent to provider
- [ ] Booking status changes notify customer
- [ ] Completion notifications sent to both parties
- [ ] Cancellation notifications work correctly
- [ ] Reminder notifications sent appropriately
- [ ] All notifications contain relevant details
- [ ] Timing of notifications appropriate

#### Test Case 5C.2.2: Payment and Review Notifications
**Objective:** Verify payment and review events trigger notifications
**Prerequisites:** Completed bookings and payment processing

**Steps:**
1. Process payment for booking
2. Verify payment confirmation notifications
3. Submit review after service completion
4. Verify provider receives review notification
5. Provider responds to review
6. Verify customer receives response notification

**Expected Results:**
- [ ] Payment confirmations sent immediately
- [ ] Review notifications sent to providers
- [ ] Review response notifications sent to customers
- [ ] Refund notifications work correctly
- [ ] All financial notifications include transaction details
- [ ] Review notifications include review content

---

## 🧪 SECTION 5D: DISCOUNT SYSTEM TESTING

### TEST SUITE 5D.1: Discount Creation and Management
**Priority: MEDIUM | Estimated Time: 40 minutes**

#### Test Case 5D.1.1: Provider Discount Creation
**Objective:** Verify providers can create venue and service discounts
**Prerequisites:** Provider account with approved venue and services
**Test Data:**
- Venue discount: 20% off all services
- Service discount: $10 off massage therapy
- Time-limited discount: Weekend special

**Steps:**
1. Login as service provider
2. Navigate to discount creation
3. Create venue-wide discount
4. Create service-specific discount
5. Set discount validity periods
6. Submit discounts for approval

**Expected Results:**
- [ ] Discount creation interface intuitive
- [ ] Venue and service discount options available
- [ ] Percentage and fixed amount discounts supported
- [ ] Date/time restrictions configurable
- [ ] Usage limits settable
- [ ] Discounts require admin approval
- [ ] Provider receives submission confirmation

#### Test Case 5D.1.2: Admin Discount Approval
**Objective:** Verify admin can approve/reject provider discounts
**Prerequisites:** Admin account and pending discount requests
**Test Data:** Various discount requests for review

**Steps:**
1. Login as admin
2. Navigate to discount approval queue
3. Review discount details
4. Approve valid discounts
5. Reject inappropriate discounts with reasons
6. Monitor discount usage analytics

**Expected Results:**
- [ ] Pending discounts visible in admin queue
- [ ] Discount details comprehensive for review
- [ ] Approval/rejection actions work correctly
- [ ] Rejection reasons required and sent to provider
- [ ] Approved discounts become active immediately
- [ ] Usage analytics available for monitoring

#### Test Case 5D.1.3: Platform-Wide Discount Management
**Objective:** Verify admin can create platform-wide discounts
**Prerequisites:** Admin account
**Test Data:**
- Platform discount: "New Customer 15% Off"
- Category discount: "Spa Services 25% Off"
- Featured discount for homepage

**Steps:**
1. Create platform-wide discount campaign
2. Set category-specific discounts
3. Configure featured discounts for homepage
4. Set discount priority rules
5. Monitor platform discount performance

**Expected Results:**
- [ ] Platform discounts override venue discounts
- [ ] Category targeting works correctly
- [ ] Featured discounts display on homepage
- [ ] Discount priority rules enforced
- [ ] Performance analytics comprehensive
- [ ] Bulk discount management available

### TEST SUITE 5D.2: Discount Application and Usage
**Priority: HIGH | Estimated Time: 35 minutes**

#### Test Case 5D.2.1: Customer Discount Discovery
**Objective:** Verify customers can find and view available discounts
**Prerequisites:** Active discounts in system
**Test Data:** Various active discounts

**Steps:**
1. Navigate to featured discounts page
2. Search for discounts by category
3. View venue-specific discounts
4. Check discount details and terms
5. Verify discount expiration display

**Expected Results:**
- [ ] Featured discounts prominently displayed
- [ ] Discount search functionality works
- [ ] Venue discounts visible on venue pages
- [ ] Discount terms clearly stated
- [ ] Expiration dates prominently shown
- [ ] Discount categories well-organized
- [ ] Visual design attractive and clear

#### Test Case 5D.2.2: Discount Application in Booking Flow
**Objective:** Verify discounts apply correctly during booking
**Prerequisites:** Customer account and active discounts
**Test Data:**
- Service with applicable discount
- Booking meeting discount requirements

**Steps:**
1. Add discounted service to cart
2. Verify discount automatically applied
3. Check discount calculation accuracy
4. Complete booking with discount
5. Verify discount recorded in payment

**Expected Results:**
- [ ] Eligible discounts automatically applied
- [ ] Discount calculations accurate
- [ ] Multiple discount rules handled correctly
- [ ] Best discount automatically selected
- [ ] Discount details shown in cart/checkout
- [ ] Final payment reflects discount
- [ ] Discount usage recorded correctly

#### Test Case 5D.2.3: Discount Usage Analytics
**Objective:** Verify discount usage tracking and analytics
**Prerequisites:** Discounts with usage history

**Steps:**
1. Review provider discount analytics
2. Check admin platform discount performance
3. Verify usage limits enforcement
4. Monitor discount ROI metrics
5. Export discount usage reports

**Expected Results:**
- [ ] Provider can see their discount performance
- [ ] Admin has comprehensive discount analytics
- [ ] Usage limits properly enforced
- [ ] ROI calculations accurate and helpful
- [ ] Export functionality works correctly
- [ ] Historical data comparison available
- [ ] Trends and insights highlighted

---

## 🧪 SECTION 5E: END-TO-END INTEGRATION TESTING

### TEST SUITE 5E.1: Complete User Journey Testing
**Priority: CRITICAL | Estimated Time: 60 minutes**

#### Test Case 5E.1.1: Complete Customer Journey
**Objective:** Verify entire customer experience works seamlessly
**Prerequisites:** Clean test environment with all apps functional
**Test Data:** New customer account and available services

**Steps:**
1. Customer registration and profile setup
2. Venue search and service discovery
3. Service selection and cart management
4. Booking creation and confirmation
5. Payment processing and receipt
6. Service completion and review submission
7. Dashboard review of entire experience

**Expected Results:**
- [ ] Registration process smooth and complete
- [ ] Search functionality returns relevant results
- [ ] Cart management intuitive and reliable
- [ ] Booking process clear and confirmatory
- [ ] Payment processing secure and successful
- [ ] Review submission straightforward
- [ ] Dashboard provides comprehensive history
- [ ] All notifications delivered appropriately

#### Test Case 5E.1.2: Complete Provider Journey
**Objective:** Verify entire provider experience works seamlessly
**Prerequisites:** Clean test environment
**Test Data:** New provider account

**Steps:**
1. Provider registration and email verification
2. Venue creation and submission for approval
3. Admin venue approval process
4. Service creation and availability setup
5. Booking reception and management
6. Service delivery and completion
7. Earnings tracking and analytics review
8. Review response and customer interaction

**Expected Results:**
- [ ] Registration and verification smooth
- [ ] Venue creation process intuitive
- [ ] Approval workflow functions correctly
- [ ] Service management comprehensive
- [ ] Booking management efficient
- [ ] Earnings tracking accurate
- [ ] Review system facilitates good customer relations
- [ ] Dashboard provides valuable business insights

#### Test Case 5E.1.3: Complete Admin Management Journey
**Objective:** Verify admin can effectively manage entire platform
**Prerequisites:** Platform with various user activities
**Test Data:** Multiple users, venues, bookings, and content

**Steps:**
1. Admin dashboard overview and monitoring
2. User account management and support
3. Venue approval and content moderation
4. Discount approval and campaign management
5. System health monitoring and maintenance
6. Analytics review and business insights
7. Platform content and announcement management

**Expected Results:**
- [ ] Admin dashboard provides comprehensive oversight
- [ ] User management tools effective and efficient
- [ ] Content moderation workflow smooth
- [ ] Discount management facilitates business growth
- [ ] System monitoring prevents issues
- [ ] Analytics support business decisions
- [ ] Platform management tools comprehensive

### TEST SUITE 5E.2: Cross-Browser and Device Testing
**Priority: HIGH | Estimated Time: 45 minutes**

#### Test Case 5E.2.1: Cross-Browser Compatibility
**Objective:** Verify platform works across different browsers
**Prerequisites:** Access to multiple browsers
**Test Data:** Standard user workflows

**Steps:**
1. Test core functionality in Chrome
2. Test core functionality in Firefox
3. Test core functionality in Safari
4. Test core functionality in Edge
5. Compare performance and appearance
6. Verify JavaScript functionality across browsers

**Expected Results:**
- [ ] All browsers display content correctly
- [ ] JavaScript functionality consistent
- [ ] CSS styling renders properly
- [ ] Form submissions work in all browsers
- [ ] Image uploads function correctly
- [ ] Payment processing works across browsers
- [ ] Performance acceptable in all browsers

#### Test Case 5E.2.2: Mobile Responsiveness
**Objective:** Verify platform is fully functional on mobile devices
**Prerequisites:** Mobile devices or browser developer tools
**Test Data:** Mobile user workflows

**Steps:**
1. Test registration/login on mobile
2. Test venue search and booking on mobile
3. Test payment processing on mobile
4. Test dashboard functionality on mobile
5. Test image uploads on mobile
6. Verify touch interactions work correctly

**Expected Results:**
- [ ] All pages responsive and mobile-friendly
- [ ] Navigation intuitive on mobile
- [ ] Forms easy to complete on mobile
- [ ] Images and media display correctly
- [ ] Touch interactions work smoothly
- [ ] Performance acceptable on mobile
- [ ] All functionality available on mobile

### TEST SUITE 5E.3: Performance and Load Testing
**Priority: MEDIUM | Estimated Time: 30 minutes**

#### Test Case 5E.3.1: Page Load Performance
**Objective:** Verify acceptable page load times across platform
**Prerequisites:** Production-like environment

**Steps:**
1. Measure homepage load time
2. Test venue search performance
3. Check dashboard load times
4. Test image-heavy pages
5. Verify database query performance
6. Check mobile performance

**Expected Results:**
- [ ] Homepage loads within 3 seconds
- [ ] Search results appear within 2 seconds
- [ ] Dashboard loads within 4 seconds
- [ ] Image galleries load progressively
- [ ] Database queries optimized
- [ ] Mobile performance acceptable

#### Test Case 5E.3.2: Concurrent User Testing
**Objective:** Verify platform handles multiple simultaneous users
**Prerequisites:** Multiple test accounts

**Steps:**
1. Simulate multiple users browsing simultaneously
2. Test concurrent booking attempts
3. Check real-time availability updates
4. Test notification delivery under load
5. Monitor system resource usage

**Expected Results:**
- [ ] Platform remains responsive under load
- [ ] Booking conflicts handled correctly
- [ ] Availability updates remain accurate
- [ ] Notifications delivered reliably
- [ ] System resources within acceptable limits

---

## 📊 SECTION 5F: FINAL TESTING SUMMARY AND SIGN-OFF

### Complete Testing Checklist
**Total Test Cases Across All Parts: 103 | Total Estimated Time: 28 hours**

**Part 1 - Foundation**: Testing framework and methodology ✅
**Part 2 - Authentication**: 25 test cases, 5.5 hours ✅
**Part 3 - Venue Management**: 22 test cases, 5.5 hours ✅
**Part 4 - Booking/Payment/Review**: 28 test cases, 6.5 hours ✅
**Part 5 - Admin/Dashboard/Integration**: 28 test cases, 10.5 hours ✅

### Critical Success Criteria Final Validation
**Must Pass 100% (Critical Priority):**
- [ ] All authentication flows functional
- [ ] Venue creation and approval workflow
- [ ] Booking and payment processing
- [ ] Admin platform management
- [ ] Cross-app integration points
- [ ] Security and access controls
- [ ] Email notification delivery
- [ ] Mobile responsiveness

### Business Constraint Validation
- [ ] Single venue per provider enforced
- [ ] Maximum 7 services per venue
- [ ] 24-hour cart expiration
- [ ] 6-hour booking cancellation window
- [ ] Provider capacity limits (10 concurrent)
- [ ] Image upload limits (5MB)
- [ ] Review submission after service completion
- [ ] Venue approval before public visibility

### Platform Readiness Assessment
**Technical Readiness:**
- [ ] All core functionality tested and working
- [ ] Cross-browser compatibility verified
- [ ] Mobile responsiveness confirmed
- [ ] Performance benchmarks met
- [ ] Security measures validated
- [ ] Integration points functioning

**Business Readiness:**
- [ ] All user workflows complete and intuitive
- [ ] Revenue generation systems functional
- [ ] Customer experience optimized
- [ ] Provider tools comprehensive
- [ ] Admin controls effective
- [ ] Analytics and reporting available

### Final Sign-Off Requirements

**Development Team Sign-Off:**
- [ ] All critical and high priority tests passed
- [ ] Known issues documented and prioritized
- [ ] Performance benchmarks achieved
- [ ] Security review completed

**Business Team Sign-Off:**
- [ ] User experience meets requirements
- [ ] Business workflows validated
- [ ] Revenue systems functional
- [ ] Marketing integration ready

**Quality Assurance Sign-Off:**
- [ ] Test coverage complete
- [ ] Issue tracking up to date
- [ ] Regression testing completed
- [ ] Documentation finalized


### Post-Launch Monitoring Plan
**Week 1 - Critical Monitoring:**
- Daily user registration and activation rates
- Booking completion rates and payment success
- System performance and error rates
- Customer support ticket volume and types

**Week 2-4 - Performance Optimization:**
- User behavior analytics and conversion funnels
- Provider onboarding success rates
- Revenue generation and commission tracking
- Feature usage analytics and optimization opportunities

**Month 2+ - Growth and Enhancement:**
- Customer retention and repeat booking rates
- Provider satisfaction and platform growth
- Market expansion opportunities
- Feature enhancement prioritization

### Emergency Response Plan
**Critical Issues (Platform Down):**
- Immediate notification to development team
- Status page updates for users
- Rollback procedures if necessary
- Communication plan for stakeholders

**High Priority Issues (Feature Broken):**
- Issue triage within 2 hours
- Fix deployment within 24 hours
- User communication if customer-facing
- Post-mortem analysis and prevention

### Success Metrics for Launch
**Month 1 Targets:**
- 100+ customer registrations
- 25+ provider registrations with approved venues
- 50+ completed bookings
- 95%+ payment success rate
- <2% customer support ticket rate

**Month 3 Targets:**
- 500+ active customers
- 100+ active providers
- 500+ monthly bookings
- $10,000+ monthly gross revenue
- 4.5+ average customer rating

===============================================================================
                    🎉 COZYWISH MANUAL TESTING CHECKLIST COMPLETE 🎉
===============================================================================
