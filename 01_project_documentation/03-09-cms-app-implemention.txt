===== CMS_APP IMPLEMENTING =====


===== 01. MODELS =====
1. StaticPage
	Title (e.g., About, Terms, Privacy Policy)
	Rich Content (Images, Video Embeds, Rich Text)
	Auto-generated SEO Metadata
	Slug (Auto-generated URL)
	Published Status

2. BlogCategory
	Name (flat/simple structure)

3. BlogPost
	Title
	Category (linked to BlogCategory)
	Content (Rich text with media support)
	Featured Image (S3)
	Auto-generated SEO metadata
	Slug (Auto-generated URL)
	Published Date
	Status (Draft/Published)
	
4. HomepageBlock
	Pre-defined sections (Title, subtitle, image, button/link) editable by admin
	
5. MediaFile
	File (stored on AWS S3)
	Uploaded Date
	File Type


===== 02. VIEWS and FUNCTIONALITIES ===== 
Static Pages:
	Rich content management (images, embedded videos, formatted text).
	Simple publish/unpublish toggle.
	
Blog Management:
	Simple flat structure categories.
	Post management with images and rich text editing.

Homepage Management:
	Editable pre-defined sections (weekly/monthly updates).
	Easy hero section editing (text, images, call-to-action buttons).
	
SEO & URLs:
	Auto-generated SEO metadata (titles, descriptions).
	Auto-generated slugs for all pages/posts.


	

===== 03. URL STRUCTURE =====  


===== 04. LIST OF PAGES =====

===== 05. IMPLEMENTATION NOTES =====







===== 06. TASK LIST =====
- Rich static page management
- Blog creation/editing system
- Homepage editing functionality
- Media upload and management

===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====

Creating/editing/publishing pages and blogs, homepage edits, SEO metadata auto-generation.


===== 08. KEY PAGES HTML PAGE CONTENT =====



===== 09. SCREEN STATES =====

