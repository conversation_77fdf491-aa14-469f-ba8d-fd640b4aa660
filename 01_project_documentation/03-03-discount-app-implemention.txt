===== DISCOUNT_APP IMPLEMENTING =====


===== 01. MODELS =====
1. Discount Model (Service Provider)
	Provider (linked to user/venue)
	Service (linked to venue service)
	Original Price (from service)
	Discounted Price (fixed amount entered by provider)
	Percentage Off (calculated automatically from price entered)
	Active Status (Active / Inactive toggle)
	Created Date

2. PlatformDiscount Model (Admin-created)
	Title (e.g., "Summer Special", "First Booking Discount")
	Discount Percentage (e.g., 10%)
	Start Date & End Date
	Active Status (Active / Inactive toggle)

3. DiscountUsage Model
	Linked Discount (Provider or Platform-wide)
	Booking Reference
	Usage Date




===== 02. VIEWS and FUNCTIONALITIES ===== 
1. Customer Features
	- View Discounts: Clearly shown in search results & venue detail pages.
	- Discount Application: Both provider and platform discounts automatically applied at checkout.

2. Service Provider Features
	- Create Discounts: Fixed amount entered, percentage auto-calculated, max 80%.
	- Manage Discounts: Enable/disable discounts easily.

3. Admin Features
	- Manage Platform Discounts: Create/edit platform-wide discounts.
	- Change Max Discount Limit: Adjust max discount percentage (currently 80%).
	- Discount Analytics: Track number of uses, revenue generated, popular discounts (usage frequency).




===== 03. URL STRUCTURE =====  
/provider/discounts/create/
/provider/discounts/manage/
/admin/discounts/manage/


===== 06. TASK LIST =====
- Create all discount models & relationships.
- Implement percentage calculation logic for provider discounts.
- Create provider views/forms for managing discounts.
- Display discounts clearly on venue/service views.
- Automatically apply both discounts at checkout.
- Implement admin views/forms for managing platform discounts and settings.
- Analytics tracking and reporting views.



===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
Provider creating discounts, auto-percentage calculation
Discounts appearing correctly in venue/service views
Discounts applying correctly at checkout



===== 08. KEY PAGES HTML PAGE CONTENT =====


===== 09. SCREEN STATES =====

