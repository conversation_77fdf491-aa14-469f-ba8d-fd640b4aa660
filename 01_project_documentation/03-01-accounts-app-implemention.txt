===== ACCOUNTS_APP IMPLEMENTING =====


===== 01. MODELS =====
1. User Model (Custom User Model)
	- Email (used as username)
	- Password
	- Role (choices: Customer, Service Provider, Admin)
	- Is Active (Boolean, default True)
	- Date Joined
	- Last Login
	- Login History (store login timestamps, IP, user agent, purged after 90 days)

2. Customer Profile Model (Relationship: linked to User)
	- First Name
	- Last Name
	-Phone Number
	- Profile Picture (stored in S3)
	- Gender (choices: Male, Female, Other)
	- Date of Birth (Month, Year only)
	- Default Address
	- City
	- ZIP Code

3. Service Provider Profile Model (Relationship: linked to User)
	- Business Name
	- DBA Name (Doing Business As)
	- Business Description (short text)
	- Business Address (Street, City, State, ZIP)
	- Business Phone Number
	- EIN (Tax ID)
	- Business Email (can differ from user login email)
	- Website URL
	- Social Media Links (Instagram, Facebook URLs)
	- Profile Picture / Logo (stored in S3)
	- State
	- County
	-  Visibility Toggle (Active / Inactive)
	
4. Team Member Model (Relationship: linked to Service Provider Profile)
	- Staff Name
	- Staff profile picture (stored in S3)
	- Staff Position
	- Active/Inactive toggle
	- Created Date





===== 02. VIEWS ===== 
1. Customer Views:
	- Signup: Email/password form, direct login without email verification.
	- Login/Logout: Session-based with separate URL from providers.
	- Google/Apple Login: Merge accounts automatically by email.
	- Password Reset: Email-based reset flow (via SendGrid).
	- Profile Management: Edit profile fields from dashboard.
	- Deactivate/Delete Account: Immediate account deactivation, retaining data.

2. Service Provider Views:
	- Signup: Email, password, detailed business information, mandatory email verification.
	- Login/Logout: Session-based with separate URL from customers.
	- Password Reset: Email-based reset flow (via SendGrid).
	- Profile Management: Edit all listed business-related fields.
	- Deactivate/Delete Account: Immediate account deactivation, retaining data.
	- Manage Team/Staff: Add, remove, activate/deactivate up to 7 team members.

3. Admin Views (Django Admin Panel):
	- User Management: View/edit/delete/reset passwords, manual password reset triggering email.
	- Activate/Deactivate Users: Manage active status.
	- Login History: View records up to 90 days.
	- Login Alerts: Notify upon multiple failed login attempts.





===== 03. URL STRUCTURE =====  
/customer/signup/ (Customer signup)
/provider/signup/ (Provider signup)
/customer/login/ (Customer login)
/provider/login/ (Provider login)
/logout/ (Universal logout URL)
/password-reset/ (Password reset request)
/profile/customer/ (Customer profile management)
/profile/provider/ (Provider profile management)





===== 04. LIST OF PAGES =====
1. Customer Pages:
	- Signup Page
	- Login Page
	- Password Reset Pages (request, confirmation)
	- Profile Dashboard (view/edit profile, change password, deactivate/delete account)

2. Service Provider Pages:
	- Signup & Email Verification Pages
	- Login Page
	- Password Reset Pages (request, confirmation)
	- Provider Profile Dashboard (edit profile details, manage team members, change password, deactivate/delete account)






===== 05. IMPLEMENTATION NOTES =====
User Model: Custom user model using email as primary identifier for login (no username).
Password Management: Secure password hashing managed by Django default authentication system.
Email Verification (Providers only): Verification emails sent via SendGrid.
Social Login (Customer only): Implement OAuth integration for Google and Apple, account merging logic based on user email.
Storage: Store profile images and logos in AWS S3 bucket, organized by user type (customers/providers).





===== 06. TASK LIST =====
1. Preparation
	 - Setup Django custom user model
	 - Configure email backend with SendGrid
	 - Setup AWS S3 storage for media uploads

2. Customer Features
	- Implement customer signup/login/logout/password reset
	- Integrate Google and Apple OAuth
	- Profile management views and forms
	- Deactivation functionality

3. Service Provider Features
	- Provider signup with business details and email verification
	- Implement team member management (max 7)
	- Profile management views and forms
	- Deactivation functionality
	
4. Admin Features
	 - Configure Django Admin for user management
	 - Implement alerts for unusual logins (multiple failures)
	 - Implement login history tracking and purging mechanism

5. Testing & Verification
	 - Write unit tests for user models and authentication flows
	 - Integration tests for signup/login/password reset workflows
	 - Manual smoke test checklist preparation
	



===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
- Customer Signup/Login/Logout
- Customer Password Reset
- Customer Google/Apple login merging
- Customer profile edits
- Customer account deactivation
- Provider Signup and email verification
- Provider profile editing
- Provider team management (add/remove/deactivate)
- Provider account deactivation
- Admin user activation/deactivation/reset password
- Admin alerts on multiple failed logins
- Login history tracking and expiry (90-day limit)
	



===== 08. KEY PAGES HTML PAGE CONTENT =====
- Signup/Login Pages: Forms capturing email/password and necessary business or personal details, error messaging clearly presented inline.
- Profile Management Pages: Forms pre-populated with existing data, clearly labeled fields, submission buttons, and confirmation messages.
- Email Templates: Simple branded templates for password resets, email verification, and notifications.




===== 09. CLARIFICATIONS|ASSUMPTIONS =====
- Profile images/logo stored in single AWS S3 bucket under structured sub-folders.
- Manual admin password resets trigger standard SendGrid emails.
- Data retained in DB upon account deactivation for potential recovery or compliance reasons.



===== 10. SCREEN STATES =====

1. Customer Signup Page
	State 1:
	Blank form, fields: Email, Password, Confirm Password.
	Visible signup button.

	State 2 (Validation Errors):
	Inline messages: "Email already exists," "Password too weak," "Passwords don't match."
	Highlight incorrect fields.

	State 3 (Successful Signup):
	Redirect immediately to Customer Dashboard.
	Brief toast notification: "Welcome to CozyWish!"


2. Customer Login Page
	State 1:
	Blank login form: Email, Password fields.
	Links: "Forgot Password?" and "Signup here."
	Google and Apple login buttons.

	State 2 (Validation Errors):
	Inline error: "Invalid email/password."
	Highlight incorrect fields.

	State 3 (Successful Login):
	Redirect immediately to Customer Dashboard.
	Toast message: "Successfully logged in!"
	
	
3. Customer Password Reset Page
Request Reset Page
	State 1:
	Blank email input field with "Submit" button.

	State 2 (Validation Errors):
	Inline message: "Email not found."
	Highlight email field.

	State 3 (Successful Submission):
	Message displayed: "Check your email to reset your password."


4. Reset Link Page (from email link)
	State 1:
	Blank form with "New Password," "Confirm Password."

	State 2 (Validation Errors):
	Inline messages: "Passwords don't match," "Password too weak."

	State 3 (Successful Reset):
	Redirect immediately to login page with message: "Your password has been reset."


5. Customer Profile Dashboard Page
	State 1:
	View mode: display profile details (name, email, phone, gender, date of birth, address, picture).
	"Edit Profile" and "Change Password" buttons visible.

	State 2 (Edit Profile):
	Editable form with current data pre-filled.

	State 3 (Validation Errors):
	Inline messages for incorrect entries (e.g., invalid phone number format).

	State 4 (Successful Update):
	Message: "Profile updated successfully."
	Returns to view mode.


6. Customer Change Password Page
	State 1:
	Form fields: "Current Password," "New Password," "Confirm New Password."

	State 2 (Validation Errors):
	Inline messages: "Incorrect current password," "Passwords don't match," "Password too weak."

	State 3 (Successful Change):
	Toast message: "Password updated successfully."


7. Customer Deactivate/Delete Account Page
	State 1:
	Button "Deactivate Account."
	Warning text: "Your data will remain stored securely."

	State 2 (Confirmation Prompt):
	Confirm modal: "Are you sure you want to deactivate your account?"

	State 3 (Successful Deactivation):
	Logout automatically.
	Redirect to homepage with message: "Your account has been deactivated."


8. Service Provider Signup Page
	State 1:
	Blank form fields: Email, Password, Confirm Password, Business Name, Phone, Email, Contact Name, EIN, Website, State, County.

	State 2 (Validation Errors):
	Inline messages for missing or incorrect fields.

	State 3 (Successful Submission):
	Display message: "Check your email for the verification link."


9, Service Provider Email Verification Page
	State 1 (Verification Link Clicked):
	Loading spinner with text: "Verifying your email..."

	State 2 (Verification Success):
	Message: "Your email is successfully verified."
	Redirect to login.

	State 3 (Verification Failed):
	Error message: "Invalid or expired verification link."
	Option to resend verification email.



10. Service Provider Login Page
	State 1:
	Form fields: Email, Password.
	Link: "Forgot Password?"

	State 2 (Validation Errors):
	Inline error message: "Invalid email/password."

	State 3 (Login Success):
	Redirect to Service Provider Dashboard.
	Toast message: "Successfully logged in!"


11. Service Provider Password Reset Page (Same states as Customer Password Reset page, adjusted URL for providers.)


12. Service Provider Profile Dashboard Page
	State 1:
	View mode displaying current business profile details.

	State 2 (Edit Profile Mode):
	Editable form pre-filled with existing data:
	Business Name, DBA Name, Description, Address, Phone, Website URL, Social Media, Logo, Visibility toggle.

	State 3 (Validation Errors):
	Inline messages on incorrect entries.

	State 4 (Successful Update):
	Message: "Business profile updated successfully."


13. Service Provider Team Management Page
	State 1:
	List current team members (max 7).
	Button: "Add Team Member."

	State 2 (Adding Member Form):
	Blank fields: Name, Position.

	State 3 (Validation Errors):
	Inline messages: "Maximum team members reached," "Required fields missing."

	State 4 (Successful Addition/Removal/Update):
	Toast messages: "Member added successfully," "Member removed," "Status updated."



14. Service Provider Change Password Page (Same states as Customer Change Password Page.)


15. Service Provider Deactivate/Delete Account Page (Same states as Customer Deactivate/Delete Account Page.)


16. Admin (Django Admin) User Management
	State 1 (View All Users):
	Tabular view listing customers/providers with search/filter options.

	State 2 (User Detail View):
	Detailed view/edit form: Email, Name, Role, Status (active/inactive).

	State 3 (Password Reset Triggered):
	Confirmation prompt: "Reset password and send email notification?"

	State 4 (User Activation/Deactivation):
	Confirmation prompt: "Activate/Deactivate user account?"



17. Login Alerts (Admin notification)
	State 1 (Multiple Failed Logins):
	Admin receives notification via dashboard/email:
	"Multiple failed login attempts detected for user: [Email]."


18. General Error Pages
	404 Error (Page Not Found):
	Message: "Oops! The page you're looking for doesn't exist."
	Button: "Back to Home."


19. 500 Error (Server Issue):
	Message: "Something went wrong! Our team has been notified."
	Button: "Back to Home."

