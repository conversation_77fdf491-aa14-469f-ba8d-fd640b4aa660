===== PAYMENTS_APP IMPLEMENTING =====

===== 01. MODELS =====
1. Payment Model:
	Booking (Foreign Key)
	Customer (linked to user)
	Provider (linked to provider/venue)
	Amount Paid
	Payment Method (via Stripe)
	Stripe Transaction ID
	Payment Status (Succeeded, Failed, Refunded)
	Date & Time
	
2. RefundRequest Model
	Payment (linked to Payment)
	Customer (linked to user)
	Reason (short text)
	Request Status (Pending, Approved, Declined)
	Admin Notes
	Created At (Timestamp)



===== 02. VIEWS and FUNCTIONALITIES ===== 
1. Customer Features:
- Stripe Payments: Secure checkout via Stripe (card payment).
- Save Payment Method: Securely store payment method in Stripe for easy checkout.
- Total Pricing Transparency: Clearly breakdown service price, discounts, taxes, service fees, platform commissions.
- Payment History: View payment receipts and past transactions.
- Refund Requests: Submit refund request with reason; manual admin review.

2. Service Provider Features
- Daily Payouts (Stripe): Automatically daily via Stripe, after deducting platform commissions.
- Payment Overview: View daily/weekly/monthly earnings.
- Refund Management: Review refund status (handled manually by admin).

3. Admin Features
- Transaction Monitoring: View detailed transactions history, success/failure tracking.
- Refund Handling: Approve or decline refun ds manually.
- Payment Analytics: Common e-commerce metrics: daily/monthly revenue, refunds, transactions counts, provider earnings.
- Disputed Payments: Notifications for failed/disputed payments; manual resolution.

4. Notifications (Email & Dashboard)
Customer:
	Payment successful receipt
	Refund request received, approved, declined

Service Provider
	Payment received notification
	Refund processed notifications

Admin: Immediate alert for failed or disputed payments
	

===== 03. URL STRUCTURE =====  
/checkout/payment/ (secure Stripe checkout page)
/payments/history/ (customer payments page)
/payments/refunds/request/ (customer refund request form)
/provider/payments/earnings/ (provider payout details)



===== 06. TASK LIST =====
- Implement Stripe payment integration (including secure card storage).
- Display pricing breakdown transparently at checkout.
- Automatic daily Stripe payouts to providers (less commissions).
- Payment success/failure handling, detailed reporting, notifications.
- Refund request submission, admin review, processing logic.
- Payment analytics and reporting.



===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
Stripe payment processing, storing cards
Transparent pricing at checkout
Refund request and admin handling
Daily provider payouts via Stripe
Admin analytics for payments/refunds


===== 08. KEY PAGES HTML PAGE CONTENT =====



===== 09. SCREEN STATES =====

