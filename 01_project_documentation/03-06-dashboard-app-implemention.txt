===== DASHBOARD_APP IMPLEMENTING =====


===== 01. MODELS =====
1. FavoriteVenue
	Customer (linked to User)
	Venue (linked to Venue)
	Added Date

===== 02. VIEWS and FUNCTIONALITIES ===== 
Customer Dashboard:
	Bookings: List past and upcoming bookings clearly with status indicators.
	Favorites:
		Simple favorites list (add/remove venues easily).
		Receive notifications for discounts/promotions on favorite venues.
	Profile Management: Quick access/editing of personal profile details.

Service Provider Dashboard:
	Today's Bookings: Display bookings clearly organized by service and time slots.
	Earnings Reports: Daily, weekly, monthly earnings summaries with simple visualizations.
	Best-Performing Services: Metrics based on revenue, booking counts, and average reviews.
	Discount Usage Report: Detailed usage logs (number of uses, revenue generated per discount).
	Team Management: Quick links to manage/add/remove staff members.

Admin Dashboard:
	Platform Activity (Detailed Analytics):
		Detailed summaries (total bookings, new users, revenues).
		Rich statistics (signups, active users, popular venues).
	System Status: Track uptime, average load times, user session durations, and performance analytics.







===== 03. URL STRUCTURE =====  


===== 04. LIST OF PAGES =====

===== 05. IMPLEMENTATION NOTES =====







===== 06. TASK LIST =====
- Customer favorites functionality
- Booking and profile management integration
- Service provider booking overview & earnings tracking
- Admin detailed analytics and system performance monitoring

===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
Customer/provider/admin dashboards overview and functionality tests.


===== 08. KEY PAGES HTML PAGE CONTENT =====



===== 09. SCREEN STATES =====

