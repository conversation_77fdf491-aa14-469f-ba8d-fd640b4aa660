===== REVIEW_APP IMPLEMENTING =====


===== 01. MODELS =====
1. Review
	Customer (linked to User)
	Venue (linked to Venue)
	Rating (1–5 stars)
	Written Review (text)
	Date Posted

2. ReviewResponse
	Provider (linked to Provider/User)
	Review (linked to Review)
	Response Text (publicly visible)
	Response Date

3. ReviewFlag
	Review (linked to Review)
	Flagged By (Customer reference)
	Reason (text)
	Status (Pending / Reviewed)


===== 02. VIEWS and FUNCTIONALITIES ===== 
Customer:
	Prompted via email automatically after service completion.
	Star rating (1–5), written review submission.
	View reviews on provider profiles (recent-first sorting).
	Flag inappropriate/fake reviews (stay visible until reviewed).

Service Provider:
	View all reviews with average rating and count.
	Immediate public response to reviews (no admin approval required).
	Receive notification on new reviews.

Admin:
	Moderate (edit/remove) reviews.
	Manage flagged reviews without automatic hiding.
	Highlight "New on CozyWish" providers (<5 reviews).



===== 03. URL STRUCTURE =====  


===== 04. LIST OF PAGES =====

===== 05. IMPLEMENTATION NOTES =====







===== 06. TASK LIST =====
- Review & response management system
- Auto-prompt reviews after service completion
- Admin review moderation features
- Flagging mechanism implementation

===== 07. HIGH LEVEL MANUAL TESTING CHECKLIST =====
Customer review posting, provider responses, admin moderation workflows.


===== 08. KEY PAGES HTML PAGE CONTENT =====



===== 09. SCREEN STATES =====

