/* ===============================
   CozyWish Main Styles (main.css)
   Common, Brand, Global Styles
   =============================== */

/* Brand Colors & Variables */
:root {
    --primary: #43251B;
    --primary-rgb: 67, 37, 27;
    --secondary: #5A342A;
    --secondary-rgb: 90, 52, 42;
    --body-bg: #F8F9FA;
    --light: #FEF6F0;
    --body-color: #222222;
    --heading-color: #43251B;
  
    --font-family-sans: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    --font-weight-normal: 500;
    --font-weight-bold: 700;
    --font-weight-bolder: 800;
    --border-color: #E0DFDE;
  }
  
  /* Global Font and Body */
  body {
    font-family: var(--font-family-sans);
    font-weight: var(--font-weight-normal);
    background: var(--body-bg);
    color: var(--body-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bolder);
    color: var(--heading-color);
  }
  
  /* Links */
  a {
    color: var(--primary);
    font-weight: var(--font-weight-bold);
    text-decoration: none;
  }
  a:hover { color: var(--secondary); text-decoration: underline; }
  
  /* Buttons (Generic) */
  .btn {
    border-width: 2px;
    border-style: solid;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.1);
  }
  .btn-primary, .btn-success, .btn-danger, .btn-dark {
    background: var(--primary);
    border-color: var(--primary);
    color: #fff;
  }
  .btn-primary:hover, .btn-success:hover, .btn-danger:hover, .btn-dark:hover {
    background: var(--secondary);
    border-color: var(--secondary);
  }
  .btn-outline-secondary {
    border-color: var(--secondary);
    color: var(--secondary);
    background: transparent;
  }
  .btn-outline-secondary:hover {
    background: var(--secondary);
    color: #fff;
  }
  
  /* Card (Generic) */
  .card {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 10px;
  }
  
  /* Forms */
  .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.15);
  }
  label, .form-label {
    font-weight: var(--font-weight-bold);
  }
  
  /* Font Awesome compatibility */
  .fas, .fab, .far, .fal {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 6 Pro";
    font-weight: 900;
  }
  .fab { font-weight: 400; }
  
  /* Utilities */
  .bg-white { background: #fff !important; }
  .text-primary { color: var(--primary) !important; }
  .text-secondary { color: var(--secondary) !important; }
  .border-primary { border-color: var(--primary) !important; }
  .border-secondary { border-color: var(--secondary) !important; }
  .rounded { border-radius: 10px !important; }

.btn-outline-dark:focus,
.btn-outline-dark:active,
.btn-outline-dark:hover {
  background: #5A342A !important;
  border-color: #5A342A !important;
  color: #fff !important;
}

/* For Bootstrap 5 checkboxes */
.form-check-input:checked {
  background-color: #5A342A !important;
  border-color: #5A342A !important;
}

/* For focus ring (when tabbed/focused) */
.form-check-input:focus {
  box-shadow: 0 0 0 0.15rem rgba(90, 52, 42, 0.25) !important;
  border-color: #5A342A !important;
}

/* ==========================
   Global Badge & Dropdown Styles
   ========================== */

/* Badge styling */
.badge { 
    font-size: 0.7rem; 
    padding: 0.2em 0.5em; 
    font-weight: 600; 
}

/* Dropdown divider styling */
.dropdown-divider { 
    border-color: #E0DFDE; 
    margin: 0.75rem 0; 
}

/* ==========================
   Common Card Styles (Login & Signup)
   ========================== */

/* Common card styling for login and signup */
.login-card, .signup-card {
    max-width: 600px;
    margin: 130px auto 50px auto; /* Adjust top margin if navbar height changes */
    border-radius: 10px;
}

/* Common header styling for login and signup */
.login-header, .signup-header {
    border: 2px solid #000 !important; /* Black border */
    background-color: #fff5ee;
    text-align: center;
    padding: 30px 20px 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.login-header i, .signup-header i { 
    font-size: 40px; 
    color: #6c3b2a; 
}

.login-header h2, .signup-header h2 { 
    margin-top: 10px; 
    font-weight: bold; 
}

/* Common card body styling for login and signup */
.login-card .card-body, .signup-card .card-body {
    border: 2px solid #000 !important; /* Black border */
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: #fff !important;
}

/* Common form controls and button styling */
.form-control, .btn-custom { 
    border-radius: 8px; 
}

/* Common responsive adjustments */
@media (max-width: 576px) {
    .login-card, .signup-card {
        margin: 90px 8px 32px 8px;
    }
    .login-header, .signup-header { 
        padding: 22px 8px 10px; 
    }
}
