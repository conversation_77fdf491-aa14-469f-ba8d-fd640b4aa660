<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Join Cozy<PERSON>ish</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/navbar_style.css">
    <link rel="stylesheet" href="css/customer_signup_style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" />
  </head>
  <body>
    <header>
      <!-- Navbar code reused, same as login page -->
      <nav class="guest-navbar fixed-top">
        <div class="container">
          <div class="row align-items-center">
            <div class="col-auto">
              <div class="guest-navbar-brand">
                <a href="#" class="guest-navbar-logo">
                  <img src="../logo/logo-light.svg" alt="CozyWish Logo">
                </a>
              </div>
            </div>
            <div class="col-auto ms-auto">
              <div class="guest-navbar-actions">
                <!-- Shopping Cart -->
                <div class="position-relative me-3">
                  <a href="#" class="btn-customer-action">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill">
                      3
                      <span class="visually-hidden">items in cart</span>
                    </span>
                  </a>
                </div>
                <!-- Notifications -->
                <div class="position-relative me-3">
                  <a href="#" class="btn-customer-action">
                    <i class="fas fa-bell"></i>
                    <span class="badge bg-warning position-absolute top-0 start-100 translate-middle rounded-pill">
                      5
                      <span class="visually-hidden">unread notifications</span>
                    </span>
                  </a>
                </div>
                <!-- User Menu -->
                <div class="dropdown">
                  <button class="btn-customer-menu dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <span>Menu</span>
                    <i class="fas fa-chevron-down"></i>
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-check me-2"></i>My Bookings <span class="badge bg-primary ms-auto">2</span></a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-shopping-cart me-2"></i>My Cart <span class="badge bg-danger ms-auto">3</span></a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-sign-out-alt me-2"></i>Log out</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
    <main>
      <div class="card signup-card">
        <div class="signup-header">
          <i class="fas fa-user-plus"></i>
          <h2>Join CozyWish</h2>
          <p class="text-muted">Create your account</p>
        </div>
        <div class="card-body px-4">
          <form>
            <div class="mb-3">
              <label for="email" class="form-label">
                <i class="fas fa-envelope me-2"></i>Email Address
              </label>
              <input type="email" class="form-control" id="email" placeholder="Enter your email address" required>
              <div class="form-text">We'll never share your email with anyone else.</div>
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">
                <i class="fas fa-lock me-2"></i>Password
              </label>
              <div class="input-group">
                <input type="password" class="form-control" id="password" placeholder="Create a strong password" required>
                <button class="btn btn-outline-secondary toggle-password" type="button" tabindex="-1" data-target="password">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="form-text">Your password must contain at least 8 characters.</div>
            </div>
            <div class="mb-3">
              <label for="confirm_password" class="form-label">
                <i class="fas fa-lock me-2"></i>Confirm Password
              </label>
              <div class="input-group">
                <input type="password" class="form-control" id="confirm_password" placeholder="Confirm your password" required>
                <button class="btn btn-outline-secondary toggle-password" type="button" tabindex="-1" data-target="confirm_password">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="form-text">Enter the same password as before, for verification.</div>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="terms" required>
              <label class="form-check-label" for="terms">
                I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
              </label>
            </div>
            <button type="submit" class="btn btn-dark w-100 btn-custom mb-3">
              <i class="fas fa-user-plus me-2"></i>Create Account
            </button>
          </form>
          <div class="text-center">
            <p class="mb-1">Already have an account?</p>
            <a href="customer_login.html" class="btn btn-outline-dark w-100 mb-2 btn-custom">
              <i class="fas fa-sign-in-alt me-2"></i>Signin
            </a>
            <p class="mb-1">Are you a service provider?</p>
            <button class="btn btn-outline-dark w-100 btn-custom">
              <i class="fas fa-briefcase me-2"></i>For Business
            </button>
          </div>
        </div>
      </div>
    </main>
    <footer></footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Simple password show/hide toggle
      document.querySelectorAll('.toggle-password').forEach(function(button) {
        button.addEventListener('click', function() {
          const inputId = button.getAttribute('data-target');
          const input = document.getElementById(inputId);
          if (input.type === 'password') {
            input.type = 'text';
            button.querySelector('i').classList.remove('fa-eye');
            button.querySelector('i').classList.add('fa-eye-slash');
          } else {
            input.type = 'password';
            button.querySelector('i').classList.remove('fa-eye-slash');
            button.querySelector('i').classList.add('fa-eye');
          }
        });
      });
    </script>
  </body>
</html>
