.PHONY: run reset_db seed_data help



# --- Start Django development server (accessible via LAN) ---
run:
	@echo "🚀 Starting Django development server at http://0.0.0.0:8000 ..."
	python manage.py runserver 0.0.0.0:8000



# --- Reset local SQLite database and clean cache files ---
reset_db:
	@echo "🔄  Starting Django reset: database and cache only..."
	@echo "⚠️  WARNING: This will completely wipe your local development database!"
	@echo "⚠️  All data will be lost. Migrations will be preserved."
	
	@echo "🛡️  Checking environment safety..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make reset_db_production' instead."; \
		echo "ℹ️  This prevents accidental database reset in production."; \
		exit 1; \
	fi
	
	@echo "🔍  Running environment validation..."
	@python scripts/validate_environment.py || exit 1
	
	@echo "ℹ️  Note: Please stop any running Django processes manually if needed."
	
	@echo "🗑️  Deleting SQLite database file..."
	@rm -f db.sqlite3 || true
	@echo "✅  Database file removed."

	@echo "🧹  Cleaning all Python cache files..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete || true
	@find . -name "*.pyo" -delete || true
	@echo "✅  Python cache cleaned."

	@echo "🗑️  Cleaning Django migration cache..."
	@find . -path "*/migrations/__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -path "*/migrations/*.pyc" -delete || true
	@echo "✅  Migration cache cleaned."

	@echo "🖼️  Clearing all user-uploaded media files..."
	@rm -rf media/* || true
	@mkdir -p media
	@echo "✅  Media files cleared."

	@echo "📁  Preserving existing migration files..."
	@echo "✅  Migration files preserved."

	@echo "⚙️  Applying existing migrations to rebuild the database schema..."
	@python manage.py migrate || (echo "❌ Failed to apply migrations. Please check your migration files." && exit 1)
	@echo "✅  Database schema rebuilt from existing migrations."

	@echo "🏙️  Seeding US cities data from CSV file..."
	@python manage.py seed_us_cities --clear || (echo "⚠️  Warning: US cities seeding failed, but continuing..." && true)
	@echo "✅  US cities data seeded."

	@echo "👤  Creating Django superuser..."
	@python scripts/create_superuser.py || (echo "❌ Failed to create superuser." && exit 1)

	@echo ""
	@echo "✅  🎉 Reset complete! Your development environment is ready."
	@echo "📋  Summary:"
	@echo "    • Fresh SQLite database created"
	@echo "    • All cache files cleaned"
	@echo "    • Existing migrations preserved and applied"
	@echo "    • Superuser created (email: <EMAIL>, password: 123)"
	@echo "    • US cities data seeded"
	@echo ""
	@python manage.py runserver 0.0.0.0:8000






# --- Show help information ---
help:
	@echo "🚀 CozyWish Makefile Commands"
	@echo ""
	@echo "📋 Development Commands:"
	@echo "  make run              - Start Django development server"
	@echo "  make reset_db         - Reset database with safety checks (development only)"
	@echo "🏭 Production Commands:"
	@echo "  make seed_data_production  - Seed test data in production (DANGEROUS)"
	@echo ""
	@echo "ℹ️  Use 'make help' to see this message again"
	@echo ""
































