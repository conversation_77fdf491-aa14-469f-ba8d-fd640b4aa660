"""
Constants and choices for the accounts_app models.
This module centralizes all hardcoded choices to improve maintainability.
"""

from django.utils.translation import gettext_lazy as _


# --- User Roles ---
class UserRoles:
    CUSTOMER = 'customer'
    SERVICE_PROVIDER = 'service_provider'
    ADMIN = 'admin'
    
    CHOICES = (
        (CUSTOMER, _('Customer')),
        (SERVICE_PROVIDER, _('Service Provider')),
        (ADMIN, _('Admin')),
    )


# --- User Status ---
class UserStatus:
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    SUSPENDED = 'suspended'
    PENDING_VERIFICATION = 'pending_verification'
    
    CHOICES = (
        (ACTIVE, _('Active')),
        (INACTIVE, _('Inactive')),
        (SUSPENDED, _('Suspended')),
        (PENDING_VERIFICATION, _('Pending Verification')),
    )


# --- Gender Choices ---
class Gender:
    MALE = 'M'
    FEMALE = 'F'
    OTHER = 'O'

    
    CHOICES = (
        (MALE, _('Male')),
        (FEMALE, _('Female')),
        (OTHER, _('Other')),
    )


# --- Month Choices ---
class Months:
    JANUARY = 1
    FEBRUARY = 2
    MARCH = 3
    APRIL = 4
    MAY = 5
    JUNE = 6
    JULY = 7
    AUGUST = 8
    SEPTEMBER = 9
    OCTOBER = 10
    NOVEMBER = 11
    DECEMBER = 12
    
    CHOICES = (
        (JANUARY, _('January')),
        (FEBRUARY, _('February')),
        (MARCH, _('March')),
        (APRIL, _('April')),
        (MAY, _('May')),
        (JUNE, _('June')),
        (JULY, _('July')),
        (AUGUST, _('August')),
        (SEPTEMBER, _('September')),
        (OCTOBER, _('October')),
        (NOVEMBER, _('November')),
        (DECEMBER, _('December')),
    )


# --- US States ---
class USStates:
    ALABAMA = 'AL'
    ALASKA = 'AK'
    ARIZONA = 'AZ'
    ARKANSAS = 'AR'
    CALIFORNIA = 'CA'
    COLORADO = 'CO'
    CONNECTICUT = 'CT'
    DELAWARE = 'DE'
    FLORIDA = 'FL'
    GEORGIA = 'GA'
    HAWAII = 'HI'
    IDAHO = 'ID'
    ILLINOIS = 'IL'
    INDIANA = 'IN'
    IOWA = 'IA'
    KANSAS = 'KS'
    KENTUCKY = 'KY'
    LOUISIANA = 'LA'
    MAINE = 'ME'
    MARYLAND = 'MD'
    MASSACHUSETTS = 'MA'
    MICHIGAN = 'MI'
    MINNESOTA = 'MN'
    MISSISSIPPI = 'MS'
    MISSOURI = 'MO'
    MONTANA = 'MT'
    NEBRASKA = 'NE'
    NEVADA = 'NV'
    NEW_HAMPSHIRE = 'NH'
    NEW_JERSEY = 'NJ'
    NEW_MEXICO = 'NM'
    NEW_YORK = 'NY'
    NORTH_CAROLINA = 'NC'
    NORTH_DAKOTA = 'ND'
    OHIO = 'OH'
    OKLAHOMA = 'OK'
    OREGON = 'OR'
    PENNSYLVANIA = 'PA'
    RHODE_ISLAND = 'RI'
    SOUTH_CAROLINA = 'SC'
    SOUTH_DAKOTA = 'SD'
    TENNESSEE = 'TN'
    TEXAS = 'TX'
    UTAH = 'UT'
    VERMONT = 'VT'
    VIRGINIA = 'VA'
    WASHINGTON = 'WA'
    WEST_VIRGINIA = 'WV'
    WISCONSIN = 'WI'
    WYOMING = 'WY'
    
    CHOICES = (
        (ALABAMA, _('Alabama')),
        (ALASKA, _('Alaska')),
        (ARIZONA, _('Arizona')),
        (ARKANSAS, _('Arkansas')),
        (CALIFORNIA, _('California')),
        (COLORADO, _('Colorado')),
        (CONNECTICUT, _('Connecticut')),
        (DELAWARE, _('Delaware')),
        (FLORIDA, _('Florida')),
        (GEORGIA, _('Georgia')),
        (HAWAII, _('Hawaii')),
        (IDAHO, _('Idaho')),
        (ILLINOIS, _('Illinois')),
        (INDIANA, _('Indiana')),
        (IOWA, _('Iowa')),
        (KANSAS, _('Kansas')),
        (KENTUCKY, _('Kentucky')),
        (LOUISIANA, _('Louisiana')),
        (MAINE, _('Maine')),
        (MARYLAND, _('Maryland')),
        (MASSACHUSETTS, _('Massachusetts')),
        (MICHIGAN, _('Michigan')),
        (MINNESOTA, _('Minnesota')),
        (MISSISSIPPI, _('Mississippi')),
        (MISSOURI, _('Missouri')),
        (MONTANA, _('Montana')),
        (NEBRASKA, _('Nebraska')),
        (NEVADA, _('Nevada')),
        (NEW_HAMPSHIRE, _('New Hampshire')),
        (NEW_JERSEY, _('New Jersey')),
        (NEW_MEXICO, _('New Mexico')),
        (NEW_YORK, _('New York')),
        (NORTH_CAROLINA, _('North Carolina')),
        (NORTH_DAKOTA, _('North Dakota')),
        (OHIO, _('Ohio')),
        (OKLAHOMA, _('Oklahoma')),
        (OREGON, _('Oregon')),
        (PENNSYLVANIA, _('Pennsylvania')),
        (RHODE_ISLAND, _('Rhode Island')),
        (SOUTH_CAROLINA, _('South Carolina')),
        (SOUTH_DAKOTA, _('South Dakota')),
        (TENNESSEE, _('Tennessee')),
        (TEXAS, _('Texas')),
        (UTAH, _('Utah')),
        (VERMONT, _('Vermont')),
        (VIRGINIA, _('Virginia')),
        (WASHINGTON, _('Washington')),
        (WEST_VIRGINIA, _('West Virginia')),
        (WISCONSIN, _('Wisconsin')),
        (WYOMING, _('Wyoming')),
    )


# --- Security Alert Types ---
class AlertTypes:
    MULTIPLE_FAILURES = 'multiple_failures'
    SUSPICIOUS_IP = 'suspicious_ip'
    UNUSUAL_LOCATION = 'unusual_location'
    BRUTE_FORCE = 'brute_force'
    ACCOUNT_COMPROMISE = 'account_compromise'
    PASSWORD_BREACH = 'password_breach'
    
    CHOICES = (
        (MULTIPLE_FAILURES, _('Multiple Failed Attempts')),
        (SUSPICIOUS_IP, _('Suspicious IP Address')),
        (UNUSUAL_LOCATION, _('Unusual Login Location')),
        (BRUTE_FORCE, _('Brute Force Attack')),
        (ACCOUNT_COMPROMISE, _('Account Compromise')),
        (PASSWORD_BREACH, _('Password Breach')),
    )


# --- Security Alert Severity Levels ---
class SeverityLevels:
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'
    
    CHOICES = (
        (LOW, _('Low')),
        (MEDIUM, _('Medium')),
        (HIGH, _('High')),
        (CRITICAL, _('Critical')),
    )




# --- User Preferences ---
class ThemePreferences:
    LIGHT = 'light'
    DARK = 'dark'
    AUTO = 'auto'
    
    CHOICES = (
        (LIGHT, _('Light')),
        (DARK, _('Dark')),
        (AUTO, _('Auto')),
    )


class LanguagePreferences:
    ENGLISH = 'en'
    SPANISH = 'es'
    FRENCH = 'fr'
    GERMAN = 'de'
    
    CHOICES = (
        (ENGLISH, _('English')),
        (SPANISH, _('Spanish')),
        (FRENCH, _('French')),
        (GERMAN, _('German')),
    )


class TimezonePreferences:
    UTC = 'UTC'
    EASTERN = 'America/New_York'
    CENTRAL = 'America/Chicago'
    MOUNTAIN = 'America/Denver'
    PACIFIC = 'America/Los_Angeles'
    
    CHOICES = (
        (UTC, _('UTC')),
        (EASTERN, _('Eastern Time')),
        (CENTRAL, _('Central Time')),
        (MOUNTAIN, _('Mountain Time')),
        (PACIFIC, _('Pacific Time')),
    )


class NotificationPreferences:
    EMAIL = 'email'
    SMS = 'sms'
    PUSH = 'push'
    NONE = 'none'
    
    CHOICES = (
        (EMAIL, _('Email')),
        (SMS, _('SMS')),
        (PUSH, _('Push Notifications')),
        (NONE, _('None')),
    )


# --- Security Settings ---
class SecuritySettings:
    TWO_FACTOR_DISABLED = 'disabled'
    TWO_FACTOR_SMS = 'sms'
    TWO_FACTOR_EMAIL = 'email'
    TWO_FACTOR_AUTHENTICATOR = 'authenticator'
    
    TWO_FACTOR_CHOICES = (
        (TWO_FACTOR_DISABLED, _('Disabled')),
        (TWO_FACTOR_SMS, _('SMS')),
        (TWO_FACTOR_EMAIL, _('Email')),
        (TWO_FACTOR_AUTHENTICATOR, _('Authenticator App')),
    )


# --- Regular Expression Patterns ---
class RegexPatterns:
    EMAIL = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    PHONE = r'^\+?1?\d{9,15}$'
    ZIP_CODE = r'^\d{5}(-\d{4})?$'
    PASSWORD_STRENGTH = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$'


# --- Model Constants ---
class ModelConstants:
    # Field lengths
    EMAIL_MAX_LENGTH = 254
    NAME_MAX_LENGTH = 100
    PHONE_MAX_LENGTH = 20
    ADDRESS_MAX_LENGTH = 255
    CITY_MAX_LENGTH = 100
    STATE_MAX_LENGTH = 2
    ZIP_CODE_MAX_LENGTH = 10
    DESCRIPTION_MAX_LENGTH = 500
    
    # Password settings
    PASSWORD_HASH_MAX_LENGTH = 128
    PASSWORD_HISTORY_COUNT = 5
    PASSWORD_EXPIRY_DAYS = 90
    
    # Lockout settings
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    
    # Session settings
    SESSION_TIMEOUT_SECONDS = 3600  # 1 hour
    
    # File upload settings
    MAX_IMAGE_SIZE_MB = 5
    ALLOWED_IMAGE_FORMATS = ['JPEG', 'PNG', 'GIF', 'WEBP']


# --- Validation Messages ---
class ValidationMessages:
    INVALID_EMAIL = _('Enter a valid email address.')
    INVALID_PHONE = _('Enter a valid phone number (e.g., +**********).')
    INVALID_ZIP_CODE = _('Enter a valid ZIP code.')
    PASSWORD_TOO_SHORT = _('Password must be at least 12 characters long.')
    PASSWORD_TOO_WEAK = _('Password must contain uppercase, lowercase, digits, and special characters.')
    PASSWORD_REUSED = _('You cannot reuse any of your last 5 passwords.')
    ACCOUNT_LOCKED = _('Account is temporarily locked due to too many failed attempts.')
    SESSION_EXPIRED = _('Your session has expired. Please log in again.')
    INVALID_IMAGE_FORMAT = _('Please upload a valid image file (JPEG, PNG, GIF, or WEBP).')
    IMAGE_TOO_LARGE = _('Image file size cannot exceed 5MB.')
    
    # Field-specific messages
    REQUIRED_FIELD = _('This field is required.')
    UNIQUE_EMAIL = _('A user with this email already exists.')
    UNIQUE_PHONE = _('A user with this phone number already exists.')
    INVALID_BIRTH_YEAR = _('Please enter a valid birth year.')
    INVALID_BUSINESS_NAME = _('Business name must be at least 3 characters long.')
    INVALID_CONTACT_NAME = _('Contact name must be at least 2 characters long.')
    
    # Custom validation messages
    FUTURE_DATE_NOT_ALLOWED = _('Future dates are not allowed.')
    INVALID_AGE = _('Age must be between 13 and 120 years.')
    INVALID_BUSINESS_HOURS = _('Business hours must be in valid format.')
    INVALID_WEBSITE_URL = _('Please enter a valid website URL.')
    INVALID_SOCIAL_MEDIA_URL = _('Please enter a valid social media URL.')


# --- Default Values ---
class DefaultValues:
    DEFAULT_ROLE = UserRoles.CUSTOMER
    DEFAULT_STATUS = UserStatus.ACTIVE
    DEFAULT_THEME = ThemePreferences.LIGHT
    DEFAULT_LANGUAGE = LanguagePreferences.ENGLISH
    DEFAULT_TIMEZONE = TimezonePreferences.UTC
    DEFAULT_NOTIFICATION_PREFERENCE = NotificationPreferences.EMAIL
    DEFAULT_TWO_FACTOR = SecuritySettings.TWO_FACTOR_DISABLED
    DEFAULT_IS_PUBLIC = True
    DEFAULT_IS_ACTIVE = True
    DEFAULT_FAILED_ATTEMPTS = 0
    DEFAULT_ALERT_SEVERITY = SeverityLevels.MEDIUM 