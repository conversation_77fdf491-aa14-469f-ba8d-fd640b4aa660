# Generated by Django 5.2.3 on 2025-07-18 04:21

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0002_alter_customerprofile_gender_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='serviceproviderprofile',
            name='phone',
            field=models.CharField(blank=True, help_text='Business phone number (can be added later)', max_length=20, validators=[django.core.validators.RegexValidator(message='Enter a valid phone number (e.g., +**********)', regex='^\\+?1?\\d{9,15}$')], verbose_name='business phone'),
        ),
    ]
