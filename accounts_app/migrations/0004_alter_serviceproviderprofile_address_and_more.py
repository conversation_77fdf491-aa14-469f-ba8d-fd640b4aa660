# Generated by Django 5.2.3 on 2025-07-18 04:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0003_alter_serviceproviderprofile_phone'),
    ]

    operations = [
        migrations.AlterField(
            model_name='serviceproviderprofile',
            name='address',
            field=models.CharField(blank=True, help_text='Business address (can be added later)', max_length=255, verbose_name='street address'),
        ),
        migrations.AlterField(
            model_name='serviceproviderprofile',
            name='city',
            field=models.CharField(blank=True, help_text='City (can be added later)', max_length=100, verbose_name='city'),
        ),
        migrations.AlterField(
            model_name='serviceproviderprofile',
            name='state',
            field=models.Char<PERSON>ield(blank=True, choices=[('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'), ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'), ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'), ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'), ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'), ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'), ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'), ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'), ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'), ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'), ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'), ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'), ('WI', 'Wisconsin'), ('WY', 'Wyoming')], help_text='State (can be added later)', max_length=2, verbose_name='state'),
        ),
        migrations.AlterField(
            model_name='serviceproviderprofile',
            name='zip_code',
            field=models.CharField(blank=True, help_text='ZIP code (can be added later)', max_length=10, verbose_name='ZIP code'),
        ),
    ]
