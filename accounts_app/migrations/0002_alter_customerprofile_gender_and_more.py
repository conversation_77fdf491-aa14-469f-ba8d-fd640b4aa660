# Generated by Django 5.2.3 on 2025-07-18 03:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerprofile',
            name='gender',
            field=models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1, verbose_name='gender'),
        ),
        migrations.AlterField(
            model_name='userpreferences',
            name='notification_preference',
            field=models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('push', 'Push Notifications'), ('none', 'None')], default='email', max_length=10, verbose_name='notification preference'),
        ),
        migrations.AlterField(
            model_name='userpreferences',
            name='timezone_preference',
            field=models.Char<PERSON>ield(choices=[('UTC', 'UTC'), ('America/New_York', 'Eastern Time'), ('America/Chicago', 'Central Time'), ('America/Denver', 'Mountain Time'), ('America/Los_Angeles', 'Pacific Time')], default='UTC', max_length=50, verbose_name='timezone preference'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='gender',
            field=models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1, verbose_name='gender'),
        ),
    ]
