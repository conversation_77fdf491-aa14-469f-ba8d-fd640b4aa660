"""
Privacy settings and GDPR compliance views for accounts_app.

This module contains views for managing user privacy settings,
data export functionality, and verification badge management.
"""

import json
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponse, JsonResponse, Http404, HttpResponseRedirect
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import (
    View, TemplateView, FormView, UpdateView, DetailView, CreateView
)

# Local imports
from ..decorators import customer_required, service_provider_required, role_required
from ..models import (
    CustomUser, ProfilePrivacySettings, ProfileVerification,
    CustomerProfile, ServiceProviderProfile
)
from ..services import GDPRService
from ..utils.logging import log_user_activity, log_error

logger = logging.getLogger(__name__)


class PrivacySettingsView(LoginRequiredMixin, TemplateView):
    """
    View for managing user privacy settings.
    """
    template_name = 'accounts_app/privacy_settings.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get or create privacy settings for the user
        privacy_settings, created = ProfilePrivacySettings.objects.get_or_create(
            user=self.request.user,
            defaults=ProfilePrivacySettings.get_default_settings()
        )
        
        context.update({
            'privacy_settings': privacy_settings,
            'visibility_choices': ProfilePrivacySettings.VISIBILITY_CHOICES,
            'user': self.request.user,
        })
        
        return context


class PrivacySettingsUpdateView(LoginRequiredMixin, View):
    """
    View for updating user privacy settings via AJAX.
    """
    
    def post(self, request, *args, **kwargs):
        try:
            # Get or create privacy settings
            privacy_settings, created = ProfilePrivacySettings.objects.get_or_create(
                user=request.user,
                defaults=ProfilePrivacySettings.get_default_settings()
            )
            
            # Update privacy settings based on POST data
            updated_fields = []
            
            # Profile visibility
            if 'profile_visibility' in request.POST:
                new_visibility = request.POST['profile_visibility']
                if new_visibility in dict(ProfilePrivacySettings.VISIBILITY_CHOICES):
                    privacy_settings.profile_visibility = new_visibility
                    updated_fields.append('profile_visibility')
            
            # Boolean fields
            boolean_fields = [
                'show_email', 'show_phone', 'show_address', 'show_birth_date',
                'show_gender', 'show_profile_picture', 'show_last_seen',
                'show_booking_history', 'show_reviews', 'show_favorites',
                'discoverable_in_search', 'allow_friend_requests', 'allow_messages',
                'allow_analytics', 'allow_personalized_ads', 'data_processing_consent',
                'marketing_consent', 'third_party_sharing'
            ]
            
            for field in boolean_fields:
                if field in request.POST:
                    value = request.POST[field].lower() == 'true'
                    if getattr(privacy_settings, field) != value:
                        setattr(privacy_settings, field, value)
                        updated_fields.append(field)
            
            # Save the changes
            if updated_fields:
                privacy_settings.save()
                
                # Log the privacy settings update
                log_user_activity(
                    activity_type='privacy_settings_updated',
                    user=request.user,
                    request=request,
                    details={'updated_fields': updated_fields}
                )
                
                messages.success(request, _('Privacy settings updated successfully.'))
                
                return JsonResponse({
                    'success': True,
                    'message': _('Privacy settings updated successfully.'),
                    'updated_fields': updated_fields
                })
            else:
                return JsonResponse({
                    'success': True,
                    'message': _('No changes were made.'),
                    'updated_fields': []
                })
                
        except Exception as e:
            logger.error(f"Privacy settings update error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': _('Failed to update privacy settings. Please try again.'),
                'error': str(e)
            }, status=500)


class DataExportView(LoginRequiredMixin, TemplateView):
    """
    View for GDPR data export functionality.
    """
    template_name = 'accounts_app/data_export.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get data portability information
        portability_info = GDPRService.get_data_portability_info(self.request.user)
        
        context.update({
            'portability_info': portability_info,
            'export_formats': ['json', 'csv', 'xml'],
            'user': self.request.user,
        })
        
        return context


class DataExportDownloadView(LoginRequiredMixin, View):
    """
    View for downloading user data export.
    """
    
    def post(self, request, *args, **kwargs):
        try:
            # Get export format from request
            export_format = request.POST.get('format', 'json').lower()
            
            if export_format not in ['json', 'csv', 'xml']:
                return JsonResponse({
                    'success': False,
                    'message': _('Invalid export format.')
                }, status=400)
            
            # Generate export file
            success, file_path, message = GDPRService.generate_data_export_file(
                user=request.user,
                file_format=export_format,
                request=request
            )
            
            if success:
                # Read the file and return it as response
                import os
                
                with open(file_path, 'rb') as f:
                    file_data = f.read()
                
                # Set appropriate content type
                content_types = {
                    'json': 'application/json',
                    'csv': 'text/csv',
                    'xml': 'application/xml'
                }
                
                response = HttpResponse(
                    file_data,
                    content_type=content_types.get(export_format, 'application/octet-stream')
                )
                
                # Set download headers
                filename = os.path.basename(file_path)
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                response['Content-Length'] = len(file_data)
                
                # Clean up the temporary file
                try:
                    os.remove(file_path)
                except:
                    pass
                
                return response
            else:
                return JsonResponse({
                    'success': False,
                    'message': message
                }, status=500)
                
        except Exception as e:
            logger.error(f"Data export download error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': _('Failed to generate data export. Please try again.')
            }, status=500)


class DataDeletionRequestView(LoginRequiredMixin, View):
    """
    View for requesting data deletion (GDPR right to be forgotten).
    """
    
    def post(self, request, *args, **kwargs):
        try:
            # Schedule data deletion
            success, message = GDPRService.schedule_data_deletion(
                user=request.user,
                request=request
            )
            
            if success:
                messages.success(request, message)
                return JsonResponse({
                    'success': True,
                    'message': message
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': message
                }, status=500)
                
        except Exception as e:
            logger.error(f"Data deletion request error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': _('Failed to process deletion request. Please try again.')
            }, status=500)


class VerificationBadgeView(LoginRequiredMixin, DetailView):
    """
    View for displaying user verification badges.
    """
    model = CustomUser
    template_name = 'accounts_app/verification_badges.html'
    context_object_name = 'user'
    
    def get_object(self):
        # Users can only view their own verification badges
        return self.request.user
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all verifications for the user
        verifications = ProfileVerification.get_user_verifications(self.request.user)
        
        # Group verifications by type
        verification_dict = {}
        for verification in verifications:
            verification_dict[verification.verification_type] = verification
        
        context.update({
            'verifications': verifications,
            'verification_dict': verification_dict,
            'verification_types': ProfileVerification.VERIFICATION_TYPES,
            'available_verifications': self._get_available_verifications(),
        })
        
        return context
    
    def _get_available_verifications(self):
        """Get verification types available for the user."""
        user = self.request.user
        available = []
        
        # Email verification - always available
        if not user.verifications.filter(
            verification_type='email',
            status='verified'
        ).exists():
            available.append('email')
        
        # Phone verification - if user has phone number
        if hasattr(user, 'customer_profile') and user.customer_profile.phone_number:
            if not user.verifications.filter(
                verification_type='phone',
                status='verified'
            ).exists():
                available.append('phone')
        
        # Business verification - for service providers
        if user.is_service_provider:
            if not user.verifications.filter(
                verification_type='business',
                status='verified'
            ).exists():
                available.append('business')
        
        # Identity verification - always available
        if not user.verifications.filter(
            verification_type='identity',
            status='verified'
        ).exists():
            available.append('identity')
        
        return available


class RequestVerificationView(LoginRequiredMixin, View):
    """
    View for requesting profile verification.
    """
    
    def post(self, request, *args, **kwargs):
        try:
            verification_type = request.POST.get('verification_type')
            
            if verification_type not in dict(ProfileVerification.VERIFICATION_TYPES):
                return JsonResponse({
                    'success': False,
                    'message': _('Invalid verification type.')
                }, status=400)
            
            # Check if user already has this verification
            existing_verification = ProfileVerification.objects.filter(
                user=request.user,
                verification_type=verification_type
            ).first()
            
            if existing_verification:
                if existing_verification.status == 'verified':
                    return JsonResponse({
                        'success': False,
                        'message': _('You are already verified for this type.')
                    }, status=400)
                elif existing_verification.status == 'pending':
                    return JsonResponse({
                        'success': False,
                        'message': _('You already have a pending verification request.')
                    }, status=400)
            
            # Create new verification request
            verification_data = {
                'requested_at': timezone.now().isoformat(),
                'request_ip': self._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            }
            
            # Handle file upload if present
            verification_document = request.FILES.get('verification_document')
            
            verification = ProfileVerification.objects.create(
                user=request.user,
                verification_type=verification_type,
                verification_data=verification_data,
                verification_document=verification_document
            )
            
            # Log the verification request
            log_user_activity(
                activity_type='verification_requested',
                user=request.user,
                request=request,
                details={'verification_type': verification_type}
            )
            
            messages.success(request, _('Verification request submitted successfully.'))
            
            return JsonResponse({
                'success': True,
                'message': _('Verification request submitted successfully.'),
                'verification_id': verification.id
            })
            
        except Exception as e:
            logger.error(f"Verification request error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': _('Failed to submit verification request. Please try again.')
            }, status=500)
    
    def _get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class VerificationStatusView(LoginRequiredMixin, View):
    """
    View for checking verification status via AJAX.
    """
    
    def get(self, request, *args, **kwargs):
        try:
            verification_id = request.GET.get('verification_id')
            
            if not verification_id:
                return JsonResponse({
                    'success': False,
                    'message': _('Verification ID is required.')
                }, status=400)
            
            # Get verification (ensure it belongs to the requesting user)
            verification = get_object_or_404(
                ProfileVerification,
                id=verification_id,
                user=request.user
            )
            
            return JsonResponse({
                'success': True,
                'verification': {
                    'id': verification.id,
                    'type': verification.get_verification_type_display(),
                    'status': verification.get_status_display(),
                    'submitted_at': verification.submitted_at.isoformat(),
                    'verified_at': verification.verified_at.isoformat() if verification.verified_at else None,
                    'expires_at': verification.expires_at.isoformat() if verification.expires_at else None,
                    'is_verified': verification.is_verified,
                    'is_expired': verification.is_expired,
                    'days_until_expiry': verification.days_until_expiry,
                    'badge_info': verification.get_badge_info(),
                    'rejection_reason': verification.rejection_reason if verification.status == 'rejected' else None,
                }
            })
            
        except Exception as e:
            logger.error(f"Verification status check error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': _('Failed to check verification status.')
            }, status=500)


# Utility views for privacy settings
@login_required
def privacy_settings_quick_toggle(request):
    """
    Quick toggle for privacy settings via AJAX.
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Method not allowed'}, status=405)
    
    try:
        field_name = request.POST.get('field')
        current_value = request.POST.get('current_value', 'false').lower() == 'true'
        new_value = not current_value
        
        # Get or create privacy settings
        privacy_settings, created = ProfilePrivacySettings.objects.get_or_create(
            user=request.user,
            defaults=ProfilePrivacySettings.get_default_settings()
        )
        
        # Validate field name
        if not hasattr(privacy_settings, field_name):
            return JsonResponse({
                'success': False,
                'message': _('Invalid field name.')
            }, status=400)
        
        # Update the field
        setattr(privacy_settings, field_name, new_value)
        privacy_settings.save()
        
        # Log the change
        log_user_activity(
            activity_type='privacy_quick_toggle',
            user=request.user,
            request=request,
            details={'field': field_name, 'new_value': new_value}
        )
        
        return JsonResponse({
            'success': True,
            'field': field_name,
            'new_value': new_value,
            'message': _('Setting updated successfully.')
        })
        
    except Exception as e:
        logger.error(f"Privacy quick toggle error: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': _('Failed to update setting.')
        }, status=500)


@login_required
def user_verification_summary(request):
    """
    Get user verification summary for display in profile.
    """
    try:
        verifications = ProfileVerification.get_user_verifications(request.user)
        
        # Count verification types
        verification_counts = {
            'total': verifications.count(),
            'verified': verifications.filter(status='verified').count(),
            'pending': verifications.filter(status='pending').count(),
            'rejected': verifications.filter(status='rejected').count(),
            'expired': verifications.filter(status='expired').count(),
        }
        
        # Get verification badges
        badges = []
        for verification in verifications.filter(status='verified'):
            badges.append({
                'type': verification.verification_type,
                'badge_info': verification.get_badge_info(),
                'verified_at': verification.verified_at.isoformat() if verification.verified_at else None,
            })
        
        return JsonResponse({
            'success': True,
            'verification_counts': verification_counts,
            'badges': badges,
            'is_verified_user': verification_counts['verified'] > 0,
        })
        
    except Exception as e:
        logger.error(f"Verification summary error: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': _('Failed to get verification summary.')
        }, status=500) 