"""
Customer views module with organized sub-modules.

This file imports all customer views from their respective sub-modules
to maintain backward compatibility with existing imports.
"""

# Import authentication views
from .auth import (
    CustomerSignupView,
    CustomerLogoutView,
    UnifiedLogoutView,
)

# Import profile management views
from .profile import (
    CustomerProfileView,
    CustomerProfileEditView,
)

# Import password management views
from .password import (
    CustomerPasswordChangeView,
    CustomerPasswordResetView,
    CustomerPasswordResetDoneView,
    CustomerPasswordResetConfirmView,
    CustomerPasswordResetCompleteView,
)

# Import account management views
from .account import (
    CustomerDeactivateAccountView,
)

# Backward compatibility exports
__all__ = [
    # Authentication views
    'CustomerSignupView',
    'CustomerLogoutView',
    'UnifiedLogoutView',
    
    # Profile management views
    'CustomerProfileView',
    'CustomerProfileEditView',
    
    # Password management views
    'CustomerPasswordChangeView',
    'CustomerPasswordResetView',
    'CustomerPasswordResetDoneView',
    'CustomerPasswordResetConfirmView',
    'CustomerPasswordResetCompleteView',
    
    # Account management views
    'CustomerDeactivateAccountView',
] 