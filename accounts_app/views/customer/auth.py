# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login, logout
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView, RedirectView
import logging

# --- Local App Imports ---
from ...constants import UserRoles
from ...decorators import CustomerRequiredMixin, AnonymousRequiredMixin
from ...forms import CustomerSignupForm
from ...utils.logging import (
    log_account_lifecycle_event,
    log_authentication_event,
    log_error,
    log_user_activity,
    performance_monitor,
)
from ...models import CustomUser, CustomerProfile
from ...services import (
    AuthenticationService,
    UserRegistrationService,
    AuthenticationError,
)
from ..common import MESSAGES, logger, record_login_attempt
from ...utils.logging import log_error
from ..common import get_client_ip

# Customer-specific logger
customer_logger = logging.getLogger('accounts_app.customer')


# --- Customer Authentication Views ---

class CustomerSignupView(AnonymousRequiredMixin, CreateView):
    """
    Customer registration with automatic login and profile creation.
    
    This view handles the complete customer registration process including:
    - Form validation and user creation
    - Automatic profile creation
    - Welcome email sending
    - User authentication and login
    - Comprehensive error handling and logging
    
    Attributes:
        model: CustomUser model for user creation
        form_class: CustomerSignupForm for validation
        template_name: Template for rendering signup form
        success_url: URL to redirect after successful signup
    """
    model = CustomUser
    form_class = CustomerSignupForm
    template_name = 'accounts_app/customer/signup.html'
    success_url = reverse_lazy('home_app:home')  # Redirect to home page

    def get_context_data(self, **kwargs):
        """
        Get context data for template rendering.
        
        Handles the case where object doesn't exist during form rendering.
        
        Args:
            **kwargs: Additional context variables
            
        Returns:
            dict: Context data for template rendering
        """
        if 'object' not in kwargs:
            kwargs['object'] = None
        return super().get_context_data(**kwargs)

    def form_valid(self, form):
        """
        Process valid signup form submissions using UserRegistrationService.
        
        This method handles the complete registration workflow:
        1. Extracts and validates user data from form
        2. Creates user account using UserRegistrationService
        3. Sends welcome email
        4. Authenticates and logs in the new user
        5. Handles success/failure responses with appropriate messaging
        
        Args:
            form: Validated CustomerSignupForm instance
            
        Returns:
            HttpResponse: Success redirect or form re-render on error
        """
        try:
            # Prepare user data for service
            user_data = {
                'email': form.cleaned_data['email'],
                'password': form.cleaned_data['password1'],
                'first_name': form.cleaned_data.get('first_name', ''),
                'last_name': form.cleaned_data.get('last_name', ''),
                'phone_number': form.cleaned_data.get('phone_number', ''),
                'gender': form.cleaned_data.get('gender', ''),
                'birth_month': form.cleaned_data.get('birth_month'),
                'birth_year': form.cleaned_data.get('birth_year'),
            }
            
            # Use service to register customer
            success, user, message = UserRegistrationService.register_customer(
                user_data=user_data,
                request=self.request
            )
            
            if success:
                self.object = user
                
                # Send welcome email
                self._send_welcome_email(user)
                
                # Authenticate user using service
                AuthenticationService.login_user(user, self.request)
                
                # Success message
                self._send_success_message(user)
                
                customer_logger.info(f"Customer registration successful: {user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                # Registration failed
                messages.error(self.request, message)
                customer_logger.error(f"Customer registration failed: {message}")
                return self.form_invalid(form)
                
        except Exception as error:
            # Handle unexpected errors
            log_error(
                error_type='customer_signup',
                error_message="Unexpected error during customer signup",
                request=self.request,
                exception=error,
                details={'form_data': form.cleaned_data}
            )
            messages.error(self.request, MESSAGES['signup_error'])
            customer_logger.error(f"Customer signup error: {str(error)}", exc_info=True)
            return self.form_invalid(form)

    def _send_welcome_email(self, user):
        """Send welcome email to the newly registered customer."""
        try:
            from utility_app.email_utils import send_welcome_email
            user_name = user.get_full_name() or user.email.split('@')[0]
            send_welcome_email(user.email, user_name, 'customer')
            customer_logger.info(f"Welcome email sent to customer: {user.email}")
        except Exception as e:
            customer_logger.warning(f"Failed to send welcome email to customer {user.email}: {str(e)}")

    def _send_success_message(self, user):
        """Send success message to the user."""
        messages.success(
            self.request,
            _("Welcome to CozyWish! Your account has been created successfully.")
        )

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        self.object = None
        
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Customer signup form validation failed",
                request=self.request,
                details={'form_errors': form.errors}
            )
            customer_logger.warning(f"Customer signup form validation failed: {form.errors}")
        
        context = self.get_context_data(form=form)
        return self.render_to_response(context)


class CustomerLogoutView(CustomerRequiredMixin, RedirectView):
    """Handle customer logout with activity logging using AuthenticationService."""
    url = reverse_lazy('home_app:home')  # Redirect to home page

    def get(self, request, *args, **kwargs):
        """Handle logout request using AuthenticationService"""
        try:
            user_email = request.user.email if request.user.is_authenticated else None
            
            # Use service to logout user
            logout_success = AuthenticationService.logout_user(request)
            
            if logout_success:
                messages.success(request, MESSAGES['logout_success'])
                customer_logger.info(f"Customer logout successful: {user_email}")
            else:
                customer_logger.error(f"Customer logout service failed: {user_email}")
                
        except Exception as e:
            log_error(
                error_type='customer_logout',
                error_message="Unexpected error during customer logout",
                request=request,
                exception=e,
                details={'user_email': user_email}
            )
            customer_logger.error(f"Customer logout error: {str(e)}", exc_info=True)
            
        return super().get(request, *args, **kwargs)


class UnifiedLogoutView(LoginRequiredMixin, RedirectView):
    """
    Unified logout handler for all user types using AuthenticationService.
    """
    url = reverse_lazy('home_app:home')  # Redirect to home page

    def get(self, request, *args, **kwargs):
        """Handle unified logout for all user types"""
        try:
            user_email = request.user.email if request.user.is_authenticated else None
            user_role = request.user.role if request.user.is_authenticated else None
            
            # Use service to logout user
            logout_success = AuthenticationService.logout_user(request)
            
            if logout_success:
                messages.success(request, MESSAGES['logout_success'])
                customer_logger.info(f"Unified logout successful: {user_email}, role: {user_role}")
            else:
                customer_logger.error(f"Unified logout service failed: {user_email}")
                
        except Exception as e:
            log_error(
                error_type='unified_logout',
                error_message="Unexpected error during unified logout",
                request=request,
                exception=e,
                details={'user_email': user_email, 'user_role': user_role}
            )
            customer_logger.error(f"Unified logout error: {str(e)}", exc_info=True)
            
        return super().get(request, *args, **kwargs) 