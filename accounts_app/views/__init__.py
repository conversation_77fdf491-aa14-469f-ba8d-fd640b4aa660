# --- Import all views for compatibility ---

# Import email utilities for test patching compatibility
from utility_app.email_utils import send_welcome_email

# Permission decorators and mixins
from ..decorators import (
    CustomerRequiredMixin, ServiceProviderRequiredMixin, AdminRequiredMixin,
    AnonymousRequiredMixin, customer_required, service_provider_required,
    admin_required, role_required, anonymous_required
)

# Common utilities and business landing page
from .common import (
    get_client_ip, record_login_attempt, business_landing_view, MESSAGES,
    email_verification_view, resend_verification_email_view,
    email_verification_success_view, email_verification_sent_view
)

# Customer views - imported from new subdirectory structure
from .customer import (
    # Authentication views
    UnifiedLogoutView,
    # Profile and account management views
    CustomerProfileView, CustomerProfileEditView,
    CustomerPasswordChangeView, CustomerDeactivateAccountView,
)

# Provider views
from .provider import (
    # Authentication views
    ServiceProviderSignupView, provider_signup_done_view, provider_email_verify_view,
    # Profile management views
    ServiceProviderProfileView, ServiceProviderProfileEditView,
    # Password management views
    service_provider_change_password_view,
    ServiceProviderPasswordResetView, ServiceProviderPasswordResetDoneView,
    ServiceProviderPasswordResetConfirmView, ServiceProviderPasswordResetCompleteView,
    # Account management views
    service_provider_deactivate_account_view,
    # Team management views
    service_provider_team_list_view, service_provider_team_add_view,
    service_provider_team_edit_view, service_provider_team_delete_view,
)

# Team management views
# from .team import (
#     team_member_list_view, team_member_add_view, team_member_edit_view,
#     team_member_delete_view, team_member_toggle_status_view,
# )

# Make all views available at package level for backward compatibility
__all__ = [
    # Email utilities
    'send_welcome_email',
    # Permission decorators and mixins
    'CustomerRequiredMixin', 'ServiceProviderRequiredMixin', 'AdminRequiredMixin',
    'AnonymousRequiredMixin', 'customer_required', 'service_provider_required',
    'admin_required', 'role_required', 'anonymous_required',
    # Common
    'get_client_ip', 'record_login_attempt', 'business_landing_view', 'MESSAGES',
    'email_verification_view', 'resend_verification_email_view',
    'email_verification_success_view', 'email_verification_sent_view',
    # Customer views
    'CustomerProfileView', 'CustomerProfileEditView',
    'CustomerPasswordChangeView', 'CustomerDeactivateAccountView',
    'UnifiedLogoutView',
    # Provider views
    'ServiceProviderSignupView', 'provider_signup_done_view', 'provider_email_verify_view',
    'ServiceProviderProfileView', 'ServiceProviderProfileEditView',
    'service_provider_change_password_view',
    'ServiceProviderPasswordResetView', 'ServiceProviderPasswordResetDoneView',
    'ServiceProviderPasswordResetConfirmView', 'ServiceProviderPasswordResetCompleteView',
    'service_provider_deactivate_account_view',
    'service_provider_team_list_view', 'service_provider_team_add_view',
    'service_provider_team_edit_view', 'service_provider_team_delete_view',
    # Team management views
    # 'team_member_list_view', 'team_member_add_view', 'team_member_edit_view',
    # 'team_member_delete_view', 'team_member_toggle_status_view',
]
