"""
Custom Allauth Views for Role-Based Email Verification

This module provides custom views that extend django-allauth views to handle
different email verification requirements based on user roles.
"""

from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from allauth.account.views import SignupView as AllauthSignupView
from allauth.account.views import LoginView as AllauthLoginView
from allauth.account.views import LogoutView as AllauthLogoutView
from allauth.account.models import EmailAddress
import logging

from ..constants import UserRoles
from ..utils.logging import log_authentication_event

# Set up logging
logger = logging.getLogger(__name__)


class CozyWishSignupView(AllauthSignupView):
    """
    Custom signup view that handles role-based email verification.
    
    - Customers: No email verification required, redirect to home
    - Service Providers: Email verification required, follow allauth flow
    """
    
    def save_user(self, request, user, form, commit=True):
        """
        Save the user and handle role-based email verification.
        
        For customers: Skip email verification entirely
        For service providers: Follow normal allauth flow
        """
        user = super().save_user(request, user, form, commit=False)
        
        # Determine user role
        role = self._determine_user_role(request, form)
        user.role = role
        
        if commit:
            user.save()
        
        # For customers, handle email verification and welcome email after user is saved
        if user.role == UserRoles.CUSTOMER:
            try:
                # Send welcome email immediately (don't wait for EmailAddress)
                self._send_welcome_email(user)
                
                # Log successful customer signup
                log_authentication_event(
                    event_type='customer_signup_completed',
                    user_email=user.email,
                    success=True,
                    request=request,
                    additional_data={
                        'role': user.role,
                        'email_verification_skipped': True
                    }
                )
                
                # Add success message
                messages.success(
                    request,
                    _("Welcome to CozyWish! Your account has been created successfully.")
                )
                
            except Exception as e:
                logger.error(f"Error in customer signup handling: {str(e)}", exc_info=True)
        
        return user
    
    def _determine_user_role(self, request, form):
        """Determine user role from form or request context."""
        if form and hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
                return role
        
        # Check request parameters
        role = request.GET.get('role') or request.POST.get('role')
        if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
            return role
        
        # Analyze URL path
        path = request.path.lower()
        if 'provider' in path or 'business' in path:
            return UserRoles.SERVICE_PROVIDER
        
        # Default to customer
        return UserRoles.CUSTOMER
    
    def form_valid(self, form):
        """Handle form validation and role-based verification."""
        # Call parent method to create user and handle login
        response = super().form_valid(form)
        
        # Get the created user
        user = self.user
        
        # Send welcome email for customers (ensure it's sent)
        if user.role == UserRoles.CUSTOMER:
            try:
                self._send_welcome_email(user)
                logger.info(f"Welcome email sent in form_valid for customer: {user.email}")
            except Exception as e:
                logger.warning(f"Failed to send welcome email in form_valid: {str(e)}")
        
        # Check if email verification is required based on role
        if user.role == UserRoles.CUSTOMER:
            # For customers, user is already logged in by allauth
            # Ensure user is logged in manually if needed
            from django.contrib.auth import login
            if not self.request.user.is_authenticated:
                # Specify the backend explicitly for allauth
                login(self.request, user, backend='allauth.account.auth_backends.AuthenticationBackend')
            
            # For customers, skip email verification and redirect to home
            return redirect('home_app:home')  # Redirect to home page
        else:
            # For service providers, follow normal allauth flow
            return response
    
    def _send_welcome_email(self, user):
        """Send welcome email to the newly registered customer."""
        try:
            from utility_app.email_utils import send_welcome_email
            user_name = user.get_full_name() or user.email.split('@')[0]
            send_welcome_email(user.email, user_name, 'customer')
            logger.info(f"Welcome email sent to customer: {user.email}")
        except Exception as e:
            logger.warning(f"Failed to send welcome email to customer {user.email}: {str(e)}")


class CozyWishLoginView(AllauthLoginView):
    """
    Custom login view that handles role-based redirects.
    """
    template_name = 'allauth/account/login.html'

    def get_success_url(self):
        """Redirect users based on their role after login."""
        user = self.request.user
        
        # Check if user is authenticated and has a role attribute
        if user.is_authenticated and hasattr(user, 'role'):
            if user.role == UserRoles.CUSTOMER:
                return reverse('home_app:home')  # Home page for customers
            elif user.role == UserRoles.SERVICE_PROVIDER:
                return reverse('dashboard_app:provider_dashboard')  # Dashboard for service providers
            else:
                return reverse('home_app:home')  # Default to home page
        else:
            # Fallback for anonymous users or users without role
            return reverse('home_app:home')  # Default to home page


class CozyWishLogoutView(AllauthLogoutView):
    """
    Custom logout view with confirmation page that doesn't show user-specific information.
    """
    
    def get_context_data(self, **kwargs):
        """Override context to remove user-specific information from confirmation page."""
        context = super().get_context_data(**kwargs)
        
        # Remove any user-specific information that might be cached
        if 'user' in context:
            # Keep only essential info, remove personal details
            user = context['user']
            if hasattr(user, 'email'):
                # Only show a generic message, not the actual email
                context['user_email'] = 'your account'
            else:
                context['user_email'] = 'your account'
        
        # Ensure no personal information is passed to template
        context['show_user_info'] = False
        
        return context
    
    def get(self, request, *args, **kwargs):
        """Handle GET request for logout confirmation."""
        # Log the logout attempt
        if request.user.is_authenticated:
            log_authentication_event(
                event_type='logout_confirmation_viewed',
                user_email=request.user.email,
                success=True,
                request=request,
                additional_data={
                    'role': getattr(request.user, 'role', 'unknown'),
                    'logout_method': 'confirmation_page'
                }
            )
            logger.info(f"Logout confirmation page viewed by: {request.user.email}")
        
        return super().get(request, *args, **kwargs)
    
    def post(self, request, *args, **kwargs):
        """Handle POST request to confirm logout."""
        # Log the logout event
        if request.user.is_authenticated:
            log_authentication_event(
                event_type='user_logout',
                user_email=request.user.email,
                success=True,
                request=request,
                additional_data={
                    'role': getattr(request.user, 'role', 'unknown'),
                    'logout_method': 'form_submission'
                }
            )
            logger.info(f"User logged out via form: {request.user.email}")
        
        return super().post(request, *args, **kwargs) 