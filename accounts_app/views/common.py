# --- Third-Party Imports ---
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.http import HttpResponse

# --- Local App Imports ---
from ..models import LoginHistory, CustomUser, EmailVerificationToken
from ..services import EmailService
from ..utils.logging import (
    log_authentication_event, 
    log_error, 
    performance_monitor
)


# --- Logging Configuration ---
import logging
logger = logging.getLogger(__name__)



# --- User-facing messages (translatable) ---
MESSAGES = {
    'logout_success': _("You have been logged out successfully."),
    'login_success': _("Successfully logged in!"),
    'login_error': _("Invalid email or password. Please try again."),
    'signup_success': _("Welcome to CozyWish! Your account has been created successfully."),
    'signup_success_console': _("Welcome to CozyWish! Your account has been created successfully."),
    'signup_success_email': _("Welcome to CozyWish! Your account has been created successfully. Please check your email at %(email)s for verification instructions."),
    'signup_error': _("There was an error creating your account. Please try again."),
    'profile_update': _("Your profile has been updated successfully."),
    'password_change': _("Your password has been changed successfully. Please log in again."),
    'password_change_error': _("There was an error changing your password. Please try again."),
    'account_deactivated': _("Your account has been deactivated. Your data will remain stored securely."),
    'deactivation_error': _("There was an error deactivating your account. Please try again."),
    'business_profile_update': _("Your business profile has been updated successfully."),
    'provider_signup': _("Your account has been created! Please check your email to verify your account."),
    'email_verified': _("Your email has been verified successfully! You can now log in."),
    'verification_link_invalid': _("The verification link is invalid or has expired. Please request a new one."),
}



# --- Utility Functions ---

def get_client_ip(request) -> str:
    """Retrieve the client's IP address from request metadata"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ips = [ip.strip() for ip in x_forwarded_for.split(',')]
        return ips[0] if ips else ''
    return request.META.get('REMOTE_ADDR', '127.0.0.1')



@performance_monitor("login_attempt_recording")
def record_login_attempt(user, request, success: bool = True) -> None:
    """Record authentication attempts and detect suspicious activity
    
    Args:
        user: Authenticating user object
        request: Current HTTP request
        success: Whether authentication succeeded
    """
    try:
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create login history record
        LoginHistory.objects.create(
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            is_successful=success
        )
        
        # Log authentication event
        log_authentication_event(
            event_type='login_attempt',
            user_email=user.email,
            success=success,
            request=request,
            failure_reason=None if success else 'authentication_failed'
        )
        
        # Check for suspicious activity after failed attempts
        if not success:
            if LoginHistory.detect_suspicious_activity(ip_address, user):
                log_authentication_event(
                    event_type='suspicious_activity_detected',
                    user_email=user.email,
                    success=False,
                    request=request,
                    failure_reason='multiple_failed_attempts',
                    additional_data={'ip_address': ip_address}
                )
                
    except Exception as error:
        log_error(
            error_type='login_tracking',
            error_message="Failed to record login attempt",
            user=user,
            request=request,
            exception=error
        )




# --- Business Landing Page ---

def business_landing_view(request):
    """Display the service provider onboarding and benefits page"""
    return render(request, 'home_app/business_landing.html')


# --- Email Verification Views ---

@require_http_methods(["GET"])
def email_verification_view(request, token):
    """
    Handle email verification using token.
    
    Args:
        request: HTTP request object
        token: Email verification token
        
    Returns:
        HttpResponse: Verification result page
    """
    try:
        # Verify the token
        is_valid, token_obj, message = EmailVerificationToken.verify_token(token)
        
        if is_valid and token_obj:
            # Mark token as used
            token_obj.mark_as_used()
            
            # Verify the user's email
            user = token_obj.user
            user.email_verified = True
            user.save()
            
            # Log successful verification
            log_authentication_event(
                event_type='email_verified',
                user_email=user.email,
                success=True,
                request=request,
                additional_data={'token_id': token_obj.id}
            )
            
            messages.success(request, MESSAGES['email_verified'])
            logger.info(f"Email verified successfully for user: {user.email}")
            
            # Redirect based on user role
            if user.is_customer:
                return redirect('accounts_app:customer_login')
            elif user.is_service_provider:
                return redirect('accounts_app:service_provider_login')
            else:
                return redirect('accounts_app:customer_login')
                
        else:
            # Token is invalid or expired
            messages.error(request, MESSAGES['verification_link_invalid'])
            logger.warning(f"Invalid verification token attempted: {token}")
            
            context = {
                'message': message,
                'token': token,
                'can_resend': token_obj is not None and token_obj.user is not None,
                'user': token_obj.user if token_obj else None
            }
            
            return render(request, 'accounts_app/email_verification_failed.html', context)
            
    except Exception as e:
        logger.error(f"Email verification error: {str(e)}", exc_info=True)
        messages.error(request, _("An error occurred during email verification. Please try again."))
        return render(request, 'accounts_app/email_verification_failed.html')


@require_http_methods(["GET", "POST"])
@csrf_protect
def resend_verification_email_view(request):
    """
    Resend verification email to user.
    
    Args:
        request: HTTP request object
        
    Returns:
        HttpResponse: Resend result page
    """
    if request.method == "GET":
        return render(request, 'accounts_app/resend_verification.html')
    
    elif request.method == "POST":
        email = request.POST.get('email', '').strip().lower()
        
        if not email:
            messages.error(request, _("Please enter your email address."))
            return render(request, 'accounts_app/resend_verification.html')
        
        try:
            user = CustomUser.objects.get(email=email)
            
            # Check if user is already verified
            if user.email_verified:
                messages.info(request, _("Your email address is already verified."))
                return redirect('accounts_app:customer_login')
            
            # Send verification email
            success, message = EmailService.send_verification_email(user, request)
            
            if success:
                messages.success(request, _("Verification email sent successfully. Please check your email."))
                logger.info(f"Verification email resent to: {email}")
            else:
                messages.error(request, _("Failed to send verification email. Please try again."))
                logger.error(f"Failed to resend verification email to: {email}")
            
            return render(request, 'accounts_app/resend_verification.html')
            
        except CustomUser.DoesNotExist:
            # Don't reveal that the email doesn't exist for security
            messages.success(request, _("If an account with this email exists, a verification email has been sent."))
            return render(request, 'accounts_app/resend_verification.html')
            
        except Exception as e:
            logger.error(f"Resend verification error: {str(e)}", exc_info=True)
            messages.error(request, _("An error occurred. Please try again."))
            return render(request, 'accounts_app/resend_verification.html')


@require_http_methods(["GET"])
def email_verification_success_view(request):
    """
    Display email verification success page.
    
    Args:
        request: HTTP request object
        
    Returns:
        HttpResponse: Success page
    """
    return render(request, 'accounts_app/email_verification_success.html')


@require_http_methods(["GET"])
def email_verification_sent_view(request):
    """
    Display page confirming verification email was sent.
    
    Args:
        request: HTTP request object
        
    Returns:
        HttpResponse: Confirmation page
    """
    return render(request, 'accounts_app/email_verification_sent.html')
