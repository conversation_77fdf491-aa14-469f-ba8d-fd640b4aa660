# --- Third-Party Imports ---
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse, reverse_lazy
from django.views.generic import DetailView, UpdateView
from django.utils.translation import gettext_lazy as _
import logging

# --- Local App Imports ---
from ...decorators import ServiceProviderRequiredMixin
from ...forms import ServiceProviderProfileForm
from ...utils.logging import log_error
from ...models import ServiceProviderProfile
from ...services import ProfileService, ProfileError
from ..common import MESSAGES

# Provider-specific logger
provider_logger = logging.getLogger('accounts_app.provider')


# --- Service Provider Profile Management ---

class ServiceProviderProfileView(ServiceProviderRequiredMixin, DetailView):
    """
    Display service provider profile with comprehensive error handling.
    """
    model = ServiceProviderProfile
    template_name = 'accounts_app/provider/profile.html'
    context_object_name = 'profile'

    def get_object(self, queryset=None):
        """Get or create service provider profile with error handling"""
        try:
            profile, created = ServiceProviderProfile.objects.get_or_create(user=self.request.user)
            if created:
                provider_logger.info(f"Service provider profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving service provider profile",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            provider_logger.error(f"Service provider profile retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object to prevent template errors
            return ServiceProviderProfile(user=self.request.user)


class ServiceProviderProfileEditView(ServiceProviderRequiredMixin, UpdateView):
    """
    Handle service provider profile updates using ProfileService.
    """
    model = ServiceProviderProfile
    form_class = ServiceProviderProfileForm
    template_name = 'accounts_app/provider/profile_edit.html'
    success_url = reverse_lazy('accounts_app:service_provider_profile')

    def get_object(self, queryset=None):
        """Get or create service provider profile with error handling"""
        try:
            profile, created = ServiceProviderProfile.objects.get_or_create(user=self.request.user)
            if created:
                provider_logger.info(f"Service provider profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving service provider profile for edit",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            provider_logger.error(f"Service provider profile edit retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object
            return ServiceProviderProfile(user=self.request.user)

    def form_valid(self, form):
        """Handle valid form submission using ProfileService"""
        try:
            # Prepare profile data for service
            profile_data = form.cleaned_data
            
            # Use service to update profile
            success, message = ProfileService.update_service_provider_profile(
                user=self.request.user,
                profile_data=profile_data,
                request=self.request
            )
            
            if success:
                messages.success(self.request, MESSAGES['business_profile_update'])
                provider_logger.info(f"Service provider profile updated successfully: {self.request.user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message)
                provider_logger.error(f"Service provider profile update failed: {self.request.user.email}")
                return self.form_invalid(form)
                
        except ProfileError as e:
            messages.error(self.request, str(e))
            provider_logger.error(f"Service provider profile update error: {str(e)}")
            return self.form_invalid(form)
        except Exception as e:
            log_error(
                error_type='profile_update',
                error_message="Unexpected error during service provider profile update",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            messages.error(self.request, _("Profile update failed. Please try again."))
            provider_logger.error(f"Service provider profile update error: {str(e)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider profile form validation failed",
                user=self.request.user,
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider profile form validation failed: {form.errors}")
        
        return super().form_invalid(form) 