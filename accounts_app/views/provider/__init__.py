"""
Provider views module with organized sub-modules.

This file imports all provider views from their respective sub-modules
to maintain backward compatibility with existing imports.
"""

# Import authentication views
from .auth import (
    ServiceProviderSignupView,
    provider_signup_done_view,
    provider_email_verify_view,
)

# Import profile management views
from .profile import (
    ServiceProviderProfileView,
    ServiceProviderProfileEditView,
)

# Import password management views
from .password import (
    service_provider_change_password_view,
    ServiceProviderPasswordResetView,
    ServiceProviderPasswordResetDoneView,
    ServiceProviderPasswordResetConfirmView,
    ServiceProviderPasswordResetCompleteView,
)

# Import account management views
from .account import (
    service_provider_deactivate_account_view,
)

# Import team management views
from .team import (
    service_provider_team_list_view,
    service_provider_team_add_view,
    service_provider_team_edit_view,
    service_provider_team_delete_view,
)

# Backward compatibility exports
__all__ = [
    # Authentication views
    'ServiceProviderSignupView',
    'provider_signup_done_view',
    'provider_email_verify_view',
    
    # Profile management views
    'ServiceProviderProfileView',
    'ServiceProviderProfileEditView',
    
    # Password management views
    'service_provider_change_password_view',
    'ServiceProviderPasswordResetView',
    'ServiceProviderPasswordResetDoneView',
    'ServiceProviderPasswordResetConfirmView',
    'ServiceProviderPasswordResetCompleteView',
    
    # Account management views
    'service_provider_deactivate_account_view',
    
    # Team management views
    'service_provider_team_list_view',
    'service_provider_team_add_view',
    'service_provider_team_edit_view',
    'service_provider_team_delete_view',
] 