"""
Custom Allauth package for CozyWish.

This package provides custom allauth adapters and forms for role-based user creation.
"""

# Import adapters for backward compatibility
from .adapters.account_adapter import CozyWishAccountAdapter
from .adapters.social_adapter import CozyWishSocialAccountAdapter

# Import forms for backward compatibility
from .forms.signup_forms import CozyWishSignupForm, CozyWishSocialSignupForm
from .forms.login_forms import CozyWishLoginForm
from .forms.password_forms import CozyWishPasswordChangeForm, CozyWishPasswordResetForm

__all__ = [
    'CozyWishAccountAdapter',
    'CozyWishSocialAccountAdapter',
    'CozyWishSignupForm',
    'CozyWishSocialSignupForm',
    'CozyWishLoginForm',
    'CozyWishPasswordChangeForm',
    'CozyWishPasswordResetForm',
] 