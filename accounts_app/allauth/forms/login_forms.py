"""
Login Forms for Allauth Integration

This module contains custom login forms that extend django-allauth forms:
- CozyWishLoginForm: Custom login form with enhanced styling and accessibility
"""

# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, HTML, Submit
from crispy_forms.bootstrap import FormActions

# --- Allauth Imports ---
from allauth.account import forms as allauth_forms

# --- Local App Imports ---
from ..forms.common import AccessibleFormMixin


class CozyWishLoginForm(AccessibleFormMixin, allauth_forms.LoginForm):
    """
    Custom allauth login form with enhanced styling and accessibility.
    
    This form extends allauth's LoginForm while maintaining all existing
    functionality and adding improved styling and accessibility features.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper and enhanced styling."""
        super().__init__(*args, **kwargs)
        
        # Ensure email field has proper attributes
        self.fields['login'].widget.attrs.update({
            'placeholder': _('Enter your email'),
            'autocomplete': 'email',
            'class': 'form-control',
            'maxlength': '254',
        })
        self.fields['login'].help_text = _('Enter your email address')
        
        # Ensure password field has proper attributes
        self.fields['password'].widget.attrs.update({
            'placeholder': _('Enter your password'),
            'autocomplete': 'current-password',
            'class': 'form-control',
            'minlength': '12',
        })
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3">Sign In</h4>'),
            Field('login', css_class='form-control'),
            Field('password', css_class='form-control'),
            Field('remember', css_class='form-check-input'),
            FormActions(
                Submit('login', _('Sign In'), css_class='btn-primary btn-lg'),
                css_class='d-grid gap-2 mt-4'
            )
        ) 