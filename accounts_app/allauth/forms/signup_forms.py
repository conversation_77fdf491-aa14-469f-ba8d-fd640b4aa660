"""
Custom Allauth Signup Forms for Role-Based User Creation in CozyWish

This module provides custom signup forms that extend django-allauth forms while
maintaining all existing functionality from accounts_app/forms/.
"""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit, Row, Column, Fieldset
from crispy_forms.bootstrap import FormActions

# --- Allauth Imports ---
from allauth.account import forms as allauth_forms
from allauth.socialaccount import forms as social_forms
from allauth.socialaccount.models import SocialLogin

# --- Local App Imports ---
from ..fields import PhoneNumberField
from ...models import CustomUser
from ...constants import UserRoles, Gender, USStates, Months
from ..forms.common import AccessibleFormMixin
from ...mixins import (
    EmailValidationMixin, 
    PasswordValidationMixin, 
    PhoneValidationMixin, 
    ImageValidationMixin,
    BusinessValidationMixin,
    URLValidationMixin,
    TermsValidationMixin
)
from ...utils.validators import normalize_phone
from ...utils.logging import log_user_activity


class CozyWishSignupForm(
    AccessibleFormMixin, 
    EmailValidationMixin, 
    PasswordValidationMixin, 
    PhoneValidationMixin,
    BusinessValidationMixin,
    TermsValidationMixin,
    allauth_forms.SignupForm
):
    """
    Custom allauth signup form with role-based registration.
    
    This form extends allauth's SignupForm while maintaining all existing
    functionality from CustomerSignupForm and ServiceProviderSignupForm.
    
    Features:
    - Role selection (customer/service_provider)
    - Dynamic field display based on role
    - All existing validation logic
    - Accessible form styling
    - Business fields for service providers
    - Profile creation integration
    """
    
    # Role selection field
    role = forms.ChoiceField(
        label=_('Account Type'),
        choices=[
            (UserRoles.CUSTOMER, _('Customer - Book events and services')),
            (UserRoles.SERVICE_PROVIDER, _('Service Provider - Offer events and services')),
        ],
        initial=UserRoles.CUSTOMER,
        required=False,  # Make optional since URL determines role
        widget=forms.RadioSelect(
            attrs={
                'class': 'form-check-input',
                'onchange': 'toggleBusinessFields(this.value)',
            }
        ),
        help_text=_('Choose the type of account you want to create')
    )
    
    # Personal information fields (for both roles)
    first_name = forms.CharField(
        label=_('First Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your first name'),
                'autocomplete': 'given-name',
                'class': 'form-control',
            }
        )
    )
    
    last_name = forms.CharField(
        label=_('Last Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your last name'),
                'autocomplete': 'family-name',
                'class': 'form-control',
            }
        )
    )
    
    phone_number = PhoneNumberField(
        label=_('Phone Number'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': '+****************',
                'type': 'tel',
                'autocomplete': 'tel',
                'class': 'form-control',
            }
        ),
        help_text=_('Format: +****************')
    )
    
    # Customer-specific fields
    gender = forms.ChoiceField(
        label=_('Gender'),
        choices=[('', _('Select Gender'))] + list(Gender.CHOICES),
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-select customer-field',
            }
        )
    )
    
    birth_month = forms.ChoiceField(
        label=_('Birth Month'),
        choices=[('', _('Select Month'))] + [
            (i, _(['January', 'February', 'March', 'April', 'May', 'June',
                  'July', 'August', 'September', 'October', 'November', 'December'][i-1])) 
            for i in range(1, 13)
        ],
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-select customer-field',
            }
        )
    )
    
    birth_year = forms.ChoiceField(
        label=_('Birth Year'),
        choices=[('', _('Select Year'))] + [
            (year, str(year)) for year in range(1920, 2010)
        ],
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-select customer-field',
            }
        )
    )
    
    # Address fields (for both roles)
    address = forms.CharField(
        label=_('Address'),
        max_length=255,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your address'),
                'autocomplete': 'street-address',
                'class': 'form-control',
            }
        )
    )
    
    city = forms.CharField(
        label=_('City'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter city'),
                'autocomplete': 'address-level2',
                'class': 'form-control',
            }
        )
    )
    
    state = forms.ChoiceField(
        label=_('State'),
        choices=[('', _('Select State'))] + list(USStates.CHOICES),
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-select',
                'autocomplete': 'address-level1',
            }
        )
    )
    
    zip_code = forms.CharField(
        label=_('ZIP Code'),
        max_length=10,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter ZIP code'),
                'autocomplete': 'postal-code',
                'class': 'form-control',
            }
        )
    )
    
    # Business-specific fields (for service providers)
    business_name = forms.CharField(
        label=_('Business Name'),
        max_length=200,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your official business name'),
                'class': 'form-control provider-field',
                'minlength': '2',
            }
        ),
        help_text=_('Official registered business name')
    )

    contact_person_name = forms.CharField(
        label=_('Contact Person Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter the primary contact person name'),
                'class': 'form-control provider-field',
                'minlength': '2',
            }
        ),
        help_text=_('Name of the primary contact person for the business')
    )

    ein = forms.CharField(
        label=_('EIN (Tax ID)'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('XX-XXXXXXX (optional)'),
                'class': 'form-control provider-field',
            }
        ),
        help_text=_('Employer Identification Number (optional)')
    )
    
    website = forms.URLField(
        label=_('Website'),
        required=False,
        widget=forms.URLInput(
            attrs={
                'placeholder': _('https://www.yourbusiness.com'),
                'class': 'form-control provider-field',
            }
        ),
        help_text=_('Your business website (optional)')
    )
    
    description = forms.CharField(
        label=_('Business Description'),
        max_length=500,
        required=False,
        widget=forms.Textarea(
            attrs={
                'placeholder': _('Brief description of your services (500 characters max)'),
                'class': 'form-control provider-field',
                'rows': 3,
                'maxlength': '500',
            }
        ),
        help_text=_('Brief overview of services offered (500 characters max)')
    )
    
    # Terms agreement
    agree_to_terms = forms.BooleanField(
        label=_('I agree to the Terms of Service and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': 'required',
            }
        )
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set up form helper for crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-2'
        self.helper.field_class = 'col-lg-8'
        
        # Create form layout
        self.helper.layout = Layout(
            # Role selection
            Fieldset(
                _('Account Type'),
                Field('role', css_class='form-check-inline'),
                css_class='mb-4'
            ),
            
            # Personal information
            Fieldset(
                _('Personal Information'),
                Row(
                    Column('first_name', css_class='form-group col-md-6'),
                    Column('last_name', css_class='form-group col-md-6'),
                    css_class='form-row'
                ),
                'phone_number',
                Row(
                    Column('gender', css_class='form-group col-md-4 customer-field'),
                    Column('birth_month', css_class='form-group col-md-4 customer-field'),
                    Column('birth_year', css_class='form-group col-md-4 customer-field'),
                    css_class='form-row'
                ),
                css_class='mb-4'
            ),
            
            # Address information
            Fieldset(
                _('Address Information'),
                'address',
                Row(
                    Column('city', css_class='form-group col-md-6'),
                    Column('state', css_class='form-group col-md-3'),
                    Column('zip_code', css_class='form-group col-md-3'),
                    css_class='form-row'
                ),
                css_class='mb-4'
            ),
            
            # Business information (for service providers)
            Fieldset(
                _('Business Information'),
                'business_name',
                'contact_person_name',
                Row(
                    Column('ein', css_class='form-group col-md-6'),
                    Column('website', css_class='form-group col-md-6'),
                    css_class='form-row'
                ),
                'description',
                css_class='mb-4 provider-field',
                css_id='business-fields'
            ),
            
            # Account credentials
            Fieldset(
                _('Account Credentials'),
                'email',
                'password1',
                'password2',
                css_class='mb-4'
            ),
            
            # Terms agreement
            Fieldset(
                _('Terms & Conditions'),
                Field('agree_to_terms', css_class='form-check-input'),
                css_class='mb-4'
            ),
            
            # Submit button
            FormActions(
                Submit('submit', _('Create Account'), css_class='btn btn-primary btn-lg'),
                css_class='text-center'
            )
        )
        
        # Add JavaScript for dynamic field visibility
        self.helper.layout.append(
            HTML("""
            <script>
            function toggleBusinessFields(role) {
                const businessFields = document.getElementById('business-fields');
                const customerFields = document.querySelectorAll('.customer-field');
                
                if (role === 'service_provider') {
                    businessFields.style.display = 'block';
                    customerFields.forEach(field => {
                        field.closest('.form-group').style.display = 'none';
                    });
                } else {
                    businessFields.style.display = 'none';
                    customerFields.forEach(field => {
                        field.closest('.form-group').style.display = 'block';
                    });
                }
            }
            
            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                const roleField = document.querySelector('input[name="role"]:checked');
                if (roleField) {
                    toggleBusinessFields(roleField.value);
                }
            });
            </script>
            """)
        )
    
    def clean_role(self):
        """Validate role selection."""
        role = self.cleaned_data.get('role')
        if not role:
            # Determine role from URL if not provided
            if hasattr(self, 'request') and self.request:
                path = self.request.path.lower()
                if 'provider' in path or 'business' in path:
                    return UserRoles.SERVICE_PROVIDER
                elif 'customer' in path:
                    return UserRoles.CUSTOMER
        return role or UserRoles.CUSTOMER
    
    def clean(self):
        """Custom validation for the entire form."""
        cleaned_data = super().clean()
        role = cleaned_data.get('role', UserRoles.CUSTOMER)
        
        # Role-specific validation
        if role == UserRoles.SERVICE_PROVIDER:
            # Service providers need business information
            if not cleaned_data.get('business_name'):
                self.add_error('business_name', _('Business name is required for service providers.'))
            if not cleaned_data.get('contact_person_name'):
                self.add_error('contact_person_name', _('Contact person name is required for service providers.'))
        
        return cleaned_data
    
    def clean_phone_number(self):
        """Validate and normalize phone number."""
        phone = self.cleaned_data.get('phone_number')
        if phone:
            try:
                return normalize_phone(phone)
            except ValidationError as e:
                raise ValidationError(_('Please enter a valid phone number.'))
        return phone
    
    def clean_agree_to_terms(self):
        """Validate terms agreement."""
        agree = self.cleaned_data.get('agree_to_terms')
        if not agree:
            raise ValidationError(_('You must agree to the Terms of Service and Privacy Policy.'))
        return agree
    
    def clean_password2(self):
        """Custom password validation."""
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        
        if password1 and password2 and password1 != password2:
            raise ValidationError(_('Passwords do not match.'))
        
        # Additional password strength validation
        if password1:
            if len(password1) < 8:
                raise ValidationError(_('Password must be at least 8 characters long.'))
            if not any(c.isupper() for c in password1):
                raise ValidationError(_('Password must contain at least one uppercase letter.'))
            if not any(c.islower() for c in password1):
                raise ValidationError(_('Password must contain at least one lowercase letter.'))
            if not any(c.isdigit() for c in password1):
                raise ValidationError(_('Password must contain at least one number.'))
        
        return password2
    
    def save(self, request):
        """Save the user with role-based profile creation."""
        user = super().save(request)
        
        # Log the signup event
        log_user_activity(
            user=user,
            activity_type='signup',
            details={
                'role': self.cleaned_data.get('role'),
                'method': 'email'
            }
        )
        
        return user


class CozyWishSocialSignupForm(AccessibleFormMixin, social_forms.SignupForm):
    """
    Custom allauth social signup form with role selection.
    
    This form extends allauth's social SignupForm to add role selection
    and maintain existing styling and accessibility features.
    """
    
    role = forms.ChoiceField(
        label=_('Account Type'),
        choices=[
            (UserRoles.CUSTOMER, _('Customer - Book events and services')),
            (UserRoles.SERVICE_PROVIDER, _('Service Provider - Offer events and services')),
        ],
        initial=UserRoles.CUSTOMER,
        required=False,
        widget=forms.RadioSelect(
            attrs={
                'class': 'form-check-input',
                'onchange': 'toggleBusinessFields(this.value)',
            }
        ),
        help_text=_('Choose the type of account you want to create')
    )
    
    # Additional fields for social signup
    phone_number = PhoneNumberField(
        label=_('Phone Number'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': '+****************',
                'type': 'tel',
                'autocomplete': 'tel',
                'class': 'form-control',
            }
        ),
        help_text=_('Format: +****************')
    )
    
    # Business fields for service providers
    business_name = forms.CharField(
        label=_('Business Name'),
        max_length=200,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your official business name'),
                'class': 'form-control provider-field',
            }
        ),
        help_text=_('Official registered business name')
    )
    
    contact_person_name = forms.CharField(
        label=_('Contact Person Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter the primary contact person name'),
                'class': 'form-control provider-field',
            }
        ),
        help_text=_('Name of the primary contact person for the business')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set up form helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-2'
        self.helper.field_class = 'col-lg-8'
        
        # Create form layout
        self.helper.layout = Layout(
            # Role selection
            Fieldset(
                _('Account Type'),
                Field('role', css_class='form-check-inline'),
                css_class='mb-4'
            ),
            
            # Additional information
            Fieldset(
                _('Additional Information'),
                'phone_number',
                css_class='mb-4'
            ),
            
            # Business information (for service providers)
            Fieldset(
                _('Business Information'),
                'business_name',
                'contact_person_name',
                css_class='mb-4 provider-field',
                css_id='business-fields'
            ),
            
            # Submit button
            FormActions(
                Submit('submit', _('Complete Registration'), css_class='btn btn-primary btn-lg'),
                css_class='text-center'
            )
        )
        
        # Add JavaScript for dynamic field visibility
        self.helper.layout.append(
            HTML("""
            <script>
            function toggleBusinessFields(role) {
                const businessFields = document.getElementById('business-fields');
                if (role === 'service_provider') {
                    businessFields.style.display = 'block';
                } else {
                    businessFields.style.display = 'none';
                }
            }
            
            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                const roleField = document.querySelector('input[name="role"]:checked');
                if (roleField) {
                    toggleBusinessFields(roleField.value);
                }
            });
            </script>
            """)
        )
    
    def clean_role(self):
        """Validate role selection."""
        role = self.cleaned_data.get('role')
        if not role:
            # Determine role from URL if not provided
            if hasattr(self, 'request') and self.request:
                path = self.request.path.lower()
                if 'provider' in path or 'business' in path:
                    return UserRoles.SERVICE_PROVIDER
        return role or UserRoles.CUSTOMER
    
    def save(self, request):
        """Save the user with role-based profile creation."""
        user = super().save(request)
        
        # Log the social signup event
        log_user_activity(
            user=user,
            activity_type='social_signup',
            details={
                'role': self.cleaned_data.get('role'),
                'provider': getattr(self.sociallogin, 'account', {}).get('provider', 'unknown')
            }
        )
        
        return user 