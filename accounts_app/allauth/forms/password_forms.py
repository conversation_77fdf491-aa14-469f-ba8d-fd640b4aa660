"""
Password Forms for Allauth Integration

This module contains custom password forms that extend django-allauth forms:
- CozyWishPasswordChangeForm: Password change form with enhanced validation
- CozyWishPasswordResetForm: Password reset form with email validation
"""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, HTML, Submit, Row, Column
from crispy_forms.bootstrap import FormActions

# --- Allauth Imports ---
from allauth.account import forms as allauth_forms

# --- Local App Imports ---
from ...forms.common import AccessibleFormMixin
from ...mixins import PasswordValidationMixin, EmailValidationMixin


class CozyWishPasswordChangeForm(AccessibleFormMixin, PasswordValidationMixin, allauth_forms.ChangePasswordForm):
    """
    Custom allauth password change form with enhanced validation and styling.
    
    This form extends allauth's ChangePasswordForm while maintaining all existing
    functionality and adding improved validation and accessibility features.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper and enhanced styling."""
        super().__init__(*args, **kwargs)
        
        # Ensure password fields have proper attributes
        self.fields['oldpassword'].widget.attrs.update({
            'placeholder': _('Enter your current password'),
            'autocomplete': 'current-password',
            'class': 'form-control',
            'minlength': '12',
        })
        
        self.fields['password1'].widget.attrs.update({
            'placeholder': _('Enter new password'),
            'autocomplete': 'new-password',
            'class': 'form-control',
            'minlength': '12',
        })
        
        self.fields['password2'].widget.attrs.update({
            'placeholder': _('Confirm new password'),
            'autocomplete': 'new-password',
            'class': 'form-control',
            'minlength': '12',
        })
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3">Change Password</h4>'),
            Field('oldpassword', css_class='form-control'),
            Row(
                Column(
                    Field('password1', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('password2', css_class='form-control'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            FormActions(
                Submit('change_password', _('Change Password'), css_class='btn-primary btn-lg'),
                css_class='d-grid gap-2 mt-4'
            )
        )


class CozyWishPasswordResetForm(AccessibleFormMixin, EmailValidationMixin, allauth_forms.ResetPasswordForm):
    """
    Custom allauth password reset form with enhanced email validation and styling.
    
    This form extends allauth's ResetPasswordForm while maintaining all existing
    functionality and adding improved email validation and accessibility features.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper and enhanced styling."""
        super().__init__(*args, **kwargs)
        
        # Ensure email field has proper attributes
        self.fields['email'].widget.attrs.update({
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email',
            'class': 'form-control',
            'maxlength': '254',
        })
        self.fields['email'].help_text = _('Enter the email address associated with your account')
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3">Reset Password</h4>'),
            HTML('<p class="text-muted mb-3">Enter your email address and we\'ll send you a link to reset your password.</p>'),
            Field('email', css_class='form-control'),
            FormActions(
                Submit('reset_password', _('Send Reset Link'), css_class='btn-primary btn-lg'),
                css_class='d-grid gap-2 mt-4'
            )
        )
    
    def clean_email(self):
        """
        Enhanced email validation for password reset.
        
        This method extends the base email validation with additional
        checks specific to password reset functionality.
        """
        email = super().clean_email()
        
        # Additional validation can be added here if needed
        # For example, checking if the email exists in the system
        
        return email 