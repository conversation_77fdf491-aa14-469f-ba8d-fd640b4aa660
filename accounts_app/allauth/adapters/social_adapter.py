"""
Custom social account adapter for role-based social authentication.

This adapter extends DefaultSocialAccountAdapter to provide:
- Role assignment for social signups
- Profile creation from social account data
- Integration with existing business logic
- Enhanced social account validation
"""

import logging
from typing import Dict, Any, Optional

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from django.shortcuts import render

from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialAccount
from allauth.exceptions import ImmediateHttpResponse

# Local imports
from ...constants import UserRoles, UserStatus
from ...models import (
    CustomUser, CustomerProfile, ServiceProviderProfile,
    UserPreferences, UserSecurity, EmailPreferences,
    ProfilePrivacySettings
)
from ...utils.validators import normalize_phone
from ...utils.logging import log_account_lifecycle_event


logger = logging.getLogger(__name__)


class CozyWishSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter for role-based social authentication.
    
    This adapter extends DefaultSocialAccountAdapter to provide:
    - Role assignment for social signups
    - Profile creation from social account data
    - Integration with existing business logic
    - Enhanced social account validation
    """
    
    def __init__(self, request=None):
        super().__init__(request)
        self.request = request
    
    def pre_social_login(self, request: HttpRequest, sociallogin) -> None:
        """
        Perform actions before social login/signup.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
        """
        # Check if social login is allowed
        if not self._is_social_login_allowed(request, sociallogin):
            raise ImmediateHttpResponse(
                response=self._render_social_login_error(
                    request, "Social login is currently disabled."
                )
            )
        
        super().pre_social_login(request, sociallogin)
    
    def save_user(self, request: HttpRequest, sociallogin, form=None) -> CustomUser:
        """
        Save user from social account with role-based profile creation.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            form: Additional form data if provided
            
        Returns:
            CustomUser: The created/updated user instance
        """
        try:
            with transaction.atomic():
                # Create user using parent method
                user = super().save_user(request, sociallogin, form)
                
                # Set role and status
                role = self._determine_social_user_role(request, sociallogin, form)
                user.role = role
                user.status = UserStatus.ACTIVE  # Social accounts are pre-verified
                user.email_verified = True  # Trust social provider email verification
                user.save()
                
                # Extract social account data
                social_data = self._extract_social_data(sociallogin)
                
                # Create profiles and records if this is a new user
                if not hasattr(user, 'customer_profile') and not hasattr(user, 'service_provider_profile'):
                    self._create_social_user_profile(user, role, social_data, request)
                    self._create_security_records(user)
                    self._create_preference_records(user)
                
                # Log social account creation/login
                log_account_lifecycle_event(
                    event_type='social_registration' if sociallogin.is_existing else 'social_login',
                    user=user,
                    request=request,
                    details={
                        'provider': sociallogin.account.provider,
                        'role': role,
                        'provider_uid': sociallogin.account.uid
                    }
                )
                
                logger.info(
                    f"Social user {'created' if not sociallogin.is_existing else 'logged in'}: "
                    f"{user.email} via {sociallogin.account.provider}"
                )
                
                return user
                
        except Exception as e:
            logger.error(f"Error saving social user: {str(e)}", exc_info=True)
            raise ValidationError(_("Social registration failed. Please try again."))
    
    def populate_user(self, request: HttpRequest, sociallogin, data: Dict[str, Any]) -> CustomUser:
        """
        Populate user instance with data from social provider.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            data: Data from social provider
            
        Returns:
            CustomUser: Populated user instance
        """
        user = super().populate_user(request, sociallogin, data)
        
        # Extract additional data from social account
        extra_data = sociallogin.account.extra_data
        
        # Set additional fields if available
        if 'first_name' in extra_data:
            user.first_name = extra_data['first_name']
        if 'last_name' in extra_data:
            user.last_name = extra_data['last_name']
        
        # Set role (will be refined in save_user)
        user.role = self._determine_social_user_role(request, sociallogin, None)
        
        return user
    
    def _determine_social_user_role(self, request: HttpRequest, sociallogin, form) -> str:
        """
        Determine user role for social account signup.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            form: Additional form data if provided
            
        Returns:
            str: User role
        """
        # Check form data first
        if form and hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
                return role
        
        # Check request parameters
        role = request.GET.get('role') or request.POST.get('role')
        if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
            return role
        
        # Analyze URL path
        path = request.path.lower()
        if 'provider' in path or 'business' in path:
            return UserRoles.SERVICE_PROVIDER
        
        # Default to customer for social signups
        return UserRoles.CUSTOMER
    
    def _extract_social_data(self, sociallogin) -> Dict[str, Any]:
        """
        Extract user data from social account.
        
        Args:
            sociallogin: SocialLogin instance
            
        Returns:
            Dict containing extracted social data
        """
        extra_data = sociallogin.account.extra_data
        data = {}
        
        # Common mappings for different providers
        field_mappings = {
            'google': {
                'first_name': 'given_name',
                'last_name': 'family_name',
                'profile_picture': 'picture',
                'phone_number': 'phone_number'
            },
            'facebook': {
                'first_name': 'first_name',
                'last_name': 'last_name',
                'profile_picture': 'picture.data.url',
                'phone_number': 'phone'
            },
            'linkedin': {
                'first_name': 'firstName',
                'last_name': 'lastName',
                'profile_picture': 'pictureUrl'
            }
        }
        
        provider = sociallogin.account.provider
        mappings = field_mappings.get(provider, {})
        
        for internal_field, external_field in mappings.items():
            # Handle nested fields (e.g., picture.data.url)
            if '.' in external_field:
                value = extra_data
                for key in external_field.split('.'):
                    value = value.get(key, {}) if isinstance(value, dict) else None
                    if value is None:
                        break
            else:
                value = extra_data.get(external_field)
            
            if value:
                data[internal_field] = value
        
        # Normalize phone number if present
        if 'phone_number' in data and data['phone_number']:
            try:
                data['phone_number'] = normalize_phone(data['phone_number'])
            except ValidationError:
                # Remove invalid phone number
                del data['phone_number']
        
        return data
    
    def _create_social_user_profile(self, user: CustomUser, role: str, data: Dict[str, Any], request: HttpRequest) -> None:
        """
        Create user profile from social account data.
        
        Args:
            user: CustomUser instance
            role: User role
            data: Social account data
            request: HTTP request object
        """
        if role == UserRoles.CUSTOMER:
            CustomerProfile.objects.create(
                user=user,
                first_name=data.get('first_name', user.first_name),
                last_name=data.get('last_name', user.last_name),
                phone_number=data.get('phone_number', ''),
                # Note: profile_picture handling would need additional logic for downloading/storing
            )
            
        elif role == UserRoles.SERVICE_PROVIDER:
            # For service providers via social, create minimal profile
            # They'll need to complete business information later
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name=f"{user.first_name} {user.last_name}".strip() or user.email,
                contact_name=f"{user.first_name} {user.last_name}".strip() or user.email,
                phone=data.get('phone_number', ''),
                # Other fields will be empty and need completion
                address='',
                city='',
                state='',
                zip_code=''
            )
    
    def _create_security_records(self, user: CustomUser) -> None:
        """Create security records for social user."""
        UserSecurity.objects.create(user=user)
    
    def _create_preference_records(self, user: CustomUser) -> None:
        """Create preference records for social user."""
        # Create user preferences
        UserPreferences.objects.create(user=user)
        
        # Create email preferences
        EmailPreferences.objects.create(
            user=user,
            email_notifications_enabled=True,
            account_notifications=True,
            booking_notifications=True,
            payment_notifications=True,
            marketing_emails=False,  # Conservative default for social users
            event_notifications=user.is_customer,
            review_notifications=True
        )
        
        # Create privacy settings
        privacy_settings = ProfilePrivacySettings.get_default_settings()
        ProfilePrivacySettings.objects.create(user=user, **privacy_settings)
    
    def _is_social_login_allowed(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social login is allowed.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            
        Returns:
            bool: True if social login is allowed
        """
        # Check global settings
        if hasattr(settings, 'SOCIALACCOUNT_ALLOW_REGISTRATION'):
            if not settings.SOCIALACCOUNT_ALLOW_REGISTRATION:
                return False
        
        # Provider-specific restrictions
        provider = sociallogin.account.provider
        disabled_providers = getattr(settings, 'DISABLED_SOCIAL_PROVIDERS', [])
        if provider in disabled_providers:
            return False
        
        return True
    
    def _render_social_login_error(self, request: HttpRequest, message: str):
        """
        Render social login error page.
        
        Args:
            request: HTTP request object
            message: Error message
            
        Returns:
            HttpResponse: Error page response
        """
        context = {
            'error_message': message,
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')
        }
        
        return render(request, 'account/social_login_error.html', context)
    
    def is_open_for_signup(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social signup is open.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            
        Returns:
            bool: True if social signup is open
        """
        if not super().is_open_for_signup(request, sociallogin):
            return False
        
        return self._is_social_login_allowed(request, sociallogin) 