"""
Custom Allauth adapters for CozyWish.

This package provides custom adapters for django-allauth that handle:
- Role-based user registration (customer vs service provider)
- Automatic profile creation based on user role
- Integration with existing business logic and security
- Social authentication with role assignment
"""

from .account_adapter import CozyWishAccountAdapter
from .social_adapter import CozyWishSocialAccountAdapter

__all__ = [
    'CozyWishAccountAdapter',
    'CozyWishSocialAccountAdapter',
] 