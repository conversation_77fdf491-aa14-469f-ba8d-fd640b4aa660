"""
Decorators package for accounts_app.

This package provides decorators for authentication and permission control
across the accounts_app.
"""

# Import all decorators from auth module for easy access
from .auth import (
    customer_required,
    service_provider_required,
    admin_required,
    role_required,
    anonymous_required,
    require_active_user,
    get_user_home_url,
    get_user_login_url,
    login_required_customer,
    login_required_provider,
    login_required_admin,
    email_verified_required,
    profile_complete_required,
    rate_limit,
    ajax_required,
    json_response,
    cache_control,
    permission_required_custom,
    two_factor_required,
    account_active_required,
)

# Import all mixins from auth module for easy access
from .auth import (
    CustomerRequiredMixin,
    ServiceProviderRequiredMixin,
    AdminRequiredMixin,
    RoleRequiredMixin,
    AnonymousRequiredMixin,
    ActiveUserRequiredMixin,
)

__all__ = [
    # Function-based decorators
    'customer_required',
    'service_provider_required',
    'admin_required',
    'role_required',
    'anonymous_required',
    'require_active_user',
    
    # Class-based mixins
    'CustomerRequiredMixin',
    'ServiceProviderRequiredMixin',
    'AdminRequiredMixin',
    'RoleRequiredMixin',
    'AnonymousRequiredMixin',
    'ActiveUserRequiredMixin',
    
    # Utility functions
    'get_user_home_url',
    'get_user_login_url',
    # Legacy alias decorators
    'login_required_customer',
    'login_required_provider',
    'login_required_admin',
    'email_verified_required',
    'profile_complete_required',
    'rate_limit',
    'ajax_required',
    'json_response',
    'cache_control',
    'permission_required_custom',
    'two_factor_required',
    'account_active_required',
] 