"""
Permission decorators and mixins for role-based access control.

This module provides decorators and mixins that enforce authentication and role-based
permissions for views in the accounts_app.
"""

from functools import wraps
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from ..constants import UserRoles, UserStatus


# --- Permission Decorators for Function-Based Views ---

def customer_required(view_func):
    """
    Decorator to require that the user is authenticated and is a customer.
    
    Usage:
        @customer_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:customer_login')
        
        if not request.user.is_customer:
            messages.error(request, _('Access denied. Customer access required.'))
            return redirect('home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


def service_provider_required(view_func):
    """
    Decorator to require that the user is authenticated and is a service provider.
    
    Usage:
        @service_provider_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:service_provider_login')
        
        if not request.user.is_service_provider:
            messages.error(request, _('Access denied. Service provider access required.'))
            return redirect('home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


def admin_required(view_func):
    """
    Decorator to require that the user is authenticated and is an admin.
    
    Usage:
        @admin_required
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:admin_login')
        
        if not request.user.is_admin:
            messages.error(request, _('Access denied. Administrator access required.'))
            return redirect('home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


def role_required(*allowed_roles):
    """
    Decorator to require that the user has one of the specified roles.
    
    Usage:
        @role_required(UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER)
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts_app:customer_login')
            
            if request.user.role not in allowed_roles:
                messages.error(request, _('Access denied. Insufficient permissions.'))
                return redirect('home')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def anonymous_required(view_func):
    """
    Decorator to require that the user is NOT authenticated.
    Redirects authenticated users to their appropriate home page.
    
    Usage:
        @anonymous_required
        def login_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.is_customer:
                return redirect('home')
            elif request.user.is_service_provider:
                return redirect('accounts_app:service_provider_profile')
            elif request.user.is_admin:
                return redirect('admin_app:dashboard')
            else:
                return redirect('home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


# --- Permission Mixins for Class-Based Views ---

class CustomerRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be a customer.
    
    Usage:
        class MyView(CustomerRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:customer_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_customer:
            messages.error(request, _('Access denied. Customer access required.'))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)


class ServiceProviderRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be a service provider.
    
    Usage:
        class MyView(ServiceProviderRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:service_provider_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_service_provider:
            messages.error(request, _('Access denied. Service provider access required.'))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to be authenticated and be an admin.
    
    Usage:
        class MyView(AdminRequiredMixin, DetailView):
            ...
    """
    login_url = reverse_lazy('accounts_app:admin_login')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_admin:
            messages.error(request, _('Access denied. Administrator access required.'))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)


class RoleRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires the user to have one of the specified roles.
    
    Usage:
        class MyView(RoleRequiredMixin, DetailView):
            allowed_roles = [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]
            ...
    """
    allowed_roles = []
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.allowed_roles:
            raise ValueError("allowed_roles must be defined in the view")
        
        if request.user.role not in self.allowed_roles:
            messages.error(request, _('Access denied. Insufficient permissions.'))
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)


class AnonymousRequiredMixin:
    """
    Mixin that requires the user to NOT be authenticated.
    Redirects authenticated users to their appropriate home page.
    
    Usage:
        class LoginView(AnonymousRequiredMixin, FormView):
            ...
            
    Note: This should be used with a view class that has a dispatch method.
    """
    
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.is_customer:
                return redirect('home')
            elif request.user.is_service_provider:
                return redirect('accounts_app:service_provider_profile')
            elif request.user.is_admin:
                return redirect('admin_app:dashboard')
            else:
                return redirect('home')
        
        if hasattr(super(), 'dispatch'):
            return super().dispatch(request, *args, **kwargs)
        else:
            # Handle case where mixin is used with a view that doesn't have dispatch
            return None


# --- Additional Security Decorators ---

def require_active_user(view_func):
    """
    Decorator to require that the user is authenticated and active.
    
    Usage:
        @require_active_user
        def my_view(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:customer_login')
        
        if not request.user.is_active:
            messages.error(request, _('Your account is inactive. Please contact support.'))
            return redirect('home')
        
        return view_func(request, *args, **kwargs)
    return wrapper


class ActiveUserRequiredMixin:
    """
    Mixin that requires the user to be authenticated and active.
    
    Usage:
        class MyView(ActiveUserRequiredMixin, DetailView):
            ...
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:customer_login')
        
        if not request.user.is_active:
            messages.error(request, _('Your account is inactive. Please contact support.'))
            return redirect('home')
        
        if hasattr(super(), 'dispatch'):
            return super().dispatch(request, *args, **kwargs)
        else:
            # Handle case where mixin is used with a view that doesn't have dispatch
            return None


# --- Utility Functions ---

def get_user_home_url(user):
    """
    Get the appropriate home URL for a user based on their role.
    
    Args:
        user: User instance
        
    Returns:
        str: URL name for the user's home page
    """
    if user.is_customer:
        return 'accounts_app:customer_profile'
    elif user.is_service_provider:
        return 'accounts_app:service_provider_profile'
    elif user.is_admin:
        return 'admin_app:dashboard'
    else:
        return 'home'


def get_user_login_url(user_role):
    """
    Get the appropriate login URL for a user role.
    
    Args:
        user_role: Role string (from UserRoles)
        
    Returns:
        str: URL name for the role's login page
    """
    if user_role == UserRoles.CUSTOMER:
        return 'accounts_app:customer_login'
    elif user_role == UserRoles.SERVICE_PROVIDER:
        return 'accounts_app:service_provider_login'
    elif user_role == UserRoles.ADMIN:
        return 'accounts_app:admin_login'
    else:
        return 'accounts_app:customer_login' 

# ---------------------------------------------------------------------------
# Legacy aliases (kept for backwards-compatibility with the old test-suite)
# ---------------------------------------------------------------------------

login_required_customer = customer_required  # alias
login_required_provider = service_provider_required  # alias
login_required_admin = admin_required  # alias 

# ---------------------------------------------------------------------------
# Additional lightweight decorators (test-suite compatibility only)
# ---------------------------------------------------------------------------

from django.http import HttpResponse, JsonResponse, HttpResponseForbidden
from django.utils import timezone

# Simple in-memory store for rate-limit counters (process-local – adequate for unit tests)
_rate_limit_store = {}


def email_verified_required(view_func):
    """Allow only users with verified e-mail addresses."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect(get_user_login_url(UserRoles.CUSTOMER))
        if not getattr(request.user, 'email_verified', False):
            return redirect('accounts_app:resend_verification')
        return view_func(request, *args, **kwargs)
    return wrapper


def profile_complete_required(view_func):
    """Ensure the customer profile is complete enough (very loose heuristic)."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect(get_user_login_url(UserRoles.CUSTOMER))
        profile = getattr(request.user, 'customer_profile', None)
        if not profile or not getattr(profile, 'address', None):
            return redirect('accounts_app:customer_profile_edit')
        return view_func(request, *args, **kwargs)
    return wrapper


def rate_limit(limit=5, period=60, by_ip=False):
    """Very naive rate-limit decorator (sufficient for unit tests)."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            key = request.META.get('REMOTE_ADDR') if by_ip else getattr(request.user, 'pk', 'anon')
            now = timezone.now().timestamp()
            bucket = _rate_limit_store.setdefault(key, [])
            # Remove expired timestamps
            bucket[:] = [ts for ts in bucket if now - ts < period]
            if len(bucket) >= limit:
                return HttpResponse('Rate limit exceeded', status=429)
            bucket.append(now)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def ajax_required(view_func):
    """Allow only AJAX (XMLHttpRequest) requests."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.headers.get('x-requested-with') != 'XMLHttpRequest':
            return HttpResponseForbidden('AJAX request required')
        return view_func(request, *args, **kwargs)
    return wrapper


def json_response(view_func):
    """Serialize the returned value into JsonResponse."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            result = view_func(request, *args, **kwargs)
            if isinstance(result, HttpResponse):
                return result
            return JsonResponse(result, safe=False)
        except Exception as exc:  # noqa: BLE001
            return JsonResponse({'error': str(exc)}, status=500)
    return wrapper


def cache_control(**directives):
    """Set Cache-Control header on response."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            response = view_func(request, *args, **kwargs)
            if isinstance(response, HttpResponse):
                parts = []
                for k, v in directives.items():
                    if isinstance(v, bool):
                        if v:
                            parts.append(k.replace('_', '-'))
                    else:
                        parts.append(f"{k.replace('_','-')}={v}")
                response['Cache-Control'] = ', '.join(parts)
            return response
        return wrapper
    return decorator


def permission_required_custom(permission):
    """Check user.has_perm; returns 403 if missing."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated or not request.user.has_perm(permission):
                return HttpResponseForbidden()
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def two_factor_required(view_func):
    """Require user.two_factor_enabled attribute to be True."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect(get_user_login_url(UserRoles.CUSTOMER))
        if not getattr(request.user, 'two_factor_enabled', False):
            return redirect('accounts_app:customer_settings')
        return view_func(request, *args, **kwargs)
    return wrapper


def account_active_required(view_func):
    """Ensure user.status is ACTIVE (simple check)."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect(get_user_login_url(UserRoles.CUSTOMER))
        if getattr(request.user, 'status', None) != UserStatus.ACTIVE:
            return HttpResponseForbidden()
        return view_func(request, *args, **kwargs)
    return wrapper 