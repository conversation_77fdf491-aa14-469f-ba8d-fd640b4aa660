import logging
from django.http import HttpResponse
from django.template.response import TemplateResponse
from django.utils import timezone
from django.utils.translation import gettext as _
from django.contrib.auth import get_user_model
from django.contrib.auth.signals import user_login_failed, user_logged_in
from django.dispatch import receiver
from django.core.exceptions import ValidationError
from django.contrib.auth.hashers import make_password

from ..models import AccountLockout, PasswordHistory

User = get_user_model()
logger = logging.getLogger(__name__)


class AccountLockoutMiddleware:
    """
    Middleware to handle account lockout functionality.
    Prevents brute force attacks by locking accounts after multiple failed attempts.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Ensure session is available before processing
        if not hasattr(request, 'session'):
            # If session is not available, create a minimal session
            from django.contrib.sessions.middleware import SessionMiddleware
            middleware = SessionMiddleware(lambda req: None)
            middleware.process_request(request)
            logger.warning(f"Session was missing in AccountLockoutMiddleware, created one for request: {request.path}")
        
        # Get client IP address
        ip_address = self.get_client_ip(request)
        
        # Check if IP is locked before processing request
        if AccountLockout.is_ip_locked(ip_address):
            try:
                lockout_record = AccountLockout.objects.get(ip_address=ip_address)
                if lockout_record.is_locked:
                    # Return lockout response
                    return self.render_lockout_response(request, lockout_record)
            except AccountLockout.DoesNotExist:
                pass
        
        response = self.get_response(request)
        return response
    
    def get_client_ip(self, request):
        """Get the client's IP address from the request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def render_lockout_response(self, request, lockout_record):
        """Render the account lockout response"""
        context = {
            'lockout_record': lockout_record,
            'locked_until': lockout_record.locked_until,
            'failed_attempts': lockout_record.failed_attempts,
            'ip_address': lockout_record.ip_address,
        }
        
        # Try to render custom template, fall back to simple response
        try:
            return TemplateResponse(
                request,
                'accounts_app/account_locked.html',
                context,
                status=423  # HTTP 423 Locked
            )
        except Exception:
            # Fallback to simple HTTP response
            locked_until = lockout_record.locked_until
            if locked_until:
                message = _(
                    'Account temporarily locked due to multiple failed login attempts. '
                    'Please try again after {time}.'
                ).format(time=locked_until.strftime('%Y-%m-%d %H:%M:%S UTC'))
            else:
                message = _('Account temporarily locked due to multiple failed login attempts.')
            
            return HttpResponse(
                message,
                status=423,
                content_type='text/plain'
            )


@receiver(user_login_failed)
def handle_failed_login(sender, credentials, request, **kwargs):
    """Handle failed login attempts"""
    if request is None:
        return
    
    # Get client IP address
    ip_address = get_client_ip_from_request(request)
    
    # Try to find the user
    user = None
    email = credentials.get('email') or credentials.get('username')
    if email:
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            pass
    
    # Record failed attempt
    lockout_record = AccountLockout.record_failed_attempt(ip_address, user)
    
    # Log the failed attempt
    logger.warning(
        f"Failed login attempt from {ip_address} for user {email}. "
        f"Total attempts: {lockout_record.failed_attempts}"
    )
    
    # Log if account is now locked
    if lockout_record.is_locked:
        logger.warning(
            f"Account locked for IP {ip_address} after {lockout_record.failed_attempts} failed attempts. "
            f"Locked until: {lockout_record.locked_until}"
        )


@receiver(user_logged_in)
def handle_successful_login(sender, user, request, **kwargs):
    """Handle successful login"""
    if request is None:
        return
    
    # Get client IP address
    ip_address = get_client_ip_from_request(request)
    
    # Reset failed attempts for this IP
    AccountLockout.reset_failed_attempts(ip_address)
    
    # Log successful login
    logger.info(f"Successful login for user {user.email} from {ip_address}")


def get_client_ip_from_request(request):
    """Helper function to get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


class PasswordHistoryMiddleware:
    """
    Middleware to track password history when users change passwords.
    Works with Django's built-in password change functionality.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Store original password before processing
        original_password = None
        if hasattr(request, 'user') and request.user.is_authenticated:
            original_password = request.user.password
            
        response = self.get_response(request)
        
        # Check if password was changed and update history
        if (original_password is not None and
            hasattr(request, 'user') and 
            request.user.is_authenticated and 
            hasattr(request.user, 'password') and
            request.user.password != original_password):
            
            # Add to password history
            PasswordHistory.add_password(request.user, request.user.password)
            
            logger.info(f"Password changed for user {request.user.email}")
        
        return response


# Signal handler for password changes through admin or other methods
@receiver(user_logged_in)
def track_password_change(sender, user, **kwargs):
    """Track password changes when user logs in with new password"""
    try:
        # Check if this is a new password by comparing with history
        recent_passwords = PasswordHistory.objects.filter(user=user).order_by('-created_at')[:1]
        
        if recent_passwords:
            last_password = recent_passwords[0]
            if last_password.password_hash != user.password:
                # New password detected, add to history
                PasswordHistory.add_password(user, user.password)
        else:
            # No history exists, add current password
            PasswordHistory.add_password(user, user.password)
            
    except Exception as e:
        logger.error(f"Error tracking password change for user {user.email}: {e}")


# Custom password change handling
def handle_password_change(user, new_password):
    """
    Handle password change with validation and history tracking.
    
    :param user: User instance
    :param new_password: New password (plaintext)
    :raises ValidationError: If password validation fails
    """
    from django.contrib.auth.password_validation import validate_password
    
    # Validate the new password
    validate_password(new_password, user)
    
    # Hash the new password
    hashed_password = make_password(new_password)
    
    # Update user's password
    user.password = hashed_password
    user.save()
    
    # Add to password history
    PasswordHistory.add_password(user, hashed_password)
    
    logger.info(f"Password successfully changed for user {user.email}") 