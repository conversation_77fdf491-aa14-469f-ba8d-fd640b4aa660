# Provider Profile Forms Refactoring

This directory contains the refactored `ServiceProviderProfileForm` that was previously a single large file (674+ lines) split into logical components for better maintainability and organization.

## Structure

```
accounts_app/forms/provider/profile/
├── __init__.py          # Package initialization and main form export
├── basic_info.py        # Main form class and layout configuration
├── business_info.py     # Business-specific form fields
├── contact_info.py      # Contact and address form fields
├── social_media.py      # Social media URL form fields
├── validation.py        # Custom validation methods
└── README.md           # This documentation file
```

## Files Overview

### `__init__.py`
- Package initialization
- Exports the main `ServiceProviderProfileForm` class
- Maintains backward compatibility

### `basic_info.py`
- Contains the main `ServiceProviderProfileForm` class
- Handles form initialization and crispy-forms layout
- Dynamically imports fields from other modules
- Contains the `save()` method

### `business_info.py`
- Contains business-specific form fields:
  - Legal business name
  - DBA name (display name)
  - Business description
  - EIN (Employer Identification Number)
  - Logo upload
  - Public visibility toggle

### `contact_info.py`
- Contains contact and address form fields:
  - Business phone
  - Contact person name
  - Street address
  - City, state, county
  - ZIP code

### `social_media.py`
- Contains social media URL form fields:
  - Website URL
  - Instagram URL
  - Facebook URL

### `validation.py`
- Contains all custom validation methods:
  - `clean_logo()` - Logo image validation and processing
  - `clean_phone()` - Phone number normalization
  - `clean_website()` - Website URL validation
  - `clean_instagram()` - Instagram URL validation
  - `clean_facebook()` - Facebook URL validation
  - `clean_ein()` - EIN format validation
  - `clean_zip_code()` - ZIP code validation
  - `clean_legal_name()` - Business name validation
  - `clean_display_name()` - DBA name validation
  - `clean_contact_name()` - Contact person validation
  - `clean_county()` - County name validation
  - `clean_address()` - Address validation
  - `clean_city()` - City name validation
  - `clean_description()` - Business description validation

## Backward Compatibility

The original `accounts_app/forms/provider/profile.py` file has been replaced with a simple import that maintains full backward compatibility:

```python
# Import the refactored form from the new subdirectory structure
from .profile import ServiceProviderProfileForm

# Re-export for backward compatibility
__all__ = ['ServiceProviderProfileForm']
```

This means existing imports will continue to work without any changes:

```python
from accounts_app.forms.provider.profile import ServiceProviderProfileForm
```

## Benefits of Refactoring

1. **Improved Maintainability**: Each file has a single responsibility
2. **Better Organization**: Related functionality is grouped together
3. **Easier Testing**: Individual components can be tested in isolation
4. **Reduced Complexity**: Smaller files are easier to understand and modify
5. **Better Code Reuse**: Field definitions can be reused in other forms
6. **Clearer Structure**: The purpose of each file is immediately obvious

## Usage

The form can be used exactly as before:

```python
from accounts_app.forms.provider.profile import ServiceProviderProfileForm

# Create form instance
form = ServiceProviderProfileForm(data=request.POST, files=request.FILES, instance=profile)

# Validate and save
if form.is_valid():
    profile = form.save()
```

## Migration Notes

- No changes required to existing code
- All functionality remains the same
- Form behavior and validation are identical
- Crispy-forms layout is preserved
- All mixins and dependencies are maintained 