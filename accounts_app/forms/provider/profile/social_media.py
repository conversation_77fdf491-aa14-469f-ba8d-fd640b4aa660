"""
Social Media Form Fields

Contains form fields for social media URLs and online presence.
"""

from django import forms
from django.utils.translation import gettext_lazy as _


def get_social_media_fields():
    """
    Get social media form fields.
    
    Returns:
        dict: Dictionary of form fields for social media URLs
    """
    return {
        'website': forms.URLField(
            label=_('Website URL'),
            required=False,
            widget=forms.URLInput(
                attrs={
                    'placeholder': _('https://www.yourbusiness.com'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Your business website')
        ),
        
        'instagram': forms.URLField(
            label=_('Instagram URL'),
            required=False,
            widget=forms.URLInput(
                attrs={
                    'placeholder': _('https://www.instagram.com/yourbusiness'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Your Instagram profile or business page')
        ),
        
        'facebook': forms.URLField(
            label=_('Facebook URL'),
            required=False,
            widget=forms.URLInput(
                attrs={
                    'placeholder': _('https://www.facebook.com/yourbusiness'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Your Facebook business page')
        ),
    } 