"""
Business Information Form Fields

Contains form fields for business-specific information.
"""

from django import forms
from django.utils.translation import gettext_lazy as _


def get_business_info_fields():
    """
    Get business information form fields.
    
    Returns:
        dict: Dictionary of form fields for business information
    """
    return {
        'legal_name': forms.Char<PERSON>ield(
            label=_('Legal Business Name'),
            max_length=200,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('Your Business Name LLC'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Official legal name of your business')
        ),
        
        'display_name': forms.CharField(
            label=_('DBA Name'),
            max_length=200,
            required=False,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('Doing Business As (optional)'),
                    'class': 'form-control',
                }
            ),
            help_text=_('If different from business name')
        ),
        
        'description': forms.CharField(
            label=_('Business Description'),
            max_length=500,
            required=False,
            widget=forms.Textarea(
                attrs={
                    'placeholder': _('Describe your business and services'),
                    'rows': 3,
                    'class': 'form-control',
                }
            ),
            help_text=_('Brief description of your business (max 500 characters)')
        ),
        
        'ein': forms.CharField(
            label=_('EIN (Employer Identification Number)'),
            max_length=12,
            required=False,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('XX-XXXXXXX'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Federal tax ID number (optional)')
        ),
        
        'logo': forms.ImageField(
            label=_('Business Logo'),
            required=False,
            widget=forms.FileInput(
                attrs={
                    'accept': 'image/jpeg,image/png,image/webp,image/gif',
                    'class': 'form-control',
                }
            ),
            help_text=_('Upload your logo (PNG, JPG, WebP, or GIF). It will be optimized automatically.')
        ),
        
        'is_public': forms.BooleanField(
            label=_('Make business visible to customers'),
            required=False,
            widget=forms.CheckboxInput(
                attrs={
                    'class': 'form-check-input',
                }
            ),
            help_text=_('Uncheck to temporarily hide your business from customers')
        ),
    } 