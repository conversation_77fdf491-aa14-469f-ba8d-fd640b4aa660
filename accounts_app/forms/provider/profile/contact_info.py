"""
Contact Information Form Fields

Contains form fields for contact information and address details.
"""

from django import forms
from django.utils.translation import gettext_lazy as _


def get_contact_info_fields():
    """
    Get contact information form fields.
    
    Returns:
        dict: Dictionary of form fields for contact information
    """
    return {
        'phone': forms.Char<PERSON>ield(
            label=_('Business Phone'),
            max_length=20,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('(*************'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Primary business contact number')
        ),
        
        'contact_name': forms.Char<PERSON>ield(
            label=_('Contact Person'),
            max_length=100,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('<PERSON>'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Primary contact person for the business')
        ),
        
        'address': forms.Char<PERSON>ield(
            label=_('Street Address'),
            max_length=200,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('123 Main Street'),
                    'class': 'form-control',
                }
            ),
            help_text=_('Business street address')
        ),
        
        'city': forms.CharField(
            label=_('City'),
            max_length=100,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('New York'),
                    'class': 'form-control',
                }
            )
        ),
        
        'state': forms.ChoiceField(
            label=_('State'),
            required=True,
            widget=forms.Select(
                attrs={
                    'class': 'form-select',
                }
            ),
            choices=[
                ('', _('Select State')),
                ('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'),
                ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'),
                ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'),
                ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'),
                ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'),
                ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'),
                ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'),
                ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'),
                ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'),
                ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'),
                ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'),
                ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'),
                ('WI', 'Wisconsin'), ('WY', 'Wyoming'),
            ]
        ),
        
        'county': forms.CharField(
            label=_('County'),
            max_length=100,
            required=False,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('County name'),
                    'class': 'form-control',
                }
            ),
            help_text=_('County (optional)')
        ),
        
        'zip_code': forms.CharField(
            label=_('ZIP Code'),
            max_length=10,
            required=True,
            widget=forms.TextInput(
                attrs={
                    'placeholder': _('12345'),
                    'class': 'form-control',
                }
            ),
            help_text=_('5-digit ZIP code or ZIP+4 format')
        ),
    } 