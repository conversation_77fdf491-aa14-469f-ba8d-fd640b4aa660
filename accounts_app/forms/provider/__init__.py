"""
Provider forms package for accounts_app.

This package contains all form classes related to service providers, 
organized by functionality for better maintainability.
"""

# --- Provider Forms ---

# Import auth forms from organized subdirectory
from .auth import (
    ServiceProviderSignupForm,
    ServiceProviderLoginForm,
    ServiceProviderPasswordChangeForm,
)

# Import profile forms (already separate)
from .profile import ServiceProviderProfileForm

# Backward compatibility exports
__all__ = [
    # Auth forms
    'ServiceProviderSignupForm',
    'ServiceProviderLoginForm',
    'ServiceProviderPasswordChangeForm',
    
    # Profile forms
    'ServiceProviderProfileForm',
] 