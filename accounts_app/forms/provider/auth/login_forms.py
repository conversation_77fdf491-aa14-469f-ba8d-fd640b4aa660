# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Submit
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ....models import CustomUser
from ...common import AccessibleFormMixin


class ServiceProviderLoginForm(AccessibleFormMixin, forms.Form):
    """
    Authenticate a service provider using email and password.

    Features:
    - Role validation
    - Active account check
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        )
    )

    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your password'),
                'autocomplete': 'current-password',
                'required': True,
                'type': 'password',
                'minlength': '1',
            }
        )
    )

    def __init__(self, request=None, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        self.request = request
        self.user_cache = None
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.layout = Layout(
            Field('email', css_class='form-control'),
            Field('password', css_class='form-control'),
            FormActions(
                Submit('login', _('Sign In'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean(self) -> dict:
        """
        Validate the user's email and password combination.

        Raises:
            ValidationError: On authentication failure.

        Returns:
            dict: The cleaned data.
        """
        data = super().clean()
        email = data.get('email')
        password = data.get('password')

        if email and password:
            from django.contrib.auth import authenticate

            try:
                user = CustomUser.objects.get(email=email)
                if not user.is_service_provider:
                    raise ValidationError(
                        _('Invalid email or password.'), code='invalid_login'
                    )
                if not user.is_active and user.check_password(password):
                    raise ValidationError(
                        _('Please verify your email to activate your account.'),
                        code='inactive'
                    )
                authenticated = authenticate(
                    self.request, username=email, password=password
                )
                if not authenticated:
                    raise ValidationError(
                        _('Invalid email or password.'), code='invalid_login'
                    )
                self.user_cache = authenticated
            except CustomUser.DoesNotExist:
                raise ValidationError(
                    _('Invalid email or password.'), code='invalid_login'
                )
        return data

    def get_user(self):
        """
        Retrieve the authenticated user after form validation.

        Returns:
            CustomUser or None: The authenticated user.
        """
        return getattr(self, 'user_cache', None) 