# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import PasswordChangeForm
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Submit
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ...common import AccessibleFormMixin


class ServiceProviderPasswordChangeForm(AccessibleFormMixin, PasswordChangeForm):
    """
    Allow service providers to change their password.

    Inherits built-in PasswordChangeForm with custom styling.
    """
    old_password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your current password'),
                'autocomplete': 'current-password',
            }
        )
    )

    new_password1 = forms.CharField(
        label=_('New Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your new password'),
                'autocomplete': 'new-password',
                'minlength': '12',
            }
        ),
        help_text=_('Your password must contain at least 12 characters.')
    )

    new_password2 = forms.CharField(
        label=_('Confirm New Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Confirm your new password'),
                'autocomplete': 'new-password',
                'minlength': '12',
            }
        )
    )

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        # Override password help text to prevent HTML list formatting
        self.fields['new_password1'].help_text = _('Your password must contain at least 12 characters.')
        
        self.helper.layout = Layout(
            Field('old_password', css_class='form-control'),
            Field('new_password1', css_class='form-control'),
            Field('new_password2', css_class='form-control'),
            FormActions(
                Submit('change_password', _('Change Password'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        ) 