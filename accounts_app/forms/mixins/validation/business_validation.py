"""
Business Validation Mixin

This module provides the BusinessValidationMixin for business-related field validation.
"""

import re
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message


class BusinessValidationMixin:
    """
    Mixin for business-related field validation.
    
    Provides methods for validating business names, EIN numbers,
    addresses, and other business-specific fields.
    """
    
    def clean_ein(self):
        """
        Clean and validate EIN (Employer Identification Number) field.
        
        Returns:
            str: Cleaned EIN
            
        Raises:
            ValidationError: If EIN is invalid
        """
        ein = self.cleaned_data.get('ein')
        
        if not ein:
            return ein
            
        # Remove formatting
        ein = re.sub(r'[^\d]', '', ein)
        
        # Check length
        if len(ein) != 9:
            raise ValidationError(
                get_error_message('business', 'invalid_ein'),
                code='invalid_ein'
            )
        
        # Format EIN
        formatted_ein = f"{ein[:2]}-{ein[2:]}"
        
        return formatted_ein
    
    def clean_zip_code(self):
        """
        Clean and validate ZIP code field.
        
        Returns:
            str: Cleaned ZIP code
            
        Raises:
            ValidationError: If ZIP code is invalid
        """
        zip_code = self.cleaned_data.get('zip_code')
        
        # Check if field is required and empty
        if not zip_code and hasattr(self, 'fields') and 'zip_code' in self.fields and self.fields['zip_code'].required:
            raise ValidationError(
                get_error_message('text', 'required_field'),
                code='required'
            )
        
        if not zip_code:
            return zip_code
            
        # Remove formatting
        zip_code = re.sub(r'[^\d]', '', zip_code)
        
        # Check format (5 digits or 5+4 digits)
        if not re.match(r'^\d{5}(\d{4})?$', zip_code):
            raise ValidationError(
                get_error_message('business', 'invalid_zip_code'),
                code='invalid_zip_code'
            )
        
        return zip_code
    
    def clean_legal_name(self):
        """
        Clean and validate legal business name field.
        
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        legal_name = self.cleaned_data.get('legal_name')
        return self._clean_business_name_field(legal_name, 'legal_name')
    
    def clean_business_name(self):
        """
        Clean and validate business name field.
        
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        business_name = self.cleaned_data.get('business_name')
        return self._clean_business_name_field(business_name, 'business_name')
    
    def _clean_business_name_field(self, name, field_name):
        """
        Internal method to clean business name field.
        
        Args:
            name (str): Raw business name input
            field_name (str): Name of the field being validated
            
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        # Check if field is required and empty
        if not name and hasattr(self, 'fields') and field_name in self.fields and self.fields[field_name].required:
            raise ValidationError(
                get_error_message('text', 'required_field'),
                code='required'
            )
        
        if not name:
            return name
            
        name = name.strip()
        
        # Check length
        if len(name) < 2:
            raise ValidationError(
                get_error_message('text', 'too_short', min_length=2),
                code='too_short'
            )
        
        if len(name) > 200:
            raise ValidationError(
                get_error_message('text', 'too_long', max_length=200),
                code='too_long'
            )
        
        # Check for valid characters (allow letters, numbers, spaces, and common business punctuation)
        if not re.match(r'^[a-zA-Z0-9\s\-\'\.\&\,\(\)]+$', name):
            raise ValidationError(
                get_error_message('text', 'invalid_business_name'),
                code='invalid_business_name'
            )
        
        return name 