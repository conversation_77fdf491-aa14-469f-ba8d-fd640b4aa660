"""
Terms Validation Mixin

This module provides the TermsValidationMixin for terms and conditions validation.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message


class TermsValidationMixin:
    """
    Mixin for terms and conditions validation.
    
    Provides methods for validating agreement checkboxes and
    consent fields.
    """
    
    def clean_agree_to_terms(self):
        """
        Clean and validate terms agreement field.
        
        Returns:
            bool: Cleaned terms agreement value
            
        Raises:
            ValidationError: If terms not agreed to
        """
        agree_to_terms = self.cleaned_data.get('agree_to_terms')
        
        if not agree_to_terms:
            raise ValidationError(
                get_error_message('terms', 'terms_required'),
                code='terms_required'
            )
        
        return agree_to_terms
    
    def clean_privacy_policy(self):
        """
        Clean and validate privacy policy agreement field.
        
        Returns:
            bool: Cleaned privacy agreement value
            
        Raises:
            ValidationError: If privacy policy not agreed to
        """
        privacy_policy = self.cleaned_data.get('privacy_policy')
        
        if not privacy_policy:
            raise ValidationError(
                get_error_message('terms', 'privacy_required'),
                code='privacy_required'
            )
        
        return privacy_policy 