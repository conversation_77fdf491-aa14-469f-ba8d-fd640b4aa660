"""
Phone Validation Mixin

This module provides the PhoneValidationMixin for phone number field validation
and normalization.
"""

import re
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message


class PhoneValidationMixin:
    """
    Mixin for phone number field validation and normalization.
    
    Provides methods for validating phone numbers, normalizing formats,
    and handling international/US phone number patterns.
    """
    
    def clean_phone_number(self):
        """
        Clean and validate phone number field.
        
        Returns:
            str: Cleaned and normalized phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        phone = self.cleaned_data.get('phone_number')
        return self._clean_phone_field(phone)
    
    def clean_phone(self):
        """
        Clean and validate phone field (alternative name).
        
        Returns:
            str: Cleaned and normalized phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        phone = self.cleaned_data.get('phone')
        return self._clean_phone_field(phone)
    
    def _clean_phone_field(self, phone):
        """
        Internal method to clean phone number field.
        
        Args:
            phone (str): Raw phone number input
            
        Returns:
            str: Cleaned phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        # Check if field is required and empty
        if not phone and hasattr(self, 'fields'):
            # Check if this is a phone field that should be required
            phone_fields = ['phone', 'phone_number']
            for field_name in phone_fields:
                if field_name in self.fields and self.fields[field_name].required:
                    raise ValidationError(
                        get_error_message('phone', 'phone_required'),
                        code='required'
                    )
        
        if not phone:
            return phone
            
        # Remove common formatting characters
        original_phone = phone
        phone = phone.strip()
        
        # Check for invalid characters
        if not re.match(r'^[\d\s\-\(\)\+\.]+$', phone):
            raise ValidationError(
                get_error_message('phone', 'invalid_characters'),
                code='invalid_characters'
            )
        
        # Extract only digits
        digits_only = re.sub(r'[^\d]', '', phone)
        
        # Validate length
        if len(digits_only) < 10:
            raise ValidationError(
                get_error_message('phone', 'too_short'),
                code='too_short'
            )
        
        if len(digits_only) > 15:
            raise ValidationError(
                get_error_message('phone', 'too_long'),
                code='too_long'
            )
        
        # Normalize format
        normalized_phone = self._normalize_phone_number(original_phone, digits_only)
        
        return normalized_phone
    
    def _normalize_phone_number(self, original_phone, digits_only):
        """
        Normalize phone number to consistent format.
        
        Args:
            original_phone (str): Original phone input
            digits_only (str): Phone number with only digits
            
        Returns:
            str: Normalized phone number
        """
        # Handle international format
        if original_phone.startswith('+'):
            # For US numbers with +1 prefix, format nicely
            if len(digits_only) == 11 and digits_only.startswith('1'):
                # Format as (XXX) XXX-XXXX
                area_code = digits_only[1:4]
                exchange = digits_only[4:7]
                number = digits_only[7:11]
                return f"({area_code}) {exchange}-{number}"
            else:
                # For other international numbers, keep the + prefix
                return '+' + digits_only
        
        # Handle US format
        if len(digits_only) == 10:
            # Format as (XXX) XXX-XXXX
            area_code = digits_only[:3]
            exchange = digits_only[3:6]
            number = digits_only[6:10]
            return f"({area_code}) {exchange}-{number}"
        elif len(digits_only) == 11 and digits_only.startswith('1'):
            # Remove country code and format as (XXX) XXX-XXXX
            area_code = digits_only[1:4]
            exchange = digits_only[4:7]
            number = digits_only[7:11]
            return f"({area_code}) {exchange}-{number}"
        
        # Return as-is for other formats
        return original_phone 