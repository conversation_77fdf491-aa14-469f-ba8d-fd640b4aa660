"""
Password Validation Mixin

This module provides the PasswordValidationMixin for password field validation
with strength checking and confirmation matching.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message
from ....models import CustomUser


class PasswordValidationMixin:
    """
    Mixin for password field validation with strength checking.
    
    Provides methods for validating password strength, confirmation matching,
    and common password-related validation patterns.
    """
    
    def clean_password1(self):
        """
        Clean and validate first password field.
        
        Returns:
            str: Cleaned password
            
        Raises:
            ValidationError: If password doesn't meet requirements
        """
        password1 = self.cleaned_data.get('password1')
        
        if not password1:
            return password1
            
        # Check maximum length
        if len(password1) > 128:
            raise ValidationError(
                get_error_message('password', 'password_too_long'),
                code='password_too_long'
            )
        
        # Use Django's built-in password validation
        user = self._get_user_for_password_validation()
        try:
            validate_password(password1, user)
        except ValidationError as error:
            # Transform Django's error messages to our custom ones
            transformed_errors = []
            for message in error.messages:
                if 'at least 12 characters' in message or 'at least 8 characters' in message:
                    transformed_errors.append(get_error_message('password', 'too_short'))
                elif 'common' in message:
                    transformed_errors.append(get_error_message('password', 'too_common'))
                elif 'similar' in message:
                    transformed_errors.append(get_error_message('password', 'too_similar'))
                elif 'numeric' in message:
                    transformed_errors.append(get_error_message('password', 'entirely_numeric'))
                else:
                    transformed_errors.append(str(message))
            
            raise ValidationError(transformed_errors)
        
        # No additional custom strength validation beyond Django's validators
        # (which already enforce a minimum length of 12 characters).
        
        return password1
    
    def clean_password2(self):
        """
        Clean and validate password confirmation field.
        
        Returns:
            str: Cleaned password confirmation
            
        Raises:
            ValidationError: If passwords don't match
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        
        if password1 and password2 and password1 != password2:
            raise ValidationError(
                get_error_message('password', 'password_mismatch'),
                code='password_mismatch'
            )
        
        return password2
    
    def _get_user_for_password_validation(self):
        """
        Get user instance for password validation context.
        
        Returns:
            CustomUser: User instance or new user with email
        """
        if hasattr(self, 'instance') and self.instance:
            return self.instance
        
        # Create temporary user for validation
        email = self.cleaned_data.get('email')
        return CustomUser(email=email) if email else CustomUser()
    
    def _check_password_strength(self, password):
        """
        Check password strength with custom rules.
        
        Args:
            password (str): Password to check
            
        Returns:
            bool: True if password meets strength requirements
        """
        if len(password) < 8:
            return False
            
        # Check for required character types
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        
        return has_upper and has_lower and has_digit and has_special 