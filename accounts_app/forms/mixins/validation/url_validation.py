"""
URL Validation Mixin

This module provides the URLValidationMixin for URL field validation
with format checking.
"""

import re
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message


class URLValidationMixin:
    """
    Mixin for URL field validation with format checking.
    
    Provides methods for validating website URLs, social media URLs,
    and other URL-related fields.
    """
    
    def clean_website(self):
        """
        Clean and validate website URL field.
        
        Returns:
            str: Cleaned website URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        website = self.cleaned_data.get('website')
        return self._clean_url_field(website, 'website')
    
    def clean_instagram(self):
        """
        Clean and validate Instagram URL field.
        
        Returns:
            str: Cleaned Instagram URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        instagram = self.cleaned_data.get('instagram')
        return self._clean_social_url_field(instagram, 'instagram')
    
    def clean_facebook(self):
        """
        Clean and validate Facebook URL field.
        
        Returns:
            str: Cleaned Facebook URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        facebook = self.cleaned_data.get('facebook')
        return self._clean_social_url_field(facebook, 'facebook')
    
    def _clean_url_field(self, url, field_name='url'):
        """
        Internal method to clean URL field.
        
        Args:
            url (str): Raw URL input
            field_name (str): Name of the field being validated
            
        Returns:
            str: Cleaned URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            return url
            
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Check URL length
        if len(url) > 2000:
            raise ValidationError(
                get_error_message('url', 'url_too_long'),
                code='url_too_long'
            )
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            raise ValidationError(
                get_error_message('url', 'invalid_format'),
                code='invalid_format'
            )
        
        return url
    
    def _clean_social_url_field(self, url, platform):
        """
        Clean social media URL field.
        
        Args:
            url (str): Raw URL input
            platform (str): Social media platform name
            
        Returns:
            str: Cleaned social media URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            return url
            
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Platform-specific validation
        if platform == 'instagram':
            if not re.match(r'^https?://(www\.)?instagram\.com/', url, re.IGNORECASE):
                raise ValidationError(
                    get_error_message('url', 'social_media_invalid', platform='Instagram'),
                    code='social_media_invalid'
                )
        elif platform == 'facebook':
            if not re.match(r'^https?://(www\.)?facebook\.com/', url, re.IGNORECASE):
                raise ValidationError(
                    get_error_message('url', 'social_media_invalid', platform='Facebook'),
                    code='social_media_invalid'
                )
        
        return url 