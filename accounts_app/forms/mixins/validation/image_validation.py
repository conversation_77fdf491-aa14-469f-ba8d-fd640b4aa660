"""
Image Validation Mixin

This module provides the ImageValidationMixin for image field validation
with size and format checking.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message


class ImageValidationMixin:
    """
    Mixin for image field validation with size and format checking.
    
    Provides methods for validating image uploads, checking file sizes,
    formats, and dimensions. Enhanced with image processing capabilities.
    """
    
    def clean_profile_picture(self):
        """
        Clean and validate profile picture field.
        
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        image = self.cleaned_data.get('profile_picture')
        return self._clean_image_field(image, 'profile_picture')
    
    def clean_logo(self):
        """
        Clean and validate logo field.
        
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        image = self.cleaned_data.get('logo')
        return self._clean_image_field(image, 'logo')
    
    def _clean_image_field(self, image, field_name='image'):
        """
        Internal method to clean image field with enhanced validation and processing.
        
        Args:
            image: Image file object
            field_name (str): Name of the field being validated
            
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        if not image:
            return image
            
        # Skip validation for existing images
        if hasattr(image, 'url') and not hasattr(image, 'content_type'):
            return image
        
        # Use enhanced image validation from utils
        try:
            from ...utils import validate_image_file, process_profile_image
            
            # Validate the image file
            validate_image_file(image)
            
            # Process the image if it's a new upload
            if hasattr(image, 'content_type') and image.content_type:
                # Only process new uploads, not existing images
                processed_image = process_profile_image(image)
                if processed_image:
                    return processed_image
            
        except ImportError:
            # Fall back to basic validation if utils are not available
            pass
        except Exception as e:
            # If processing fails, fall back to basic validation
            import logging
            logging.warning(f"Image processing failed: {str(e)}")
        
        # Fallback to basic validation
        # Check file size (5MB limit)
        max_size = 5 * 1024 * 1024
        if hasattr(image, 'size') and image.size > max_size:
            raise ValidationError(
                get_error_message('image', 'file_too_large', max_size=5),
                code='file_too_large'
            )
        
        # Check file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
        if hasattr(image, 'content_type') and image.content_type not in allowed_types:
            raise ValidationError(
                get_error_message('image', 'invalid_format'),
                code='invalid_format'
            )
        
        # Check file extension
        if hasattr(image, 'name'):
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
            file_extension = '.' + image.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                raise ValidationError(
                    get_error_message('image', 'invalid_extension'),
                    code='invalid_extension'
                )
        
        # Validate image dimensions
        self._validate_image_dimensions(image, field_name)
        
        return image
    
    def _validate_image_dimensions(self, image, field_name):
        """
        Validate image dimensions.
        
        Args:
            image: Image file object
            field_name (str): Name of the field being validated
            
        Raises:
            ValidationError: If image dimensions are invalid
        """
        try:
            from PIL import Image
            
            # Reset file pointer
            image.seek(0)
            
            # Open and verify image
            pil_image = Image.open(image)
            pil_image.verify()
            
            # Reset file pointer after verification
            image.seek(0)
            
            # Check dimensions
            pil_image = Image.open(image)
            width, height = pil_image.size
            
            # Set dimension limits based on field type
            if field_name == 'profile_picture':
                min_width = min_height = 100
                max_width = max_height = 1000
            elif field_name == 'logo':
                min_width = min_height = 50
                max_width = max_height = 2000
            else:
                min_width = min_height = 50
                max_width = max_height = 2000
            
            if width < min_width or height < min_height:
                raise ValidationError(
                    get_error_message('image', 'image_too_small', 
                                    min_width=min_width, min_height=min_height),
                    code='image_too_small'
                )
            
            if width > max_width or height > max_height:
                raise ValidationError(
                    get_error_message('image', 'image_too_large',
                                    max_width=max_width, max_height=max_height),
                    code='image_too_large'
                )
            
            # Reset file pointer for saving
            image.seek(0)
            
        except ImportError:
            # PIL not available, skip dimension validation
            pass
        except Exception:
            raise ValidationError(
                get_error_message('image', 'invalid_image'),
                code='invalid_image'
            ) 