"""
Email Validation Mixin

This module provides the EmailValidationMixin for email field validation
with uniqueness checking and format validation.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from ...errors import get_error_message
from ....models import CustomUser


class EmailValidationMixin:
    """
    Mixin for email field validation with uniqueness checking.
    
    Provides methods for validating email format, uniqueness, and common
    email-related validation patterns.
    """
    
    def clean_email(self):
        """
        Clean and validate email field with uniqueness check.
        
        Returns:
            str: Cleaned email address
            
        Raises:
            ValidationError: If email is invalid or already exists
        """
        email = self.cleaned_data.get('email')
        
        if not email:
            return email
            
        # Convert to lowercase for consistency
        email = email.lower().strip()
        
        # Check maximum length
        if len(email) > 254:
            raise ValidationError(
                get_error_message('email', 'email_too_long'),
                code='email_too_long'
            )
        
        # Check for uniqueness (skip if editing existing user)
        if self._should_check_email_uniqueness(email):
            if CustomUser.objects.filter(email=email).exists():
                raise ValidationError(
                    get_error_message('email', 'email_exists'),
                    code='email_exists'
                )
        
        return email
    
    def _should_check_email_uniqueness(self, email):
        """
        Determine if email uniqueness should be checked.
        
        Args:
            email (str): Email to check
            
        Returns:
            bool: True if uniqueness should be checked
        """
        # Skip uniqueness check if editing existing user with same email
        if hasattr(self, 'instance') and self.instance and hasattr(self.instance, 'email'):
            return self.instance.email != email
        return True
    
    def clean_confirm_email(self):
        """
        Validate email confirmation field.
        
        Returns:
            str: Cleaned email address
            
        Raises:
            ValidationError: If emails don't match
        """
        confirm_email = self.cleaned_data.get('confirm_email')
        email = self.cleaned_data.get('email')
        
        if confirm_email and email and confirm_email != email:
            raise ValidationError(
                get_error_message('email', 'email_mismatch'),
                code='email_mismatch'
            )
        
        return confirm_email 