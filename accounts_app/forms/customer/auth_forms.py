# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, Submit
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ..common import AccessibleFormMixin
from ...models import CustomUser
from ...mixins import (
    EmailValidationMixin, 
    PasswordValidationMixin, 
    TermsValidationMixin
)


class CustomerSignupForm(AccessibleFormMixin, EmailValidationMixin, PasswordValidationMixin, TermsValidationMixin, UserCreationForm):
    """
    Sign up a new customer with email and password.

    Features:
    - Email uniqueness validation
    - Password confirmation
    - Accessible form styling
    - Password strength validation with proper error display
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        ),
        help_text=_('We\'ll never share your email with anyone else.')
    )
    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Create a strong password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '12',
                'title': _('Password must be at least 12 characters long.'),
            }
        ),
        help_text=_(
            'Your password must contain at least 12 characters.'
        )
    )
    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Confirm your password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '12',
            }
        ),
        help_text=_('Enter the same password as before, for verification.')
    )
    agree_to_terms = forms.BooleanField(
        label=_('I agree to the Terms of Service and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'required': True,
            }
        ),
        error_messages={
            'required': _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
        }
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'password1', 'password2', 'agree_to_terms')

    def __init__(self, *args, **kwargs):
        """
        Initialize form with crispy-forms helper and accessibility features.
        
        Sets up the form layout using crispy-forms with:
        - Bootstrap styling
        - Accessibility attributes
        - Validation classes
        - Responsive layout
        
        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        # Override password help text to prevent HTML list formatting
        self.fields['password1'].help_text = _('Your password must contain at least 12 characters.')
        
        self.helper.layout = Layout(
            Field('email', css_class='form-control'),
            Field('password1', css_class='form-control'),
            Field('password2', css_class='form-control'),
            Div(
                Field('agree_to_terms', css_class='form-check-input'),
                css_class='form-check mb-3'
            ),
            FormActions(
                Submit('signup', _('Create Account'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_email(self):
        """
        Ensure the email address is unique.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError(
                _('A user with this email already exists.'),
                code='email_exists'
            )
        return email

    def clean_password1(self):
        """
        Validate password1 using Django's password validators.

        This ensures password strength errors are displayed on the password1 field
        instead of password2 field.

        Returns:
            str: The cleaned password.

        Raises:
            ValidationError: If password doesn't meet requirements.
        """
        password1 = self.cleaned_data.get('password1')
        if password1:
            # Import here to avoid circular imports
            from django.contrib.auth.password_validation import validate_password
            try:
                # Create a temporary user instance for validation if none exists
                user = self.instance
                if user is None:
                    # Create a temporary user with the email for validation
                    email = self.cleaned_data.get('email')
                    user = CustomUser(email=email)
                validate_password(password1, user)
            except ValidationError as error:
                raise ValidationError(error.messages)
        return password1

    def clean_password2(self):
        """
        Override the default clean_password2 to avoid duplicate validation errors.

        Only check if passwords match, not password strength (already done in clean_password1).

        Returns:
            str: The cleaned password2.

        Raises:
            ValidationError: If passwords don't match.
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _("The two password fields didn't match."),
                code='password_mismatch',
            )
        return password2

    def _post_clean(self):
        """
        Override _post_clean to prevent duplicate password validation.

        UserCreationForm's _post_clean validates passwords and adds errors to password2.
        Since we already validate in clean_password1, we skip the password validation
        but still call the parent's _post_clean for other processing.
        """
        # Call ModelForm's _post_clean, but skip UserCreationForm's password validation
        super(UserCreationForm, self)._post_clean()
        # Note: We don't call UserCreationForm's _post_clean to avoid duplicate validation

    def validate_password_for_user(self, user):
        """
        Override to prevent password validation errors from being added to password2.

        Since we already validate passwords in clean_password1, we don't need
        to validate them again here. This prevents duplicate validation and
        ensures errors appear on the password1 field.
        """
        # Do nothing - password validation is handled in clean_password1
        # This prevents Django from running validation again and adding errors to password2
        pass

    def clean_agree_to_terms(self):
        """
        Ensure the user has agreed to the terms of service.

        Raises:
            ValidationError: If the terms checkbox is not checked.

        Returns:
            bool: True if terms are agreed to.
        """
        agree_to_terms = self.cleaned_data.get('agree_to_terms')
        if not agree_to_terms:
            raise ValidationError(
                _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
                code='terms_required'
            )
        return agree_to_terms

    def save(self, commit=True):
        """
        Save the user instance and create a customer profile.

        Args:
            commit (bool): Whether to save to database immediately.

        Returns:
            CustomUser: The saved user instance.
        """
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.is_customer = True
        user.is_active = False  # Require email verification
        
        if commit:
            user.save()
            # Create customer profile
            from ...models import CustomerProfile
            CustomerProfile.objects.create(user=user)
            
        return user


class CustomerLoginForm(AccessibleFormMixin, forms.Form):
    """
    Authenticate a customer using email and password.

    Features:
    - Role validation
    - Active account check
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        )
    )
    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Enter your password'),
                'autocomplete': 'current-password',
                'required': True,
                'type': 'password',
            }
        )
    )

    def __init__(self, request=None, *args, **kwargs):
        """
        Initialize the form with request context.

        Args:
            request: The HTTP request object
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        self.request = request
        self.user_cache = None
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        self.helper.layout = Layout(
            Field('email', css_class='form-control'),
            Field('password', css_class='form-control'),
            FormActions(
                Submit('login', _('Sign In'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean(self):
        """
        Validate the form data and authenticate the user.

        Returns:
            dict: The cleaned form data.

        Raises:
            ValidationError: If authentication fails.
        """
        email = self.cleaned_data.get('email')
        password = self.cleaned_data.get('password')

        if email and password:
            self.user_cache = authenticate(self.request, email=email, password=password)
            if self.user_cache is None:
                raise ValidationError(
                    _('Please enter a correct email and password. Note that both fields may be case-sensitive.'),
                    code='invalid_login',
                )
            elif not self.user_cache.is_active:
                raise ValidationError(
                    _('This account is inactive.'),
                    code='inactive',
                )
            elif not self.user_cache.is_customer:
                raise ValidationError(
                    _('This account is not a customer account.'),
                    code='invalid_role',
                )
        return self.cleaned_data

    def get_user(self):
        """
        Return the authenticated user.

        Returns:
            CustomUser: The authenticated user instance.
        """
        return self.user_cache


def authenticate(request, email=None, password=None):
    """
    Authenticate a user with email and password.

    Args:
        request: The HTTP request object
        email (str): The user's email address
        password (str): The user's password

    Returns:
        CustomUser: The authenticated user or None if authentication fails
    """
    from django.contrib.auth import authenticate as django_authenticate
    if email is None:
        email = request.POST.get('email')
    if password is None:
        password = request.POST.get('password')
    if email is None or password is None:
        return None
    try:
        user = CustomUser.objects.get(email=email)
        if django_authenticate(request, username=user.username, password=password):
            return user
    except CustomUser.DoesNotExist:
        pass
    return None 