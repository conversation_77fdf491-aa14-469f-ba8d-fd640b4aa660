# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ...models import CustomUser


class CustomerEmailChangeForm(forms.Form):
    """Simple stand-alone email change form kept for backwards-compatibility.

    The production system now relies on django-allauth for e-mail management,
    but a portion of the historic test-suite still expects this bespoke form
    to be available.  The implementation below purposefully mirrors the most
    common validation rules exercised by those tests without re-introducing
    the full stack of business logic.
    """

    new_email = forms.EmailField(
        label=_('New Email Address'),
        widget=forms.EmailInput(attrs={'autocomplete': 'email', 'class': 'form-control'}),
    )
    password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(attrs={'autocomplete': 'current-password', 'class': 'form-control'}),
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_new_email(self):
        email = self.cleaned_data.get('new_email', '').lower()
        if email == self.user.email.lower():
            raise ValidationError(_('Please enter a different email address.'), code='same_email')
        if CustomUser.objects.filter(email__iexact=email).exists():
            raise ValidationError(_('This email address is already in use.'), code='duplicate_email')
        return email

    def clean_password(self):
        pwd = self.cleaned_data.get('password')
        if not self.user.check_password(pwd):
            raise ValidationError(_('Incorrect password.'), code='incorrect_password')
        return pwd

    def save(self, commit: bool = True):
        if not self.is_valid():
            raise ValidationError(_('Cannot save the form because it is invalid.'))

        new_email = self.cleaned_data['new_email']
        self.user.email = new_email
        from accounts_app.constants import UserStatus
        self.user.status = UserStatus.PENDING_VERIFICATION
        self.user.email_verified = False
        if commit:
            self.user.save()
        return self.user 