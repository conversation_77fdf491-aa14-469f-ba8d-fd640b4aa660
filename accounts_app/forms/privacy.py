"""
Privacy settings and verification forms for accounts_app.

This module contains forms for managing user privacy settings,
data export functionality, and verification badge management.

Note: This file is maintained for backward compatibility.
The forms have been moved to the privacy/ subdirectory for better organization.
"""

# Import all forms from the privacy subdirectory
from .privacy.settings_forms import PrivacySettingsForm
from .privacy.data_export_forms import DataExportForm
from .privacy.verification_forms import VerificationRequestForm
from .privacy.deletion_forms import DataDeletionRequestForm
from .privacy.toggle_forms import PrivacyQuickToggleForm

__all__ = [
    'PrivacySettingsForm',
    'DataExportForm',
    'VerificationRequestForm', 
    'DataDeletionRequestForm',
    'PrivacyQuickToggleForm',
] 