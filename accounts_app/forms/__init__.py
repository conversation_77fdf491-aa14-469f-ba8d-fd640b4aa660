"""
Forms package for accounts_app.

This package contains all form classes organized by feature area for better maintainability.
All forms are imported here to maintain backward compatibility.
"""

# --- Local App Imports ---
from .common import AccountDeactivationForm, AccessibleFormMixin
from .customer import (
    # Authentication forms
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerSignupForm,
    CustomerProfileForm,
)
from .provider import (
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
    ServiceProviderLoginForm,
    ServiceProviderPasswordChangeForm,
)
from .team import TeamMemberForm


# Make all forms available at package level for backward compatibility
__all__ = [
    # Common forms and mixins
    'AccessibleFormMixin',
    'AccountDeactivationForm',
    
    # Customer forms
    'CustomerLoginForm',
    'CustomerPasswordChangeForm',
    'CustomerSignupForm',
    'CustomerProfileForm',
    
    # Service provider forms
    'ServiceProviderProfileForm',
    'ServiceProviderSignupForm',
    'ServiceProviderLoginForm',
    'ServiceProviderPasswordChangeForm',
    
    # Team management forms
    'TeamMemberForm',
]
