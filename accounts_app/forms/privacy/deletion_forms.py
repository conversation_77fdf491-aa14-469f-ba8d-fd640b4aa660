"""
Data deletion forms for accounts_app.

This module contains forms for requesting data deletion (GDPR right to be forgotten).
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, HTML, Submit
from crispy_forms.bootstrap import FormActions

# Local imports
from ..common import AccessibleFormMixin


class DataDeletionRequestForm(AccessibleFormMixin, forms.Form):
    """
    Form for requesting data deletion (GDPR right to be forgotten).
    """
    
    confirm_email = forms.EmailField(
        label=_('Confirm your email address'),
        help_text=_('Enter your email address to confirm data deletion request'),
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email'
            }
        )
    )
    
    deletion_reason = forms.CharField(
        label=_('Reason for deletion (optional)'),
        help_text=_('Please let us know why you want to delete your data'),
        required=False,
        widget=forms.Textarea(
            attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Optional: Tell us why you want to delete your data')
            }
        )
    )
    
    understand_consequences = forms.BooleanField(
        required=True,
        label=_('I understand that this action cannot be undone'),
        help_text=_('Once deleted, your data cannot be recovered'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        label=_('I confirm that I want to delete all my data'),
        help_text=_('This will permanently remove all your personal data'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'data-deletion-form'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-danger">'),
            HTML('<h5>⚠️ Warning: Data Deletion Request</h5>'),
            HTML('<p>This action will permanently delete all your personal data including:</p>'),
            HTML('<ul>'),
            HTML('<li>Account information</li>'),
            HTML('<li>Profile data</li>'),
            HTML('<li>Activity history</li>'),
            HTML('<li>Verification data</li>'),
            HTML('<li>Privacy settings</li>'),
            HTML('</ul>'),
            HTML('<p><strong>This action cannot be undone!</strong></p>'),
            HTML('</div>'),
            
            Field('confirm_email', css_class='mb-3'),
            Field('deletion_reason', css_class='mb-3'),
            Field('understand_consequences', css_class='mb-3'),
            Field('confirm_deletion', css_class='mb-3'),
            
            FormActions(
                Submit('delete', _('Delete My Data'), css_class='btn btn-danger'),
                css_class='d-grid gap-2'
            )
        )
    
    def clean_confirm_email(self):
        """Validate that email matches the user's email."""
        email = self.cleaned_data.get('confirm_email')
        
        if email and self.user and email != self.user.email:
            raise ValidationError(
                _('Email address does not match your account email.'),
                code='email_mismatch'
            )
        
        return email 