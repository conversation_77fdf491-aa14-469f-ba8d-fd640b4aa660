"""
Data export forms for accounts_app.

This module contains forms for requesting data export functionality.
"""

from django import forms
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, HTML, Submit
from crispy_forms.bootstrap import FormActions

# Local imports
from ..common import AccessibleFormMixin


class DataExportForm(AccessibleFormMixin, forms.Form):
    """
    Form for requesting data export.
    """
    
    EXPORT_FORMAT_CHOICES = [
        ('json', _('JSON - Machine readable format')),
        ('csv', _('CSV - Spreadsheet format')),
        ('xml', _('XML - Structured format')),
    ]
    
    export_format = forms.ChoiceField(
        choices=EXPORT_FORMAT_CHOICES,
        initial='json',
        widget=forms.RadioSelect(
            attrs={
                'class': 'form-check-input'
            }
        ),
        label=_('Export Format'),
        help_text=_('Choose the format for your data export')
    )
    
    include_deleted_data = forms.BooleanField(
        required=False,
        initial=False,
        label=_('Include deleted data'),
        help_text=_('Include data that has been marked for deletion but not yet purged'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'role': 'switch'
            }
        )
    )
    
    confirm_export = forms.BooleanField(
        required=True,
        label=_('I confirm that I want to export my data'),
        help_text=_('This will generate a file containing all your personal data'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'data-export-form'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-info">'),
            HTML('<h5>Data Export Information</h5>'),
            HTML('<p>Your data export will include:</p>'),
            HTML('<ul>'),
            HTML('<li>Account information</li>'),
            HTML('<li>Profile data</li>'),
            HTML('<li>Privacy settings</li>'),
            HTML('<li>Activity history</li>'),
            HTML('<li>Verification data</li>'),
            HTML('</ul>'),
            HTML('</div>'),
            
            Field('export_format', css_class='mb-3'),
            Field('include_deleted_data', css_class='mb-3'),
            Field('confirm_export', css_class='mb-3'),
            
            FormActions(
                Submit('export', _('Export My Data'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        ) 