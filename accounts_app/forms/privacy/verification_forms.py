"""
Verification forms for accounts_app.

This module contains forms for requesting profile verification.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, HTML, Submit
from crispy_forms.bootstrap import FormActions

# Local imports
from ...models import ProfileVerification
from ..common import AccessibleFormMixin


class VerificationRequestForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for requesting profile verification.
    """
    
    class Meta:
        model = ProfileVerification
        fields = ['verification_type', 'verification_document']
        widgets = {
            'verification_type': forms.Select(
                attrs={
                    'class': 'form-select',
                    'aria-describedby': 'verification-type-help'
                }
            ),
            'verification_document': forms.FileInput(
                attrs={
                    'class': 'form-control',
                    'accept': '.pdf,.jpg,.jpeg,.png,.gif',
                    'aria-describedby': 'verification-document-help'
                }
            )
        }
    
    confirm_accuracy = forms.BooleanField(
        required=True,
        label=_('I confirm that all information provided is accurate'),
        help_text=_('False information may result in account suspension'),
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'required': True
            }
        )
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        # Filter verification types based on user type and existing verifications
        if user:
            self._filter_verification_types(user)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'verification-request-form'
        self.helper.form_enctype = 'multipart/form-data'
        
        self.helper.layout = Layout(
            HTML('<div class="alert alert-warning">'),
            HTML('<h5>Verification Requirements</h5>'),
            HTML('<p>Please ensure that:</p>'),
            HTML('<ul>'),
            HTML('<li>All information is accurate and up to date</li>'),
            HTML('<li>Documents are clear and legible</li>'),
            HTML('<li>Files are in PDF, JPG, PNG, or GIF format</li>'),
            HTML('<li>File size is under 5MB</li>'),
            HTML('</ul>'),
            HTML('</div>'),
            
            Field('verification_type'),
            HTML('<small id="verification-type-help" class="form-text text-muted">Select the type of verification you want to request</small>'),
            
            Field('verification_document', css_class='mb-3'),
            HTML('<small id="verification-document-help" class="form-text text-muted">Upload supporting documents (PDF, JPG, PNG, GIF - max 5MB)</small>'),
            
            Field('confirm_accuracy', css_class='mb-3'),
            
            FormActions(
                Submit('submit', _('Submit Verification Request'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        )
    
    def _filter_verification_types(self, user):
        """Filter verification types based on user and existing verifications."""
        available_types = []
        
        # Get existing verifications
        existing_verifications = ProfileVerification.objects.filter(
            user=user,
            status__in=['verified', 'pending']
        ).values_list('verification_type', flat=True)
        
        # Email verification - always available if not already verified
        if 'email' not in existing_verifications:
            available_types.append(('email', _('Email Verification')))
        
        # Phone verification - available if user has phone number
        if 'phone' not in existing_verifications:
            has_phone = False
            if hasattr(user, 'customer_profile') and user.customer_profile.phone_number:
                has_phone = True
            elif hasattr(user, 'service_provider_profile') and user.service_provider_profile.phone:
                has_phone = True
            
            if has_phone:
                available_types.append(('phone', _('Phone Verification')))
        
        # Identity verification - always available
        if 'identity' not in existing_verifications:
            available_types.append(('identity', _('Identity Verification')))
        
        # Business verification - for service providers only
        if user.is_service_provider and 'business' not in existing_verifications:
            available_types.append(('business', _('Business Verification')))
        
        # Address verification - always available
        if 'address' not in existing_verifications:
            available_types.append(('address', _('Address Verification')))
        
        # Payment verification - always available
        if 'payment' not in existing_verifications:
            available_types.append(('payment', _('Payment Method Verification')))
        
        # Update field choices
        self.fields['verification_type'].choices = available_types
        
        if not available_types:
            self.fields['verification_type'].widget.attrs['disabled'] = True
            self.fields['verification_type'].help_text = _('No verification types available.')
    
    def clean_verification_document(self):
        """Validate uploaded document."""
        document = self.cleaned_data.get('verification_document')
        
        if document:
            # Check file size (5MB limit)
            if document.size > 5 * 1024 * 1024:
                raise ValidationError(
                    _('File size must be under 5MB.'),
                    code='file_too_large'
                )
            
            # Check file type
            allowed_types = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif']
            if document.content_type not in allowed_types:
                raise ValidationError(
                    _('File must be PDF, JPG, PNG, or GIF format.'),
                    code='invalid_file_type'
                )
        
        return document 