"""
Privacy toggle forms for accounts_app.

This module contains forms for quick privacy setting toggles via AJAX.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field

# Local imports
from ..common import AccessibleFormMixin


class PrivacyQuickToggleForm(AccessibleFormMixin, forms.Form):
    """
    Form for quick privacy setting toggles via AJAX.
    """
    
    field_name = forms.CharField(
        max_length=50,
        widget=forms.HiddenInput()
    )
    
    current_value = forms.BooleanField(
        required=False,
        widget=forms.HiddenInput()
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'privacy-quick-toggle-form'
        
        self.helper.layout = Layout(
            Field('field_name'),
            Field('current_value'),
        )
    
    def clean_field_name(self):
        """Validate field name."""
        field_name = self.cleaned_data.get('field_name')
        
        # List of valid privacy setting fields
        valid_fields = [
            'show_email', 'show_phone', 'show_address', 'show_birth_date',
            'show_gender', 'show_profile_picture', 'show_last_seen',
            'show_booking_history', 'show_reviews', 'show_favorites',
            'discoverable_in_search', 'allow_friend_requests', 'allow_messages',
            'allow_analytics', 'allow_personalized_ads', 'marketing_consent',
            'third_party_sharing'
        ]
        
        if field_name not in valid_fields:
            raise ValidationError(
                _('Invalid privacy setting field.'),
                code='invalid_field'
            )
        
        return field_name 