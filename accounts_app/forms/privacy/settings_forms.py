"""
Privacy settings forms for accounts_app.

This module contains forms for managing user privacy settings.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Crispy Forms imports
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit, Row, Column
from crispy_forms.bootstrap import FormActions

# Local imports
from ...models import ProfilePrivacySettings
from ..common import AccessibleFormMixin


class PrivacySettingsForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for managing user privacy settings.
    """
    
    class Meta:
        model = ProfilePrivacySettings
        fields = [
            'profile_visibility',
            'show_email',
            'show_phone',
            'show_address',
            'show_birth_date',
            'show_gender',
            'show_profile_picture',
            'show_last_seen',
            'show_booking_history',
            'show_reviews',
            'show_favorites',
            'discoverable_in_search',
            'allow_friend_requests',
            'allow_messages',
            'allow_analytics',
            'allow_personalized_ads',
            'data_processing_consent',
            'marketing_consent',
            'third_party_sharing',
        ]
        widgets = {
            'profile_visibility': forms.Select(
                attrs={
                    'class': 'form-select',
                    'aria-describedby': 'profile-visibility-help'
                }
            ),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Configure form helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'privacy-settings-form'
        
        # Group fields by category
        self.helper.layout = Layout(
            HTML('<h4>Profile Visibility</h4>'),
            Div(
                Field('profile_visibility'),
                HTML('<small id="profile-visibility-help" class="form-text text-muted">Control who can see your profile</small>'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Profile Information</h4>'),
            Div(
                Row(
                    Column('show_email', css_class='form-group col-md-6 mb-3'),
                    Column('show_phone', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Row(
                    Column('show_address', css_class='form-group col-md-6 mb-3'),
                    Column('show_birth_date', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Row(
                    Column('show_gender', css_class='form-group col-md-6 mb-3'),
                    Column('show_profile_picture', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('show_last_seen', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Activity & Interactions</h4>'),
            Div(
                Row(
                    Column('show_booking_history', css_class='form-group col-md-6 mb-3'),
                    Column('show_reviews', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('show_favorites', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Search & Discovery</h4>'),
            Div(
                Row(
                    Column('discoverable_in_search', css_class='form-group col-md-6 mb-3'),
                    Column('allow_friend_requests', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('allow_messages', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            HTML('<h4>Data & Analytics</h4>'),
            Div(
                Row(
                    Column('allow_analytics', css_class='form-group col-md-6 mb-3'),
                    Column('allow_personalized_ads', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                css_class='mb-4'
            ),
            
            HTML('<h4>GDPR Compliance</h4>'),
            Div(
                Row(
                    Column('data_processing_consent', css_class='form-group col-md-6 mb-3'),
                    Column('marketing_consent', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('third_party_sharing', css_class='form-group mb-3'),
                css_class='mb-4'
            ),
            
            FormActions(
                Submit('save', _('Save Privacy Settings'), css_class='btn btn-primary'),
                css_class='d-grid gap-2'
            )
        )
        
        # Add Bootstrap classes to checkboxes
        for field_name in self.fields:
            field = self.fields[field_name]
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({
                    'class': 'form-check-input',
                    'role': 'switch'
                })
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Validate GDPR compliance
        if not cleaned_data.get('data_processing_consent'):
            raise ValidationError(
                _('Data processing consent is required to use this service.'),
                code='required_consent'
            )
        
        return cleaned_data 