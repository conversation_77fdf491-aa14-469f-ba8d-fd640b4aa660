# --- Django Imports ---
from django.db import models
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .profiles import ServiceProviderProfile
from ..utils import get_staff_profile_image_path, validate_image_file


class TeamMember(models.Model):
    """Represents a staff member associated with a service provider business"""
    service_provider = models.ForeignKey(
        ServiceProviderProfile,
        on_delete=models.CASCADE,
        related_name='team',
        verbose_name=_('service provider'),
        help_text=_('Business this team member belongs to')
    )
    name = models.CharField(
        _('full name'),
        max_length=100,
        help_text=_('Team member\'s full name')
    )
    position = models.CharField(
        _('position'),
        max_length=100,
        help_text=_('Role or job title')
    )
    photo = models.ImageField(
        _('profile photo'),
        upload_to=get_staff_profile_image_path,
        blank=True,
        null=True,
        help_text=_('Professional headshot (optional)')
    )
    is_active = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(
        _('active status'),
        default=True,
        help_text=_('Is this team member currently active?')
    )
    created = models.DateTimeField(_('created at'), auto_now_add=True)
    updated = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Team Member')
        verbose_name_plural = _('Team Members')
        ordering = ['name']
        constraints = [
            models.UniqueConstraint(
                fields=['service_provider', 'name'],
                name='unique_team_member'
            )
        ]
        indexes = [
            models.Index(fields=['service_provider'], name='team_member_provider_idx'),
            models.Index(fields=['is_active'], name='team_member_active_idx'),
        ]

    def __str__(self):
        return f"{self.name} - {self.service_provider.business_name}"

    @classmethod
    def max_count(cls) -> int:
        """Maximum number of team members allowed per service provider"""
        return 10

    def clean(self):
        """Validate team member data"""
        super().clean()
        
        # Validate image file if provided
        if self.photo:
            validate_image_file(self.photo) 