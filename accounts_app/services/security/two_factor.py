"""Stub two-factor authentication service for legacy tests."""

class TwoFactorService:  # noqa: D101
    @staticmethod
    def is_two_factor_enabled(user):
        return getattr(user, 'two_factor_enabled', False)

    @staticmethod
    def enable_two_factor(user):  # noqa: D401
        user.two_factor_enabled = True
        user.save(update_fields=['two_factor_enabled'])

    @staticmethod
    def disable_two_factor(user):
        user.two_factor_enabled = False
        user.save(update_fields=['two_factor_enabled']) 