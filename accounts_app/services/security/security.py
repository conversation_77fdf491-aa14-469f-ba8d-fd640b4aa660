"""
Security service for accounts_app.

This module contains the SecurityService class for handling
security-related operations.
"""

import logging
from typing import Dict, <PERSON><PERSON>, Any, Optional
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import CustomUser, UserSecurity, AccountLockout
from ...utils.logging import log_error


logger = logging.getLogger(__name__)


class SecurityService:
    """
    Service for handling security-related operations.
    
    Provides methods for lockout management, security monitoring,
    and threat detection.
    """
    
    @staticmethod
    def check_account_lockout(user: CustomUser, ip_address: str = None) -> Tuple[bool, str]:
        """
        Check if account or IP is locked.
        
        Args:
            user: User object
            ip_address: IP address to check
            
        Returns:
            Tuple of (is_locked, message)
        """
        try:
            # Check IP lockout
            if ip_address and AccountLockout.is_ip_locked(ip_address):
                return True, _("Account is temporarily locked due to multiple failed attempts")
            
            # Check user-specific lockout
            if user:
                security = UserSecurity.objects.filter(user=user).first()
                if security and security.is_account_locked:
                    return True, _("Account is temporarily locked")
            
            return False, ""
            
        except Exception as e:
            logger.error(f"Account lockout check error: {str(e)}", exc_info=True)
            return False, ""
    
    @staticmethod
    def record_security_event(event_type: str, user: CustomUser = None, ip_address: str = None, 
                            details: Dict[str, Any] = None, request=None) -> bool:
        """
        Record security event for monitoring.
        
        Args:
            event_type: Type of security event
            user: User object (optional)
            ip_address: IP address
            details: Additional event details
            request: HTTP request object
            
        Returns:
            Boolean indicating success
        """
        try:
            # Log security event
            log_error(
                error_type='security_event',
                error_message=f"Security event: {event_type}",
                user=user,
                request=request,
                details=details or {}
            )
            
            logger.warning(f"Security event recorded: {event_type}, user: {user.email if user else 'N/A'}, IP: {ip_address}")
            return True
            
        except Exception as e:
            logger.error(f"Security event recording error: {str(e)}", exc_info=True)
            return False 