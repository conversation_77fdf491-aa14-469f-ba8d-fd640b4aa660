"""
Team service for accounts_app.

This module contains the TeamService class for handling
team member operations for service providers.
"""

import logging
from typing import Dict, Tu<PERSON>, Any, Optional
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import ServiceProviderProfile, TeamMember
from ...utils.logging import log_user_activity


logger = logging.getLogger(__name__)


class TeamService:
    """
    Service for handling team member operations for service providers.
    
    Provides methods for managing team members with proper validation
    and security checks.
    """
    
    @staticmethod
    def add_team_member(service_provider: ServiceProviderProfile, member_data: Dict[str, Any], request=None) -> Tuple[bool, Optional[TeamMember], str]:
        """
        Add a team member to a service provider's team.
        
        Args:
            service_provider: ServiceProviderProfile object
            member_data: Dictionary containing team member data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, team_member_object, message)
        """
        try:
            with transaction.atomic():
                # Check team size limit
                current_count = TeamMember.objects.filter(service_provider=service_provider).count()
                if current_count >= TeamMember.max_count():
                    return False, None, _("Maximum team size limit reached")
                
                # Create team member
                team_member = TeamMember.objects.create(
                    service_provider=service_provider,
                    name=member_data['name'],
                    position=member_data['position'],
                    photo=member_data.get('photo'),
                    is_active=member_data.get('is_active', True)
                )
                
                # Log team member addition
                log_user_activity(
                    activity_type='team_member_added',
                    user=service_provider.user,
                    request=request,
                    details={'member_name': member_data['name'], 'position': member_data['position']}
                )
                
                logger.info(f"Team member added: {member_data['name']} to {service_provider.legal_name}")
                return True, team_member, _("Team member added successfully")
                
        except ValidationError as e:
            logger.error(f"Team member validation error: {str(e)}")
            return False, None, _("Team member data is invalid")
        except Exception as e:
            logger.error(f"Team member addition error: {str(e)}", exc_info=True)
            return False, None, _("Failed to add team member. Please try again.")
    
    @staticmethod
    def update_team_member(team_member: TeamMember, member_data: Dict[str, Any], request=None) -> Tuple[bool, str]:
        """
        Update team member information.
        
        Args:
            team_member: TeamMember object
            member_data: Dictionary containing updated member data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                # Track changes for logging
                changed_fields = {}
                
                # Update member fields
                for field, value in member_data.items():
                    if hasattr(team_member, field):
                        old_value = getattr(team_member, field)
                        if old_value != value:
                            setattr(team_member, field, value)
                            changed_fields[field] = {'old': old_value, 'new': value}
                
                # Validate and save
                team_member.full_clean()
                team_member.save()
                
                # Log team member update
                if changed_fields:
                    log_user_activity(
                        activity_type='team_member_updated',
                        user=team_member.service_provider.user,
                        request=request,
                        details={'member_name': team_member.name, 'changed_fields': list(changed_fields.keys())}
                    )
                
                logger.info(f"Team member updated: {team_member.name}, fields: {list(changed_fields.keys())}")
                return True, _("Team member updated successfully")
                
        except ValidationError as e:
            logger.error(f"Team member update validation error: {str(e)}")
            return False, _("Team member data is invalid")
        except Exception as e:
            logger.error(f"Team member update error: {str(e)}", exc_info=True)
            return False, _("Failed to update team member. Please try again.")
    
    @staticmethod
    def remove_team_member(team_member: TeamMember, request=None) -> Tuple[bool, str]:
        """
        Remove a team member.
        
        Args:
            team_member: TeamMember object
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                member_name = team_member.name
                service_provider = team_member.service_provider
                
                # Delete team member
                team_member.delete()
                
                # Log team member removal
                log_user_activity(
                    activity_type='team_member_removed',
                    user=service_provider.user,
                    request=request,
                    details={'member_name': member_name}
                )
                
                logger.info(f"Team member removed: {member_name} from {service_provider.legal_name}")
                return True, _("Team member removed successfully")
                
        except Exception as e:
            logger.error(f"Team member removal error: {str(e)}", exc_info=True)
            return False, _("Failed to remove team member. Please try again.") 