"""
Service layer for accounts_app.

This module provides a unified interface to all account-related services,
including authentication, registration, profile management, and more.
"""

# Import all services to make them available from the services package
from .auth import AuthenticationService, AuthenticationError, UserRegistrationService, SessionService, get_client_ip
from .profile import ProfileService, TeamService
from .security import SecurityService, AccountLockoutService, PasswordService, TwoFactorService
from .compliance import GDPRService, EmailService, EmailVerificationService
from .account import AccountService

# Import utility functions and exceptions
# Import base exception classes that might be used across the codebase
class ServiceError(Exception):
    """Base exception for service layer errors"""
    pass

class ProfileError(ServiceError):
    """Profile-related errors"""
    pass

class SecurityError(ServiceError):
    """Security-related errors"""
    pass

__all__ = [
    'AuthenticationService',
    'AuthenticationError',
    'UserRegistrationService',
    'ProfileService',
    'PasswordService',
    'AccountService',
    'EmailService',
    'EmailVerificationService',
    'TeamService',
    'SecurityService',
    'AccountLockoutService',
    'TwoFactorService',
    'GDPRService',
    'SessionService',
    'get_client_ip',
    'ServiceError',
    'ProfileError',
    'SecurityError',
] 