"""
Account service for accounts_app.

This module contains the AccountService class for handling
account lifecycle operations.
"""

import logging
from typing import Tuple
from django.db import transaction
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import CustomUser
from ...utils.logging import log_account_lifecycle_event


logger = logging.getLogger(__name__)


class AccountService:
    """
    Service for handling account lifecycle operations.
    
    Provides methods for account deactivation, verification, and management
    with proper security and logging.
    """
    
    @staticmethod
    def deactivate_account(user: CustomUser, reason: str = None, request=None) -> Tuple[bool, str]:
        """
        Deactivate user account.
        
        Args:
            user: User object
            reason: Reason for deactivation
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                # Deactivate account
                user.is_active = False
                user.save()
                
                # Log account deactivation
                log_account_lifecycle_event(
                    event_type='deactivation',
                    user=user,
                    request=request,
                    reason=reason or 'user_requested'
                )
                
                logger.info(f"Account deactivated: {user.email}, reason: {reason}")
                return True, _("Account deactivated successfully")
                
        except Exception as e:
            logger.error(f"Account deactivation error: {str(e)}", exc_info=True)
            return False, _("Account deactivation failed. Please try again.")
    
    @staticmethod
    def verify_email(user: CustomUser, request=None) -> Tuple[bool, str]:
        """
        Verify user email address.
        
        Args:
            user: User object
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with transaction.atomic():
                # Verify email and activate account
                user.email_verified = True
                user.is_active = True
                user.save()
                
                # Log email verification
                log_account_lifecycle_event(
                    event_type='email_verification',
                    user=user,
                    request=request,
                    reason='email_verified'
                )
                
                logger.info(f"Email verified successfully: {user.email}")
                return True, _("Email verified successfully")
                
        except Exception as e:
            logger.error(f"Email verification error: {str(e)}", exc_info=True)
            return False, _("Email verification failed. Please try again.") 