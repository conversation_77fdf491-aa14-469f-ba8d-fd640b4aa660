"""
Custom Allauth URLs for Role-Based Email Verification

This module provides custom URL patterns that override django-allauth URLs
to use our custom views for role-based email verification.
"""

from django.urls import path
from allauth.account import views as allauth_views

from .views.allauth_views import CozyWishSignupView, CozyWishLoginView

# Override allauth URLs with our custom views
urlpatterns = [
    # Override signup view
    path('signup/', CozyWishSignupView.as_view(), name='account_signup'),
    
    # Override login view
    path('login/', CozyWishLoginView.as_view(), name='account_login'),
    
    # Keep all other allauth URLs as they are
    path('logout/', allauth_views.LogoutView.as_view(), name='account_logout'),
    path('password/change/', allauth_views.PasswordChangeView.as_view(), name='account_change_password'),
    path('password/set/', allauth_views.PasswordSetView.as_view(), name='account_set_password'),
    path('password/reset/', allauth_views.PasswordResetView.as_view(), name='account_reset_password'),
    path('password/reset/done/', allauth_views.PasswordResetDoneView.as_view(), name='account_reset_password_done'),
    path('password/reset/key/<uidb36>-<key>/', allauth_views.PasswordResetFromKeyView.as_view(), name='account_reset_password_from_key'),
    path('password/reset/key/done/', allauth_views.PasswordResetFromKeyDoneView.as_view(), name='account_reset_password_from_key_done'),
    path('confirm-email/', allauth_views.EmailVerificationSentView.as_view(), name='account_email_verification_sent'),
    path('confirm-email/<key>/', allauth_views.ConfirmEmailView.as_view(), name='account_confirm_email'),
    path('email/', allauth_views.EmailView.as_view(), name='account_email'),
    path('inactive/', allauth_views.AccountInactiveView.as_view(), name='account_inactive'),
] 