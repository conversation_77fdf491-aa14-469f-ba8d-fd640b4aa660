"""
Utils Module for Accounts App

This module provides convenient imports for common utility functions
used throughout the accounts_app.
"""

# --- Helper Functions ---
# Image processing and validation
from .image.processing import (
    process_profile_image,
    create_thumbnail,
)
from .image.validation import (
    validate_image_file,
)

# Profile completion
from .profile.completion import (
    calculate_customer_profile_completion,
    calculate_provider_profile_completion,
    get_profile_completion_suggestions,
)

# File path generators
from .profile.paths import (
    get_customer_profile_image_path,
    get_provider_profile_image_path,
    get_staff_profile_image_path,
    get_profile_thumbnail_path,
)

# --- Logging Functions ---
from .logging import (
    # Client info
    get_client_info,
    
    # Security logging
    log_security_event,
    log_authentication_event,
    
    # Activity logging
    log_user_activity,
    log_profile_change,
    log_team_management_event,
    log_account_lifecycle_event,
    log_data_access_event,
    
    # Error logging
    log_error,
    log_info,
    
    # Performance logging
    log_performance,
    performance_monitor,
    
    # Audit logging
    log_audit_event,
)

# --- Validators ---
from .validators import (
    # Phone validation
    normalize_phone,
    
    # Password validators
    ComplexityPasswordValidator,
    PasswordHistoryValidator,
    CustomMinimumLengthValidator,
    NoPersonalInformationValidator,
    NoSequentialCharactersValidator,
    NoRepeatedCharactersValidator,
)

# --- Convenience module imports ---
# Import entire modules for direct access
from . import image
from . import profile
from . import common

# --- Convenience exports ---
__all__ = [
    # Helper functions
    'validate_image_file',
    'process_profile_image',
    'create_thumbnail',
    'calculate_customer_profile_completion',
    'calculate_provider_profile_completion',
    'get_profile_completion_suggestions',
    'get_customer_profile_image_path',
    'get_provider_profile_image_path',
    'get_staff_profile_image_path',
    'get_profile_thumbnail_path',
    
    # Logging functions
    'get_client_info',
    'log_security_event',
    'log_authentication_event',
    'log_user_activity',
    'log_profile_change',
    'log_team_management_event',
    'log_account_lifecycle_event',
    'log_data_access_event',
    'log_error',
    'log_info',
    'log_performance',
    'performance_monitor',
    'log_audit_event',
    
    # Validators
    'normalize_phone',
    'ComplexityPasswordValidator',
    'PasswordHistoryValidator',
    'CustomMinimumLengthValidator',
    'NoPersonalInformationValidator',
    'NoSequentialCharactersValidator',
    'NoRepeatedCharactersValidator',
] 