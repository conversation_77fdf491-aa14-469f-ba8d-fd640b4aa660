# --- Standard Library Imports ---
from django.core.exceptions import ValidationError
from PIL import Image
from accounts_app.constants import ModelConstants, ValidationMessages


def validate_image_file(image_file) -> None:
    """
    Validates uploaded image file for size and format.
    
    Args:
        image_file: Django uploaded file object
        
    Raises:
        ValidationError: If image is invalid
    """
    if not image_file:
        return
        
    # Check file size
    if image_file.size > ModelConstants.MAX_IMAGE_SIZE_MB * 1024 * 1024:
        raise ValidationError(ValidationMessages.IMAGE_TOO_LARGE)
    
    # Check file format
    try:
        with Image.open(image_file) as img:
            if img.format not in ModelConstants.ALLOWED_IMAGE_FORMATS:
                raise ValidationError(ValidationMessages.INVALID_IMAGE_FORMAT)
    except Exception:
        raise ValidationError(ValidationMessages.INVALID_IMAGE_FORMAT) 