# --- Standard Library Imports ---
import os
import uuid
from typing import Optional


def _generate_filename(prefix: str, identifier: Optional[str], filename: str) -> str:
    """
    Generates a unique filename with prefix and identifier.
    
    Args:
        prefix: Prefix for the filename
        identifier: Optional identifier (usually user ID)
        filename: Original filename
        
    Returns:
        str: Generated unique filename
    """
    ext = filename.split('.')[-1]
    unique_id = uuid.uuid4().hex
    identifier_part = f"{identifier}_" if identifier else ""
    return f"{prefix}_{identifier_part}{unique_id}.{ext}"


def get_customer_profile_image_path(instance, filename: str) -> str:
    """
    Generates file path for customer profile images.
    
    Args:
        instance: Model instance
        filename: Original filename
        
    Returns:
        str: File path for customer profile image
    """
    return os.path.join('customers', 'profile_images', _generate_filename('customer', str(instance.user.id), filename))


def get_provider_profile_image_path(instance, filename: str) -> str:
    """
    Generates file path for provider profile images/logos.
    
    Args:
        instance: Model instance
        filename: Original filename
        
    Returns:
        str: File path for provider profile image
    """
    return os.path.join('providers', 'logos', _generate_filename('provider', str(instance.user.id), filename))


def get_staff_profile_image_path(instance, filename: str) -> str:
    """
    Generates file path for staff profile images.
    
    Args:
        instance: Model instance
        filename: Original filename
        
    Returns:
        str: File path for staff profile image
    """
    return os.path.join('providers', 'staff_images', _generate_filename('staff', None, filename))


def get_profile_thumbnail_path(instance, filename: str) -> str:
    """
    Generates file path for profile picture thumbnails.
    
    Args:
        instance: Model instance
        filename: Original filename
        
    Returns:
        str: File path for profile thumbnail
    """
    return os.path.join('thumbnails', 'profile_images', _generate_filename('thumb', str(instance.user.id), filename)) 