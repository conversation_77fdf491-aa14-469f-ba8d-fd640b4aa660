# --- Standard Library Imports ---
from typing import Dict, Any
from django.utils.translation import gettext_lazy as _


def calculate_customer_profile_completion(user) -> Dict[str, Any]:
    """
    Calculates profile completion percentage for customer users.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing completion percentage and missing fields
    """
    if not user.is_customer:
        return {'percentage': 0, 'missing_fields': [], 'completed_fields': []}
    
    # Get related profiles
    try:
        profile = user.customer_profile
        user_profile = user.user_profile
        preferences = user.user_preferences
    except:
        return {'percentage': 0, 'missing_fields': ['profile_not_created'], 'completed_fields': []}
    
    # Define required fields and their weights
    required_fields = {
        'first_name': {'weight': 15, 'value': profile.first_name},
        'last_name': {'weight': 15, 'value': profile.last_name},
        'profile_picture': {'weight': 10, 'value': profile.profile_picture},
        'phone_number': {'weight': 10, 'value': profile.phone_number},
        'gender': {'weight': 5, 'value': profile.gender},
        'birth_month': {'weight': 5, 'value': profile.birth_month},
        'birth_year': {'weight': 5, 'value': profile.birth_year},
        'address': {'weight': 10, 'value': profile.address},
        'city': {'weight': 10, 'value': profile.city},
        'zip_code': {'weight': 10, 'value': profile.zip_code},
        'email_verified': {'weight': 5, 'value': user.email_verified},
    }
    
    completed_fields = []
    missing_fields = []
    total_score = 0
    
    for field_name, field_info in required_fields.items():
        if field_info['value']:
            completed_fields.append(field_name)
            total_score += field_info['weight']
        else:
            missing_fields.append(field_name)
    
    percentage = min(100, total_score)
    
    return {
        'percentage': percentage,
        'missing_fields': missing_fields,
        'completed_fields': completed_fields,
        'total_fields': len(required_fields)
    }


def calculate_provider_profile_completion(user) -> Dict[str, Any]:
    """
    Calculates profile completion percentage for service provider users.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing completion percentage and missing fields
    """
    if not user.is_service_provider:
        return {'percentage': 0, 'missing_fields': [], 'completed_fields': []}
    
    # Get related profile
    try:
        profile = user.service_provider_profile
        preferences = user.user_preferences
    except:
        return {'percentage': 0, 'missing_fields': ['profile_not_created'], 'completed_fields': []}
    
    # Define required fields and their weights
    required_fields = {
        'legal_name': {'weight': 20, 'value': profile.legal_name},
        'display_name': {'weight': 10, 'value': profile.display_name},
        'description': {'weight': 10, 'value': profile.description},
        'logo': {'weight': 10, 'value': profile.logo},
        'phone': {'weight': 10, 'value': profile.phone},
        'contact_name': {'weight': 10, 'value': profile.contact_name},
        'address': {'weight': 10, 'value': profile.address},
        'city': {'weight': 5, 'value': profile.city},
        'state': {'weight': 5, 'value': profile.state},
        'zip_code': {'weight': 5, 'value': profile.zip_code},
        'email_verified': {'weight': 5, 'value': user.email_verified},
    }
    
    completed_fields = []
    missing_fields = []
    total_score = 0
    
    for field_name, field_info in required_fields.items():
        if field_info['value']:
            completed_fields.append(field_name)
            total_score += field_info['weight']
        else:
            missing_fields.append(field_name)
    
    percentage = min(100, total_score)
    
    return {
        'percentage': percentage,
        'missing_fields': missing_fields,
        'completed_fields': completed_fields,
        'total_fields': len(required_fields)
    }


def get_profile_completion_suggestions(user) -> Dict[str, Any]:
    """
    Returns suggestions for completing user profile.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing suggestions and priority actions
    """
    if user.is_customer:
        completion_data = calculate_customer_profile_completion(user)
    elif user.is_service_provider:
        completion_data = calculate_provider_profile_completion(user)
    else:
        return {'suggestions': [], 'priority_actions': []}
    
    # Define suggestions based on missing fields
    field_suggestions = {
        'first_name': {
            'message': _('Add your first name to personalize your profile'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'last_name': {
            'message': _('Add your last name to complete your identity'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'profile_picture': {
            'message': _('Upload a profile picture to make your profile more personal'),
            'priority': 'medium',
            'action': 'upload_photo'
        },
        'phone_number': {
            'message': _('Add your phone number for better communication'),
            'priority': 'medium',
            'action': 'add_contact_info'
        },
        'address': {
            'message': _('Add your address for location-based services'),
            'priority': 'medium',
            'action': 'add_location'
        },
        'email_verified': {
            'message': _('Verify your email address to secure your account'),
            'priority': 'critical',
            'action': 'verify_email'
        },
        'legal_name': {
            'message': _('Add your business legal name (required for providers)'),
            'priority': 'critical',
            'action': 'complete_business_info'
        },
        'logo': {
            'message': _('Upload your business logo to build brand recognition'),
            'priority': 'medium',
            'action': 'upload_logo'
        },
        'description': {
            'message': _('Add a business description to attract customers'),
            'priority': 'high',
            'action': 'complete_business_info'
        },
    }
    
    suggestions = []
    priority_actions = []
    
    for field in completion_data['missing_fields']:
        if field in field_suggestions:
            suggestion = field_suggestions[field]
            suggestions.append(suggestion)
            
            if suggestion['priority'] == 'critical':
                priority_actions.append(suggestion)
    
    # Sort suggestions by priority
    priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
    suggestions.sort(key=lambda x: priority_order.get(x['priority'], 3))
    
    return {
        'suggestions': suggestions,
        'priority_actions': priority_actions,
        'completion_percentage': completion_data['percentage'],
        'missing_fields_count': len(completion_data['missing_fields'])
    } 