#!/usr/bin/env python3
"""
Test runner for customer forms.

This script provides a convenient way to run tests specifically for the refactored
customer forms. It can be used to verify that the refactoring didn't break any
functionality and that all forms work correctly.

Usage:
    python test_customer_forms_runner.py
    python test_customer_forms_runner.py --verbose
    python test_customer_forms_runner.py --coverage
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings.development')

def run_tests(verbose=False, coverage=False):
    """Run the customer forms tests."""
    
    # Import Django settings
    import django
    django.setup()
    
    # Build the test command
    test_path = "accounts_app.tests.test_customer_forms"
    cmd = [sys.executable, "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=accounts_app.forms.customer",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=90"
        ])
    
    cmd.append(test_path)
    
    print(f"Running tests with command: {' '.join(cmd)}")
    print("=" * 60)
    
    # Run the tests
    result = subprocess.run(cmd, cwd=project_root)
    
    if result.returncode == 0:
        print("\n" + "=" * 60)
        print("✅ All tests passed!")
        
        if coverage:
            print("\n📊 Coverage report generated in htmlcov/")
            print("Open htmlcov/index.html in your browser to view detailed coverage.")
    else:
        print("\n" + "=" * 60)
        print("❌ Some tests failed!")
        sys.exit(1)

def run_specific_form_tests(form_name=None, verbose=False):
    """Run tests for a specific form."""
    
    # Import Django settings
    import django
    django.setup()
    
    # Map form names to test classes
    form_test_map = {
        'signup': 'CustomerSignupFormTest',
        'login': 'CustomerLoginFormTest',
        'password': 'CustomerPasswordChangeFormTest',
        'profile': 'CustomerProfileFormTest',
        'email': 'CustomerEmailChangeFormTest',
        'integration': 'CustomerFormsIntegrationTest',
    }
    
    if form_name and form_name not in form_test_map:
        print(f"❌ Unknown form: {form_name}")
        print(f"Available forms: {', '.join(form_test_map.keys())}")
        sys.exit(1)
    
    # Build the test command
    test_path = "accounts_app.tests.test_customer_forms"
    if form_name:
        test_class = form_test_map[form_name]
        test_path += f"::{test_class}"
    
    cmd = [sys.executable, "-m", "pytest", test_path]
    
    if verbose:
        cmd.append("-v")
    
    print(f"Running tests for: {form_name or 'all forms'}")
    print(f"Command: {' '.join(cmd)}")
    print("=" * 60)
    
    # Run the tests
    result = subprocess.run(cmd, cwd=project_root)
    
    if result.returncode == 0:
        print("\n" + "=" * 60)
        print("✅ Tests passed!")
    else:
        print("\n" + "=" * 60)
        print("❌ Tests failed!")
        sys.exit(1)

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(
        description="Run tests for refactored customer forms",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_customer_forms_runner.py                    # Run all tests
  python test_customer_forms_runner.py --verbose          # Run with verbose output
  python test_customer_forms_runner.py --coverage         # Run with coverage report
  python test_customer_forms_runner.py --form signup      # Test only signup form
  python test_customer_forms_runner.py --form profile -v  # Test profile form with verbose output
        """
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Run tests with verbose output'
    )
    
    parser.add_argument(
        '--coverage', '-c',
        action='store_true',
        help='Generate coverage report'
    )
    
    parser.add_argument(
        '--form', '-f',
        choices=['signup', 'login', 'password', 'profile', 'email', 'integration'],
        help='Run tests for a specific form only'
    )
    
    args = parser.parse_args()
    
    print("🧪 Customer Forms Test Runner")
    print("=" * 60)
    print("Testing refactored customer forms structure:")
    print("  📁 accounts_app/forms/customer/")
    print("  ├── auth_forms.py (CustomerSignupForm, CustomerLoginForm)")
    print("  ├── password_forms.py (CustomerPasswordChangeForm)")
    print("  ├── profile_forms.py (CustomerProfileForm)")
    print("  └── email_forms.py (CustomerEmailChangeForm)")
    print()
    
    if args.form:
        run_specific_form_tests(args.form, args.verbose)
    else:
        run_tests(args.verbose, args.coverage)

if __name__ == "__main__":
    main() 