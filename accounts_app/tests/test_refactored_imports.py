"""
Test to verify that the refactored customer forms imports work correctly.

This test ensures that the refactoring didn't break any existing import patterns
and that all forms are accessible through the expected import paths.
"""

from django.test import TestCase


class RefactoredImportsTest(TestCase):
    """Test that all customer forms can be imported correctly."""

    def test_import_from_customer_package(self):
        """Test importing from the customer package."""
        try:
            from accounts_app.forms.customer import (
                CustomerSignupForm,
                CustomerLoginForm,
                CustomerPasswordChangeForm,
                CustomerProfileForm,
                CustomerEmailChangeForm,
            )
            
            # Verify all forms are imported
            self.assertTrue(CustomerSignupForm)
            self.assertTrue(CustomerLoginForm)
            self.assertTrue(CustomerPasswordChangeForm)
            self.assertTrue(CustomerProfileForm)
            self.assertTrue(CustomerEmailChangeForm)
            
        except ImportError as e:
            self.fail(f"Failed to import customer forms: {e}")

    def test_import_from_forms_package(self):
        """Test importing from the main forms package."""
        try:
            from accounts_app.forms import (
                CustomerSignupForm,
                CustomerLoginForm,
                CustomerPasswordChangeForm,
                CustomerProfileForm,
                CustomerEmailChangeForm,
            )
            
            # Verify all forms are imported
            self.assertTrue(CustomerSignupForm)
            self.assertTrue(CustomerLoginForm)
            self.assertTrue(CustomerPasswordChangeForm)
            self.assertTrue(CustomerProfileForm)
            self.assertTrue(CustomerEmailChangeForm)
            
        except ImportError as e:
            self.fail(f"Failed to import customer forms from main forms package: {e}")

    def test_import_from_individual_modules(self):
        """Test importing from individual modules."""
        try:
            from accounts_app.forms.customer.auth_forms import CustomerSignupForm, CustomerLoginForm
            from accounts_app.forms.customer.password_forms import CustomerPasswordChangeForm
            from accounts_app.forms.customer.profile_forms import CustomerProfileForm
            from accounts_app.forms.customer.email_forms import CustomerEmailChangeForm
            
            # Verify all forms are imported
            self.assertTrue(CustomerSignupForm)
            self.assertTrue(CustomerLoginForm)
            self.assertTrue(CustomerPasswordChangeForm)
            self.assertTrue(CustomerProfileForm)
            self.assertTrue(CustomerEmailChangeForm)
            
        except ImportError as e:
            self.fail(f"Failed to import customer forms from individual modules: {e}")

    def test_form_class_names(self):
        """Test that form classes have the expected names."""
        from accounts_app.forms.customer import (
            CustomerSignupForm,
            CustomerLoginForm,
            CustomerPasswordChangeForm,
            CustomerProfileForm,
            CustomerEmailChangeForm,
        )
        
        self.assertEqual(CustomerSignupForm.__name__, 'CustomerSignupForm')
        self.assertEqual(CustomerLoginForm.__name__, 'CustomerLoginForm')
        self.assertEqual(CustomerPasswordChangeForm.__name__, 'CustomerPasswordChangeForm')
        self.assertEqual(CustomerProfileForm.__name__, 'CustomerProfileForm')
        self.assertEqual(CustomerEmailChangeForm.__name__, 'CustomerEmailChangeForm')

    def test_form_module_locations(self):
        """Test that forms are in the expected modules."""
        from accounts_app.forms.customer import (
            CustomerSignupForm,
            CustomerLoginForm,
            CustomerPasswordChangeForm,
            CustomerProfileForm,
            CustomerEmailChangeForm,
        )
        
        # Check that forms are in the correct modules
        self.assertIn('auth_forms', CustomerSignupForm.__module__)
        self.assertIn('auth_forms', CustomerLoginForm.__module__)
        self.assertIn('password_forms', CustomerPasswordChangeForm.__module__)
        self.assertIn('profile_forms', CustomerProfileForm.__module__)
        self.assertIn('email_forms', CustomerEmailChangeForm.__module__)

    def test_backward_compatibility(self):
        """Test that the old import path still works (redirect)."""
        try:
            from accounts_app.forms.customer import (
                CustomerSignupForm,
                CustomerLoginForm,
                CustomerPasswordChangeForm,
                CustomerProfileForm,
                CustomerEmailChangeForm,
            )
            
            # This should work without any changes
            self.assertTrue(all([
                CustomerSignupForm,
                CustomerLoginForm,
                CustomerPasswordChangeForm,
                CustomerProfileForm,
                CustomerEmailChangeForm,
            ]))
            
        except ImportError as e:
            self.fail(f"Backward compatibility broken: {e}")

    def test_package_all_exports(self):
        """Test that __all__ exports are correct."""
        from accounts_app.forms.customer import __all__
        
        expected_exports = [
            'CustomerLoginForm',
            'CustomerSignupForm',
            'CustomerPasswordChangeForm',
            'CustomerProfileForm',
            'CustomerEmailChangeForm',
        ]
        
        for export in expected_exports:
            self.assertIn(export, __all__)
        
        self.assertEqual(len(__all__), len(expected_exports)) 