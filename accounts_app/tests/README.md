# Customer Forms Test Suite

This directory contains comprehensive unit tests for the refactored customer forms in the `accounts_app`.

## 📁 Test Structure

```
accounts_app/tests/
├── __init__.py
├── test_customer_forms.py          # Main test file for all customer forms
├── test_customer_forms_runner.py   # Convenient test runner script
└── README.md                       # This file
```

## 🧪 Test Coverage

The test suite covers all customer forms in the refactored structure:

### Authentication Forms (`auth_forms.py`)
- **CustomerSignupForm**: 8 test methods
  - Valid form submission
  - Required field validation
  - Email uniqueness validation
  - Password confirmation validation
  - Password strength validation
  - Terms agreement validation
  - User creation and profile setup
  - Crispy forms configuration

- **CustomerLoginForm**: 8 test methods
  - Valid form submission
  - Required field validation
  - Invalid credentials handling
  - Non-existent user handling
  - Inactive user handling
  - Non-customer user handling
  - User retrieval
  - Crispy forms configuration

### Password Management (`password_forms.py`)
- **CustomerPasswordChangeForm**: 7 test methods
  - Valid form submission
  - Required field validation
  - Incorrect old password handling
  - Password confirmation validation
  - Password strength validation
  - Password change functionality
  - Crispy forms configuration

### Profile Management (`profile_forms.py`)
- **CustomerProfileForm**: 10 test methods
  - Valid form submission
  - Optional field handling
  - Phone number validation
  - Birth date field cleaning
  - Image validation (valid format)
  - Image validation (invalid format)
  - Image validation (file size)
  - Image processing with mocks
  - Profile saving without image
  - Crispy forms configuration

### Email Management (`email_forms.py`)
- **CustomerEmailChangeForm**: 8 test methods
  - Valid form submission
  - Required field validation
  - Same email validation
  - Duplicate email validation
  - Incorrect password handling
  - Email change functionality
  - Invalid form save handling
  - Case-insensitive email comparison

### Integration Tests
- **CustomerFormsIntegrationTest**: 3 test methods
  - Complete signup to profile workflow
  - Login to password change workflow
  - Email change workflow

## 🚀 Running Tests

### Using the Test Runner Script (Recommended)

```bash
# Run all customer form tests
python accounts_app/tests/test_customer_forms_runner.py

# Run with verbose output
python accounts_app/tests/test_customer_forms_runner.py --verbose

# Run with coverage report
python accounts_app/tests/test_customer_forms_runner.py --coverage

# Test a specific form
python accounts_app/tests/test_customer_forms_runner.py --form signup
python accounts_app/tests/test_customer_forms_runner.py --form profile --verbose

# Available form options: signup, login, password, profile, email, integration
```

### Using pytest directly

```bash
# Run all customer form tests
pytest accounts_app/tests/test_customer_forms.py

# Run with verbose output
pytest accounts_app/tests/test_customer_forms.py -v

# Run a specific test class
pytest accounts_app/tests/test_customer_forms.py::CustomerSignupFormTest

# Run a specific test method
pytest accounts_app/tests/test_customer_forms.py::CustomerSignupFormTest::test_valid_signup_form

# Run with coverage
pytest accounts_app/tests/test_customer_forms.py --cov=accounts_app.forms.customer --cov-report=html
```

### Using Django's test runner

```bash
# Run all tests in the accounts_app
python manage.py test accounts_app.tests

# Run specific test file
python manage.py test accounts_app.tests.test_customer_forms

# Run specific test class
python manage.py test accounts_app.tests.test_customer_forms.CustomerSignupFormTest
```

## 📊 Test Utilities

The test suite includes several utility functions for creating test data:

### Image Creation Utilities
- `create_test_image()`: Creates a valid test image file
- `create_large_test_image()`: Creates a large image for file size testing
- `create_invalid_image_file()`: Creates an invalid file for format testing

### Test Data Setup
Each test class includes proper `setUp()` methods that create:
- Test users with appropriate roles
- Customer profiles
- Valid form data
- Request factories for authentication testing

## 🔧 Test Configuration

The tests use the following configuration:

- **Database**: Uses Django's test database with transaction support
- **Authentication**: Uses RequestFactory for testing authenticated requests
- **File Uploads**: Uses SimpleUploadedFile for testing file uploads
- **Mocking**: Uses unittest.mock for external dependencies
- **Coverage**: Targets 90%+ code coverage for all form modules

## 🎯 Test Categories

### Unit Tests
- Individual form validation
- Field-specific validation
- Form configuration (crispy forms)
- Save methods

### Integration Tests
- Complete user workflows
- Form interactions
- Database operations
- Authentication flows

### Edge Cases
- Invalid data handling
- Error conditions
- Boundary conditions
- Security considerations

## 📈 Coverage Goals

The test suite aims for:
- **90%+ code coverage** for all form modules
- **100% coverage** of validation methods
- **100% coverage** of save methods
- **100% coverage** of error handling paths

## 🐛 Debugging Tests

If tests fail, you can:

1. **Run with verbose output**:
   ```bash
   python accounts_app/tests/test_customer_forms_runner.py --verbose
   ```

2. **Run specific failing tests**:
   ```bash
   python accounts_app/tests/test_customer_forms_runner.py --form signup
   ```

3. **Use pytest debugging**:
   ```bash
   pytest accounts_app/tests/test_customer_forms.py -v -s --tb=short
   ```

4. **Check test database**:
   ```bash
   python manage.py test accounts_app.tests.test_customer_forms --keepdb
   ```

## 🔄 Continuous Integration

These tests are designed to run in CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run Customer Forms Tests
  run: |
    python accounts_app/tests/test_customer_forms_runner.py --coverage
```

## 📝 Adding New Tests

When adding new customer forms or modifying existing ones:

1. **Add test methods** to the appropriate test class
2. **Follow naming convention**: `test_<form_name>_<test_case>`
3. **Include edge cases** and error conditions
4. **Update this README** with new test coverage
5. **Run the test suite** to ensure all tests pass

## 🎉 Success Criteria

The test suite is considered successful when:
- ✅ All tests pass
- ✅ Coverage is 90% or higher
- ✅ No regressions introduced
- ✅ All form functionality works as expected
- ✅ Integration workflows function correctly 