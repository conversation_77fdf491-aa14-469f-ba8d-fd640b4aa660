"""
Comprehensive unit tests for customer forms.

This module tests all customer forms in the refactored structure:
- CustomerSignupForm (auth_forms.py)
- CustomerLoginForm (auth_forms.py)
- CustomerPasswordChangeForm (password_forms.py)
- CustomerProfileForm (profile_forms.py)
- CustomerEmailChangeForm (email_forms.py)
"""

# Standard library imports
import tempfile
from unittest.mock import patch, Mock
from datetime import date

# Django imports
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.password_validation import validate_password

# Local imports
from accounts_app.forms.customer import (
    CustomerSignupForm,
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerProfileForm,
    CustomerEmailChangeForm,
)
from accounts_app.models import CustomUser, CustomerProfile
from accounts_app.constants import Gender, UserStatus

# Test utility functions
import io
from PIL import Image

User = get_user_model()


def create_test_image(filename="test_image.jpg", format="JPEG", size=(300, 200)):
    """Create a test image file for testing."""
    image = Image.new('RGB', size, color='red')
    image_io = io.BytesIO()
    image.save(image_io, format=format)
    image_io.seek(0)
    return SimpleUploadedFile(
        filename,
        image_io.getvalue(),
        content_type=f'image/{format.lower()}'
    )


def create_large_test_image(filename="large_image.jpg", format="JPEG", size_mb=6):
    """Create a large test image file for testing file size validation."""
    large_size = (3000, 3000)
    image = Image.new('RGB', large_size, color='blue')
    image_io = io.BytesIO()
    image.save(image_io, format=format, quality=100)
    image_io.seek(0)
    
    image_data = image_io.getvalue()
    target_size = size_mb * 1024 * 1024
    
    if len(image_data) < target_size:
        padding_size = target_size - len(image_data)
        padding = b'\x00' * padding_size
        image_data = image_data + padding
    
    return SimpleUploadedFile(
        filename,
        image_data,
        content_type=f'image/{format.lower()}'
    )


def create_invalid_image_file(filename="invalid_file.txt"):
    """Create an invalid file (non-image) for testing validation."""
    return SimpleUploadedFile(
        filename,
        b"This is not an image file content",
        content_type="text/plain"
    )


class CustomerSignupFormTest(TestCase):
    """Test the CustomerSignupForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.valid_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123456',
            'password2': 'testpass123456',
            'agree_to_terms': True,
        }

    def test_valid_signup_form(self):
        """Test valid signup form submission."""
        form = CustomerSignupForm(data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_signup_form_required_fields(self):
        """Test that required fields are validated."""
        form = CustomerSignupForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        self.assertIn('password1', form.errors)
        self.assertIn('password2', form.errors)
        self.assertIn('agree_to_terms', form.errors)

    def test_signup_form_email_uniqueness(self):
        """Test that email uniqueness is enforced."""
        # Create a user with the same email
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True
        )
        
        form = CustomerSignupForm(data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    def test_signup_form_password_mismatch(self):
        """Test password confirmation validation."""
        data = self.valid_data.copy()
        data['password2'] = 'differentpassword'
        
        form = CustomerSignupForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password2', form.errors)

    def test_signup_form_weak_password(self):
        """Test password strength validation."""
        data = self.valid_data.copy()
        data['password1'] = 'weak'
        data['password2'] = 'weak'
        
        form = CustomerSignupForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password1', form.errors)

    def test_signup_form_terms_not_agreed(self):
        """Test terms agreement validation."""
        data = self.valid_data.copy()
        data['agree_to_terms'] = False
        
        form = CustomerSignupForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)

    def test_signup_form_save_creates_customer(self):
        """Test that form save creates a customer user and profile."""
        form = CustomerSignupForm(data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        user = form.save()
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.is_customer)
        self.assertFalse(user.is_active)  # Should be inactive until email verification
        
        # Check that customer profile was created
        self.assertTrue(hasattr(user, 'customerprofile'))

    def test_signup_form_crispy_forms_setup(self):
        """Test that crispy forms helper is properly configured."""
        form = CustomerSignupForm()
        self.assertTrue(hasattr(form, 'helper'))
        self.assertEqual(form.helper.form_method, 'post')
        self.assertEqual(form.helper.form_class, 'needs-validation')


class CustomerLoginFormTest(TestCase):
    """Test the CustomerLoginForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True,
            is_active=True
        )
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'testpass123456',
        }

    def test_valid_login_form(self):
        """Test valid login form submission."""
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request, data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_login_form_required_fields(self):
        """Test that required fields are validated."""
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request, data={})
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        self.assertIn('password', form.errors)

    def test_login_form_invalid_credentials(self):
        """Test invalid credentials validation."""
        request = self.factory.post('/login/')
        data = self.valid_data.copy()
        data['password'] = 'wrongpassword'
        
        form = CustomerLoginForm(request=request, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_login_form_nonexistent_user(self):
        """Test login with non-existent user."""
        request = self.factory.post('/login/')
        data = self.valid_data.copy()
        data['email'] = '<EMAIL>'
        
        form = CustomerLoginForm(request=request, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_login_form_inactive_user(self):
        """Test login with inactive user."""
        self.user.is_active = False
        self.user.save()
        
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request, data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_login_form_non_customer_user(self):
        """Test login with non-customer user."""
        self.user.is_customer = False
        self.user.is_service_provider = True
        self.user.save()
        
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request, data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_login_form_get_user(self):
        """Test get_user method returns authenticated user."""
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request, data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        user = form.get_user()
        self.assertEqual(user, self.user)

    def test_login_form_crispy_forms_setup(self):
        """Test that crispy forms helper is properly configured."""
        request = self.factory.post('/login/')
        form = CustomerLoginForm(request=request)
        self.assertTrue(hasattr(form, 'helper'))
        self.assertEqual(form.helper.form_method, 'post')
        self.assertEqual(form.helper.form_class, 'needs-validation')


class CustomerPasswordChangeFormTest(TestCase):
    """Test the CustomerPasswordChangeForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpass123456',
            is_customer=True,
            is_active=True
        )
        self.valid_data = {
            'old_password': 'oldpass123456',
            'new_password1': 'newpass123456',
            'new_password2': 'newpass123456',
        }

    def test_valid_password_change_form(self):
        """Test valid password change form submission."""
        form = CustomerPasswordChangeForm(user=self.user, data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_password_change_form_required_fields(self):
        """Test that required fields are validated."""
        form = CustomerPasswordChangeForm(user=self.user, data={})
        self.assertFalse(form.is_valid())
        self.assertIn('old_password', form.errors)
        self.assertIn('new_password1', form.errors)
        self.assertIn('new_password2', form.errors)

    def test_password_change_form_wrong_old_password(self):
        """Test validation with incorrect old password."""
        data = self.valid_data.copy()
        data['old_password'] = 'wrongpassword'
        
        form = CustomerPasswordChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('old_password', form.errors)

    def test_password_change_form_password_mismatch(self):
        """Test password confirmation validation."""
        data = self.valid_data.copy()
        data['new_password2'] = 'differentpassword'
        
        form = CustomerPasswordChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_password2', form.errors)

    def test_password_change_form_weak_new_password(self):
        """Test password strength validation."""
        data = self.valid_data.copy()
        data['new_password1'] = 'weak'
        data['new_password2'] = 'weak'
        
        form = CustomerPasswordChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_password1', form.errors)

    def test_password_change_form_save(self):
        """Test that form save changes the password."""
        form = CustomerPasswordChangeForm(user=self.user, data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        form.save()
        
        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpass123456'))

    def test_password_change_form_crispy_forms_setup(self):
        """Test that crispy forms helper is properly configured."""
        form = CustomerPasswordChangeForm(user=self.user)
        self.assertTrue(hasattr(form, 'helper'))
        self.assertEqual(form.helper.form_method, 'post')
        self.assertEqual(form.helper.form_class, 'needs-validation')


class CustomerProfileFormTest(TestCase):
    """Test the CustomerProfileForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True,
            is_active=True
        )
        self.profile = CustomerProfile.objects.create(user=self.user)
        
        self.valid_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'phone_number': '+****************',
            'gender': Gender.MALE,
            'birth_month': 1,
            'birth_year': 1990,
            'address': '123 Main St',
            'city': 'New York',
            'zip_code': '10001',
        }

    def test_valid_profile_form(self):
        """Test valid profile form submission."""
        form = CustomerProfileForm(instance=self.profile, data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_profile_form_optional_fields(self):
        """Test that all fields are optional."""
        form = CustomerProfileForm(instance=self.profile, data={})
        self.assertTrue(form.is_valid())

    def test_profile_form_phone_number_validation(self):
        """Test phone number validation."""
        data = self.valid_data.copy()
        data['phone_number'] = 'invalid-phone'
        
        form = CustomerProfileForm(instance=self.profile, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('phone_number', form.errors)

    def test_profile_form_birth_month_cleaning(self):
        """Test birth month cleaning converts empty to None."""
        data = self.valid_data.copy()
        data['birth_month'] = ''
        
        form = CustomerProfileForm(instance=self.profile, data=data)
        self.assertTrue(form.is_valid())
        self.assertIsNone(form.cleaned_data['birth_month'])

    def test_profile_form_birth_year_cleaning(self):
        """Test birth year cleaning converts empty to None."""
        data = self.valid_data.copy()
        data['birth_year'] = ''
        
        form = CustomerProfileForm(instance=self.profile, data=data)
        self.assertTrue(form.is_valid())
        self.assertIsNone(form.cleaned_data['birth_year'])

    def test_profile_form_image_validation_valid(self):
        """Test valid image upload."""
        test_image = create_test_image("test_image.jpg")
        
        form = CustomerProfileForm(
            instance=self.profile,
            data=self.valid_data,
            files={'profile_picture': test_image}
        )
        self.assertTrue(form.is_valid())

    def test_profile_form_image_validation_invalid_format(self):
        """Test invalid image format validation."""
        test_file = create_invalid_image_file("test_file.txt")
        
        form = CustomerProfileForm(
            instance=self.profile,
            data=self.valid_data,
            files={'profile_picture': test_file}
        )
        self.assertFalse(form.is_valid())
        self.assertIn('profile_picture', form.errors)

    def test_profile_form_image_validation_too_large(self):
        """Test image file size validation."""
        test_image = create_large_test_image("large_image.jpg", size_mb=6)
        
        form = CustomerProfileForm(
            instance=self.profile,
            data=self.valid_data,
            files={'profile_picture': test_image}
        )
        self.assertFalse(form.is_valid())
        self.assertIn('profile_picture', form.errors)

    @patch('accounts_app.forms.customer.profile_forms.ProfileImageForm')
    @patch('accounts_app.forms.customer.profile_forms.ImageService')
    def test_profile_form_save_with_image_processing(self, mock_image_service, mock_profile_image_form):
        """Test form save with image processing."""
        test_image = create_test_image("test_image.jpg")
        
        # Mock the image processing
        mock_form_instance = Mock()
        mock_form_instance.is_valid.return_value = True
        mock_form_instance.process.return_value = test_image
        mock_profile_image_form.return_value = mock_form_instance
        
        mock_image_service.save_image.return_value = ('path/to/image.jpg', 'filename.jpg')
        
        form = CustomerProfileForm(
            instance=self.profile,
            data=self.valid_data,
            files={'profile_picture': test_image}
        )
        self.assertTrue(form.is_valid())
        
        saved_profile = form.save()
        self.assertEqual(saved_profile.profile_picture, 'path/to/image.jpg')

    def test_profile_form_save_without_image(self):
        """Test form save without image processing."""
        form = CustomerProfileForm(instance=self.profile, data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        saved_profile = form.save()
        self.assertEqual(saved_profile.first_name, 'John')
        self.assertEqual(saved_profile.last_name, 'Doe')

    def test_profile_form_crispy_forms_setup(self):
        """Test that crispy forms helper is properly configured."""
        form = CustomerProfileForm(instance=self.profile)
        self.assertTrue(hasattr(form, 'helper'))
        self.assertEqual(form.helper.form_method, 'post')
        self.assertEqual(form.helper.form_class, 'needs-validation')
        self.assertEqual(form.helper.form_enctype, 'multipart/form-data')


class CustomerEmailChangeFormTest(TestCase):
    """Test the CustomerEmailChangeForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True,
            is_active=True
        )
        self.valid_data = {
            'new_email': '<EMAIL>',
            'password': 'testpass123456',
        }

    def test_valid_email_change_form(self):
        """Test valid email change form submission."""
        form = CustomerEmailChangeForm(user=self.user, data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_email_change_form_required_fields(self):
        """Test that required fields are validated."""
        form = CustomerEmailChangeForm(user=self.user, data={})
        self.assertFalse(form.is_valid())
        self.assertIn('new_email', form.errors)
        self.assertIn('password', form.errors)

    def test_email_change_form_same_email(self):
        """Test validation when new email is same as current."""
        data = self.valid_data.copy()
        data['new_email'] = '<EMAIL>'
        
        form = CustomerEmailChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_email', form.errors)

    def test_email_change_form_duplicate_email(self):
        """Test validation when new email already exists."""
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True
        )
        
        form = CustomerEmailChangeForm(user=self.user, data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_email', form.errors)

    def test_email_change_form_wrong_password(self):
        """Test validation with incorrect password."""
        data = self.valid_data.copy()
        data['password'] = 'wrongpassword'
        
        form = CustomerEmailChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password', form.errors)

    def test_email_change_form_save(self):
        """Test that form save changes the email and sets status."""
        form = CustomerEmailChangeForm(user=self.user, data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        saved_user = form.save()
        self.assertEqual(saved_user.email, '<EMAIL>')
        self.assertEqual(saved_user.status, UserStatus.PENDING_VERIFICATION)
        self.assertFalse(saved_user.email_verified)

    def test_email_change_form_save_invalid_form(self):
        """Test that save raises error for invalid form."""
        form = CustomerEmailChangeForm(user=self.user, data={})
        self.assertFalse(form.is_valid())
        
        with self.assertRaises(ValidationError):
            form.save()

    def test_email_change_form_email_case_insensitive(self):
        """Test that email comparison is case insensitive."""
        data = self.valid_data.copy()
        data['new_email'] = '<EMAIL>'
        
        form = CustomerEmailChangeForm(user=self.user, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_email', form.errors)


class CustomerFormsIntegrationTest(TestCase):
    """Integration tests for customer forms working together."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()

    def test_signup_to_profile_workflow(self):
        """Test complete workflow from signup to profile editing."""
        # Step 1: Sign up
        signup_data = {
            'email': '<EMAIL>',
            'password1': 'workflowpass123456',
            'password2': 'workflowpass123456',
            'agree_to_terms': True,
        }
        
        signup_form = CustomerSignupForm(data=signup_data)
        self.assertTrue(signup_form.is_valid())
        user = signup_form.save()
        
        # Step 2: Create profile
        profile = CustomerProfile.objects.get(user=user)
        
        # Step 3: Edit profile
        profile_data = {
            'first_name': 'Workflow',
            'last_name': 'User',
            'phone_number': '+****************',
            'gender': Gender.FEMALE,
        }
        
        profile_form = CustomerProfileForm(instance=profile, data=profile_data)
        self.assertTrue(profile_form.is_valid())
        saved_profile = profile_form.save()
        
        self.assertEqual(saved_profile.first_name, 'Workflow')
        self.assertEqual(saved_profile.last_name, 'User')

    def test_login_to_password_change_workflow(self):
        """Test workflow from login to password change."""
        # Create user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpass123456',
            is_customer=True,
            is_active=True
        )
        
        # Step 1: Login
        request = self.factory.post('/login/')
        login_data = {
            'email': '<EMAIL>',
            'password': 'oldpass123456',
        }
        
        login_form = CustomerLoginForm(request=request, data=login_data)
        self.assertTrue(login_form.is_valid())
        authenticated_user = login_form.get_user()
        self.assertEqual(authenticated_user, user)
        
        # Step 2: Change password
        password_data = {
            'old_password': 'oldpass123456',
            'new_password1': 'newpass123456',
            'new_password2': 'newpass123456',
        }
        
        password_form = CustomerPasswordChangeForm(user=user, data=password_data)
        self.assertTrue(password_form.is_valid())
        password_form.save()
        
        # Verify password changed
        user.refresh_from_db()
        self.assertTrue(user.check_password('newpass123456'))

    def test_email_change_workflow(self):
        """Test email change workflow."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123456',
            is_customer=True,
            is_active=True
        )
        
        email_data = {
            'new_email': '<EMAIL>',
            'password': 'testpass123456',
        }
        
        email_form = CustomerEmailChangeForm(user=user, data=email_data)
        self.assertTrue(email_form.is_valid())
        
        saved_user = email_form.save()
        self.assertEqual(saved_user.email, '<EMAIL>')
        self.assertEqual(saved_user.status, UserStatus.PENDING_VERIFICATION)
        self.assertFalse(saved_user.email_verified) 