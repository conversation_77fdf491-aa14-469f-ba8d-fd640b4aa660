"""Home page view for the CozyWish application."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.shortcuts import render

# Get the custom user model
User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


def home_view(request):
    """
    Display a simplified home page for development - only accounts_app and home_app are enabled.
    """
    logger.info("Displaying simplified home page - only accounts_app and home_app enabled")
    
    # Context flags to hide hero section and footer for a minimal landing page that only shows the navbar
    context = {
        'hero_section': False,   # Disable hero section (radial gradient block)
        'hide_footer': True,     # Custom flag (template-level CSS will hide footer)
    }

    return render(request, 'home_app/home_minimal.html', context)


def business_landing_view(request):
    """
    Display the service provider onboarding and benefits page.
    """
    return render(request, 'home_app/business_landing.html') 