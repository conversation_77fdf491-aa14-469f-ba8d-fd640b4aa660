{% extends 'base.html' %}

{% block title %}CozyWish For Business{% endblock %}

{% block extra_css %}
<!-- Additional Google Fonts for Display Typography -->
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* CozyWish Business Landing - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Hero Section */
    .business-hero {
        background: var(--cw-gradient-hero);
        padding: 5rem 0;
        position: relative;
        overflow: hidden;
    }

    .business-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="spa-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23spa-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .business-hero .container {
        position: relative;
        z-index: 2;
    }

    .business-title {
        font-family: var(--cw-font-display);
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(47, 22, 15, 0.1);
    }

    .business-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2.5rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Features Section */
    .features-section {
        padding: 5rem 0;
        background: white;
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        text-align: center;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        text-align: center;
        margin-bottom: 3rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Feature Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: var(--cw-gradient-card-subtle);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        padding: 2.5rem 2rem;
        text-align: center;
    }

    .card-cw:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .feature-title {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .feature-description {
        color: var(--cw-neutral-600);
        line-height: 1.6;
        font-size: 1rem;
    }

    /* Call to Action Section */
    .cta-section {
        padding: 5rem 0;
        background: var(--cw-brand-accent);
        position: relative;
    }

    .cta-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 Q20,10 15,15 Q10,10 15,5" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .cta-section .container {
        position: relative;
        z-index: 2;
    }

    .cta-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        text-align: center;
    }

    .cta-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-700);
        text-align: center;
        margin-bottom: 2.5rem;
    }

    /* How It Works Section */
    .how-it-works-section {
        padding: 5rem 0;
        background: white;
        position: relative;
    }

    .step-card {
        text-align: center;
        padding: 2rem 1rem;
        position: relative;
    }

    .step-number {
        width: 60px;
        height: 60px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0 auto 1.5rem;
        box-shadow: var(--cw-shadow-md);
        font-family: var(--cw-font-heading);
    }

    .step-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .step-description {
        color: var(--cw-neutral-600);
        line-height: 1.6;
    }

    /* Statistics Section */
    .stats-section {
        padding: 5rem 0;
        background: var(--cw-gradient-card-subtle);
        position: relative;
    }

    .stat-card {
        text-align: center;
        padding: 2rem 1rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
    }

    .stat-number {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-neutral-700);
        margin-bottom: 0.5rem;
    }

    .stat-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
    }

    /* Benefits Section */
    .benefits-section {
        padding: 5rem 0;
        background: white;
    }

    .benefit-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        transition: all 0.3s ease;
    }

    .benefit-item:hover {
        transform: translateX(10px);
        box-shadow: var(--cw-shadow-md);
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        margin-right: 1.5rem;
        flex-shrink: 0;
        box-shadow: var(--cw-shadow-sm);
    }

    .benefit-content h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
    }

    .benefit-content p {
        color: var(--cw-neutral-600);
        margin: 0;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .business-title {
            font-size: 2.5rem;
        }

        .business-subtitle {
            font-size: 1.125rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .cta-title {
            font-size: 2.25rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.875rem 1.5rem;
            font-size: 0.95rem;
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .business-hero,
        .features-section,
        .cta-section,
        .how-it-works-section,
        .stats-section,
        .benefits-section {
            padding: 3rem 0;
        }

        .stat-number {
            font-size: 2.5rem;
        }

        .step-number {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .benefit-item {
            flex-direction: column;
            text-align: center;
        }

        .benefit-icon {
            margin: 0 auto 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="business-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="business-title">CozyWish For Business</h1>
                <p class="business-subtitle">Join CozyWish - the premier spa and wellness marketplace. Connect with customers seeking quality beauty and wellness services. Grow your bookings, expand your reach, and build lasting client relationships.</p>
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mt-4">
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-primary">
                        <i class="fas fa-spa me-2"></i>Start Your Journey
                    </a>
                    <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>Provider Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title">How It Works</h2>
                <p class="section-subtitle">Get started in just a few simple steps</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h5 class="step-title">Create Your Profile</h5>
                    <p class="step-description">Sign up and create your professional business profile with services, photos, and availability.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h5 class="step-title">Get Verified</h5>
                    <p class="step-description">Complete our quick verification process to build trust with potential customers.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h5 class="step-title">Receive Bookings</h5>
                    <p class="step-description">Start receiving booking requests from customers in your area looking for your services.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h5 class="step-title">Grow Your Business</h5>
                    <p class="step-description">Build relationships, earn reviews, and expand your customer base through our platform.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title">Why Choose CozyWish</h2>
                <p class="section-subtitle">Join a platform designed specifically for spa and wellness professionals</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="feature-title">Reach Quality Customers</h5>
                    <p class="feature-description">Connect with customers who value wellness and are actively seeking professional spa and beauty services in your area.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="feature-title">Smart Booking Management</h5>
                    <p class="feature-description">Streamline your operations with our intuitive booking system, automated scheduling, and real-time availability management.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5 class="feature-title">Business Growth Tools</h5>
                    <p class="feature-description">Access comprehensive analytics, customer insights, and marketing tools to grow your business and increase revenue.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h5 class="feature-title">Build Your Reputation</h5>
                    <p class="feature-description">Showcase your expertise through customer reviews, professional profiles, and service portfolios that attract new clients.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h5 class="feature-title">Secure Payments</h5>
                    <p class="feature-description">Get paid quickly and securely with our integrated payment processing, automated invoicing, and transparent fee structure.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card-cw">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5 class="feature-title">Dedicated Support</h5>
                    <p class="feature-description">Receive ongoing support from our team who understands the wellness industry and is committed to your success.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title">Join a Growing Community</h2>
                <p class="section-subtitle">See why wellness professionals choose CozyWish</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-number">2,500+</div>
                    <div class="stat-label">Active Providers</div>
                    <p class="stat-description">Trusted wellness professionals on our platform</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-number">50K+</div>
                    <div class="stat-label">Monthly Bookings</div>
                    <p class="stat-description">Services booked through CozyWish every month</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-number">4.8★</div>
                    <div class="stat-label">Average Rating</div>
                    <p class="stat-description">Customer satisfaction rating across all services</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Provider Satisfaction</div>
                    <p class="stat-description">Of providers would recommend CozyWish</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="benefits-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="mb-5">
                    <h2 class="section-title text-start">Everything You Need to Succeed</h2>
                    <p class="section-subtitle text-start">Comprehensive tools and support for your wellness business</p>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>Mobile-First Platform</h6>
                        <p>Manage your business on-the-go with our responsive mobile platform and dedicated app.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>Insurance & Protection</h6>
                        <p>Comprehensive liability coverage and secure payment processing for peace of mind.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>Training & Resources</h6>
                        <p>Access to business training, marketing resources, and industry best practices.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="mb-5">
                    <h3 class="section-title text-start">Flexible & Transparent</h3>
                    <p class="section-subtitle text-start">No hidden fees, no long-term contracts</p>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>Competitive Commission</h6>
                        <p>Keep more of what you earn with our transparent, industry-leading commission structure.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>Flexible Scheduling</h6>
                        <p>Set your own hours and availability. Work when it suits your lifestyle and goals.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="benefit-content">
                        <h6>No Long-Term Contracts</h6>
                        <p>Join and leave anytime. We believe in earning your business through great service.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="cta-title">Ready to Transform Your Business?</h2>
                <p class="cta-subtitle">Join thousands of wellness professionals who trust CozyWish to grow their business</p>
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-primary">
                        <i class="fas fa-rocket me-2"></i>Get Started Today
                    </a>
                    <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>Already a Provider?
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
