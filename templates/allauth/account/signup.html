{% extends 'accounts_app/base_guest.html' %}

{% block title %}
    {% if 'provider' in request.path or 'business' in request.path %}
        Join <PERSON> for Business - Create Provider Account
    {% else %}
        Join <PERSON>ish - Create Customer Account
    {% endif %}
{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<link rel="stylesheet" href="{% static 'css/accounts_app/customer_signup.css' %}">
{% endblock %}

{% block content %}
<div class="card signup-card">
    <div class="signup-header">
        {% if 'provider' in request.path or 'business' in request.path %}
            <i class="fas fa-store"></i>
            <h2>Join CozyWish for Business</h2>
            <p class="text-muted">Create your business account</p>
        {% else %}
            <i class="fas fa-user-plus"></i>
            <h2>Join <PERSON></h2>
            <p class="text-muted">Create your customer account</p>
        {% endif %}
    </div>
    <div class="card-body px-4">
        <form method="post">
            {% csrf_token %}
            
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} mb-3" role="alert">
                        <i class="fas fa-info-circle me-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Non-field errors -->
            {% if form.non_field_errors %}
                <div class="alert alert-danger mb-3" role="alert">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Email field -->
            <div class="mb-3">
                <label for="{{ form.email.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Email Address
                </label>
                {% if form.email.errors %}
                    {{ form.email|add_class:"form-control is-invalid"|attr:"placeholder:Enter your email address" }}
                    <div class="invalid-feedback">
                        {% for error in form.email.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.email|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
                {% endif %}
                {% if form.email.help_text %}
                    <div class="form-text">{{ form.email.help_text }}</div>
                {% else %}
                    <div class="form-text">We'll never share your email with anyone else.</div>
                {% endif %}
            </div>

            <!-- Password field -->
            <div class="mb-3">
                <label for="{{ form.password1.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <div class="input-group">
                    {% if form.password1.errors %}
                        {{ form.password1|add_class:"form-control is-invalid"|attr:"placeholder:Create a strong password" }}
                    {% else %}
                        {{ form.password1|add_class:"form-control"|attr:"placeholder:Create a strong password" }}
                    {% endif %}
                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="{{ form.password1.id_for_label }}">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                {% if form.password1.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password1.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                {% if form.password1.help_text %}
                    <div class="form-text">{{ form.password1.help_text }}</div>
                {% else %}
                    <div class="form-text">Your password must contain at least 12 characters.</div>
                {% endif %}
            </div>

            <!-- Confirm Password field -->
            <div class="mb-3">
                <label for="{{ form.password2.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Confirm Password
                </label>
                <div class="input-group">
                    {% if form.password2.errors %}
                        {{ form.password2|add_class:"form-control is-invalid"|attr:"placeholder:Confirm your password" }}
                    {% else %}
                        {{ form.password2|add_class:"form-control"|attr:"placeholder:Confirm your password" }}
                    {% endif %}
                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="{{ form.password2.id_for_label }}">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                {% if form.password2.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password2.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                {% if form.password2.help_text %}
                    <div class="form-text">{{ form.password2.help_text }}</div>
                {% else %}
                    <div class="form-text">Enter the same password as before, for verification.</div>
                {% endif %}
            </div>

            <!-- Terms and conditions -->
            {% if form.agree_to_terms %}
            <div class="mb-3">
                <div class="form-check">
                    {% if form.agree_to_terms.errors %}
                        {{ form.agree_to_terms|add_class:"form-check-input is-invalid" }}
                    {% else %}
                        {{ form.agree_to_terms|add_class:"form-check-input" }}
                    {% endif %}
                    <label class="form-check-label" for="{{ form.agree_to_terms.id_for_label }}">
                        I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                    </label>
                    {% if form.agree_to_terms.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.agree_to_terms.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Submit button -->
            <button type="submit" class="btn btn-dark w-100 btn-custom mb-3">
                {% if 'provider' in request.path or 'business' in request.path %}
                    <i class="fas fa-store me-2"></i>Create Business Account
                {% else %}
                    <i class="fas fa-user-plus me-2"></i>Create Customer Account
                {% endif %}
            </button>
        </form>

        <!-- Sign in links -->
        <div class="text-center">
            <p class="mb-1">Already have an account?</p>
            <a href="{% url 'account_login' %}" class="btn btn-outline-dark w-100 mb-2 btn-custom">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </a>
            {% if 'provider' not in request.path and 'business' not in request.path %}
                <p class="mb-1">Are you a service provider?</p>
                <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline-dark w-100 btn-custom">
                    <i class="fas fa-briefcase me-2"></i>For Business
                </a>
            {% else %}
                <p class="mb-1">Looking for a personal account?</p>
                <a href="{% url 'account_signup' %}" class="btn btn-outline-dark w-100 btn-custom">
                    <i class="fas fa-user me-2"></i>Personal Account
                </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password show/hide toggle for multiple fields
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.toggle-password').forEach(function(button) {
        button.addEventListener('click', function() {
            const targetId = button.getAttribute('data-target');
            const input = document.getElementById(targetId);
            const icon = button.querySelector('i');
            
            if (input && icon) {
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        });
    });
});
</script>
{% endblock %} 