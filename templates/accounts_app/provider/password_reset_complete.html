{% extends 'base.html' %}

{% block title %}Password Reset Complete - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Provider Password Reset Complete */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Password Reset Complete Section */
    .password-reset-complete-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .password-reset-complete-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .password-reset-complete-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .password-reset-complete-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .password-reset-complete-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="business-complete-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><path d="M12.5,5 L20,12.5 L12.5,20 L5,12.5 Z" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23business-complete-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .password-reset-complete-header .content {
        position: relative;
        z-index: 2;
    }

    .password-reset-complete-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .password-reset-complete-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .password-reset-complete-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .password-reset-complete-body {
        padding: 3rem;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-success {
        background: var(--cw-accent-light);
        border-color: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .alert-cw-success strong {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Text Styling */
    .text-center p {
        color: var(--cw-neutral-700);
        font-family: var(--cw-font-primary);
        font-weight: 500;
        font-size: 1.125rem;
        margin-bottom: 2rem;
    }

    /* Instructions List */
    .instructions-list {
        margin: 0;
        padding-left: 1.5rem;
    }

    .instructions-list li {
        margin-bottom: 0.75rem;
        font-weight: 500;
        color: var(--cw-neutral-700);
        line-height: 1.5;
    }

    .instructions-list li:last-child {
        margin-bottom: 0;
    }

    /* Security Note */
    .security-note {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        margin-top: 2rem;
    }

    .security-note p {
        margin: 0;
        color: var(--cw-neutral-700);
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .security-note i {
        color: var(--cw-brand-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .password-reset-complete-section {
            padding: 3rem 0;
        }

        .password-reset-complete-container {
            max-width: 700px;
            padding: 0 1.5rem;
        }

        .password-reset-complete-header {
            padding: 2rem 2rem 1.5rem;
        }

        .password-reset-complete-title {
            font-size: 2rem;
        }

        .password-reset-complete-subtitle {
            font-size: 1rem;
        }

        .password-reset-complete-body {
            padding: 2rem;
        }
    }

    @media (max-width: 576px) {
        .password-reset-complete-container {
            padding: 0 1rem;
        }

        .password-reset-complete-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .password-reset-complete-body {
            padding: 1.5rem;
        }

        .password-reset-complete-title {
            font-size: 1.75rem;
        }

        .password-reset-complete-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .alert-cw {
            padding: 1.25rem;
        }

        .security-note {
            padding: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="password-reset-complete-section">
    <div class="password-reset-complete-container">
        <div class="password-reset-complete-card">
            <div class="password-reset-complete-header">
                <div class="content">
                    <div class="password-reset-complete-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1 class="password-reset-complete-title">Password Reset Complete!</h1>
                    <p class="password-reset-complete-subtitle">Your business account password has been successfully updated</p>
                </div>
            </div>

            <div class="password-reset-complete-body">
                <!-- Main message -->
                <div class="text-center">
                  <p>Your password has been set successfully. You can now log in to your business account with your new password.</p>
                </div>

                <!-- Success info -->
                <div class="alert-cw alert-cw-success" role="alert">
                  <strong><i class="fas fa-check-circle"></i>What's Next:</strong>
                  <ul class="instructions-list">
                    <li>Sign in with your new password</li>
                    <li>Complete your business profile if needed</li>
                    <li>Add your services and venue information</li>
                    <li>Start managing your bookings on CozyWish</li>
                  </ul>
                </div>

                <!-- Action buttons -->
                <div class="d-flex flex-column gap-3 mb-4">
                  <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-primary">
                    <i class="fas fa-sign-in-alt"></i>Sign In to Your Business Account
                  </a>
                                      <a href="{% url 'home_app:home' %}" class="btn-cw-secondary">
                    <i class="fas fa-home"></i>Return to Homepage
                  </a>
                </div>

                <!-- Security reminder -->
                <div class="security-note">
                  <p>
                    <i class="fas fa-shield-alt"></i>
                    For your security, remember to keep your password safe and don't share it with anyone.
                  </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
