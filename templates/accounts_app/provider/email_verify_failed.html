{% extends 'base.html' %}

{% block title %}Email Verification Failed - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Email Verification Failed */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Semantic Colors */
        --cw-error: #dc2626;
        --cw-error-light: #fef2f2;
        --cw-error-border: #fecaca;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Email Verification Failed Section */
    .email-verify-failed-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .email-verify-failed-container {
        max-width: 700px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .email-verify-failed-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .email-verify-failed-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .email-verify-failed-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="error-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 L25,15 L15,25 L5,15 Z" fill="%23fecaca" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23error-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .email-verify-failed-header .content {
        position: relative;
        z-index: 2;
    }

    .email-verify-failed-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--cw-error) 0%, #ef4444 100%);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .email-verify-failed-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .email-verify-failed-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .email-verify-failed-body {
        padding: 3rem;
    }

    /* Error Steps Card */
    .error-steps-card {
        background: var(--cw-error-light);
        border: 2px solid var(--cw-error-border);
        border-radius: 0.75rem;
        padding: 2rem;
        margin: 2rem 0;
    }

    .error-steps-title {
        color: var(--cw-error);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 1.125rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .error-steps-list {
        list-style: none;
        padding: 0;
        margin: 0;
        counter-reset: step-counter;
    }

    .error-steps-list li {
        color: var(--cw-neutral-700);
        margin-bottom: 0.75rem;
        padding-left: 2rem;
        position: relative;
        font-size: 0.95rem;
        line-height: 1.5;
        counter-increment: step-counter;
    }

    .error-steps-list li::before {
        content: counter(step-counter);
        position: absolute;
        left: 0;
        top: 0;
        background: var(--cw-error);
        color: white;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: bold;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        width: 100%;
        margin-bottom: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-ghost {
        background: transparent;
        border: none;
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        width: 100%;
    }

    .btn-cw-ghost:hover {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-error {
        background: var(--cw-error-light);
        border-color: var(--cw-error-border);
        color: #991b1b;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .email-verify-failed-section {
            padding: 3rem 0;
        }

        .email-verify-failed-container {
            max-width: 600px;
            padding: 0 1.5rem;
        }

        .email-verify-failed-header {
            padding: 2rem 2rem 1.5rem;
        }

        .email-verify-failed-title {
            font-size: 2rem;
        }

        .email-verify-failed-subtitle {
            font-size: 1rem;
        }

        .email-verify-failed-body {
            padding: 2rem;
        }

        .error-steps-card {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .email-verify-failed-container {
            padding: 0 1rem;
        }

        .email-verify-failed-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .email-verify-failed-body {
            padding: 1.5rem;
        }

        .email-verify-failed-title {
            font-size: 1.75rem;
        }

        .email-verify-failed-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .error-steps-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="email-verify-failed-section">
    <div class="email-verify-failed-container">
        <div class="email-verify-failed-card">
            <div class="email-verify-failed-header">
                <div class="content">
                    <div class="email-verify-failed-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="email-verify-failed-title">Verification Failed</h1>
                    <p class="email-verify-failed-subtitle">We couldn't verify your business email address</p>
                </div>
            </div>

            <div class="email-verify-failed-body">
                <!-- Display messages -->
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert-cw alert-cw-error mb-4" role="alert">
                      <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
                    </div>
                  {% endfor %}
                {% endif %}

                <div class="text-center mb-4">
                  <p class="mb-4" style="color: var(--cw-neutral-700); font-size: 1.1rem;">The verification link is invalid or has expired. This can happen if the link is older than 24 hours or has already been used.</p>
                </div>

                <div class="error-steps-card">
                  <h3 class="error-steps-title">
                    <i class="fas fa-lightbulb"></i>What to do next:
                  </h3>
                  <ol class="error-steps-list">
                    <li>Try signing up again with the same email address</li>
                    <li>Check if you already have an account and try logging in</li>
                    <li>Check your spam/junk folder for verification emails</li>
                    <li>Contact our support team if you continue having issues</li>
                  </ol>
                </div>

                <div class="d-grid">
                  <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-primary">
                    <i class="fas fa-user-plus"></i>Try Signing Up Again
                  </a>
                  <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-secondary">
                    <i class="fas fa-sign-in-alt"></i>Try Logging In
                  </a>
                                      <a href="{% url 'home_app:home' %}" class="btn-cw-ghost">
                    <i class="fas fa-home"></i>Return to Homepage
                  </a>
                </div>

                <!-- Support contact -->
                <div class="text-center mt-4 pt-4" style="border-top: 1px solid var(--cw-brand-accent);">
                  <p style="color: var(--cw-neutral-600); margin-bottom: 0;">
                    Still having trouble? Contact us at
                    <a href="mailto:<EMAIL>" style="font-weight: 600;"><EMAIL></a>
                  </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
