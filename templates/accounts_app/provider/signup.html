{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Join <PERSON> as a Service Provider{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-store me-2"></i>
                        Join <PERSON>ish for Business
                    </h1>
                    <p class="mb-0 mt-2">Create your business account</p>
                </div>
                
                <div class="card-body p-4">
                    <!-- Display messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Account Information Section -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-user-circle me-2"></i>Account Information
                            </h5>

                            <!-- Email field -->
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
                                </label>
                                {% if form.email.errors %}
                                    {{ form.email|add_class:"form-control is-invalid" }}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.email|add_class:"form-control" }}
                                {% endif %}
                                {% if form.email.help_text %}
                                    <div class="form-text">{{ form.email.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Password Row -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                                            <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
                                        </label>
                                        <div class="input-group">
                                            {% if form.password1.errors %}
                                                {{ form.password1|add_class:"form-control is-invalid" }}
                                            {% else %}
                                                {{ form.password1|add_class:"form-control" }}
                                            {% endif %}
                                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="#{{ form.password1.id_for_label }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        {% if form.password1.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.password1.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.password1.help_text %}
                                            <div class="form-text">{{ form.password1.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                                            <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
                                        </label>
                                        <div class="input-group">
                                            {% if form.password2.errors %}
                                                {{ form.password2|add_class:"form-control is-invalid" }}
                                            {% else %}
                                                {{ form.password2|add_class:"form-control" }}
                                            {% endif %}
                                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="#{{ form.password2.id_for_label }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        {% if form.password2.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.password2.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.password2.help_text %}
                                            <div class="form-text">{{ form.password2.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Business Information Section -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-building me-2"></i>Business Information
                            </h5>

                            <!-- Business Name -->
                            <div class="mb-3">
                                <label for="{{ form.business_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-building me-2"></i>{{ form.business_name.label }}
                                </label>
                                {% if form.business_name.errors %}
                                    {{ form.business_name|add_class:"form-control is-invalid" }}
                                    <div class="invalid-feedback">
                                        {% for error in form.business_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.business_name|add_class:"form-control" }}
                                {% endif %}
                                {% if form.business_name.help_text %}
                                    <div class="form-text">{{ form.business_name.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Business Contact Row -->
                            <div class="mb-3">
                                <label for="{{ form.contact_person_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tie me-2"></i>{{ form.contact_person_name.label }}
                                </label>
                                {% if form.contact_person_name.errors %}
                                    {{ form.contact_person_name|add_class:"form-control is-invalid" }}
                                    <div class="invalid-feedback">
                                        {% for error in form.contact_person_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.contact_person_name|add_class:"form-control" }}
                                {% endif %}
                                {% if form.contact_person_name.help_text %}
                                    <div class="form-text">{{ form.contact_person_name.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- EIN -->
                            <div class="mb-3">
                                <label for="{{ form.ein.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card me-2"></i>{{ form.ein.label }}
                                </label>
                                {% if form.ein.errors %}
                                    {{ form.ein|add_class:"form-control is-invalid" }}
                                    <div class="invalid-feedback">
                                        {% for error in form.ein.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.ein|add_class:"form-control" }}
                                {% endif %}
                                {% if form.ein.help_text %}
                                    <div class="form-text">{{ form.ein.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Submit Section -->
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Business Account
                            </button>
                        </div>

                        <!-- Login link -->
                        <div class="text-center">
                            <p class="mb-0 text-muted">Already have an account?
                                <a href="{% url 'accounts_app:service_provider_login' %}">Sign in here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
