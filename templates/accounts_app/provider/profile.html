{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Business Profile - CozyWish{% endblock %}

{% block content %}
<!-- Business Profile Page Content -->
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home_app:home' %}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Business Profile</li>
                </ol>
            </nav>
<!-- Profile Section -->
<section class="profile-section">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-hero">
            <div class="content">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="business-logo-container">
                            {% if profile.profile_picture %}
                                <img src="{{ profile.profile_picture.url }}" alt="{{ profile.business_name }}" class="business-logo">
                            {% else %}
                                <div class="business-logo bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-building fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <h1 class="business-name">{{ profile.business_name|default:"Your Business" }}</h1>
                        <p class="business-email">{{ profile.user.email }}</p>
                    </div>
                    <div class="top-actions">
                        <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn btn-cw-primary">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Business Information</h2>
            <div class="row">
                <div class="col-md-8">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-building" aria-hidden="true"></i>
                                Business Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Business Name:</div>
                                        <div class="profile-info-value">{{ profile.business_name|default:"Not provided" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">DBA Name:</div>
                                        <div class="profile-info-value">{{ profile.dba_name|default:"Not provided" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Contact Person:</div>
                                        <div class="profile-info-value">{{ profile.contact_first_name }} {{ profile.contact_last_name }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Business Phone:</div>
                                        <div class="profile-info-value">{{ profile.business_phone|default:"Not provided" }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Business Website:</div>
                                        <div class="profile-info-value">
                                            {% if profile.business_website %}
                                                <a href="{{ profile.business_website }}" target="_blank" class="text-primary">{{ profile.business_website }}</a>
                                            {% else %}
                                                Not provided
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Years in Business:</div>
                                        <div class="profile-info-value">{{ profile.years_in_business|default:"Not specified" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Business Type:</div>
                                        <div class="profile-info-value">{{ profile.get_business_type_display|default:"Not specified" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">License Number:</div>
                                        <div class="profile-info-value">{{ profile.license_number|default:"Not provided" }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Business Address
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            {% if profile.business_address %}
                                <p class="mb-2">{{ profile.business_address }}</p>
                                <p class="mb-2">{{ profile.city }}, {{ profile.state }} {{ profile.zip_code }}</p>
                                {% if profile.country %}
                                    <p class="mb-0">{{ profile.country }}</p>
                                {% endif %}
                            {% else %}
                                <p class="text-muted">No address provided</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media Section -->
        {% if profile.instagram_url or profile.facebook_url or profile.twitter_url or profile.linkedin_url %}
        <div class="profile-info-section">
            <h2 class="section-title">Social Media</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-share-alt" aria-hidden="true"></i>
                                Social Media Links
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                {% if profile.instagram_url %}
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="{{ profile.instagram_url }}" target="_blank" class="btn btn-cw-secondary w-100">
                                        <i class="fab fa-instagram me-2"></i>Instagram
                                    </a>
                                </div>
                                {% endif %}
                                {% if profile.facebook_url %}
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="{{ profile.facebook_url }}" target="_blank" class="btn btn-cw-secondary w-100">
                                        <i class="fab fa-facebook me-2"></i>Facebook
                                    </a>
                                </div>
                                {% endif %}
                                {% if profile.twitter_url %}
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="{{ profile.twitter_url }}" target="_blank" class="btn btn-cw-secondary w-100">
                                        <i class="fab fa-twitter me-2"></i>Twitter
                                    </a>
                                </div>
                                {% endif %}
                                {% if profile.linkedin_url %}
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="{{ profile.linkedin_url }}" target="_blank" class="btn btn-cw-secondary w-100">
                                        <i class="fab fa-linkedin me-2"></i>LinkedIn
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Team Members Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Team Members</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-users" aria-hidden="true"></i>
                                Your Team
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            {% if team_members %}
                                <div class="row">
                                    {% for member in team_members %}
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="team-member-card">
                                            <div class="member-avatar">
                                                {% if member.profile_picture %}
                                                    <img src="{{ member.profile_picture.url }}" alt="{{ member.get_full_name }}">
                                                {% else %}
                                                    <div class="avatar-placeholder">
                                                        {{ member.first_name.0|default:"T" }}{{ member.last_name.0|default:"" }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="member-info">
                                                <h6 class="member-name">{{ member.get_full_name }}</h6>
                                                <p class="member-role">{{ member.role|default:"Team Member" }}</p>
                                                <p class="member-email">{{ member.email }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No team members yet</h5>
                                    <p class="text-muted">Add team members to help manage your business.</p>
                                    <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-cw-primary">
                                        <i class="fas fa-plus me-2"></i>Add Team Member
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Settings Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Account Settings</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Account Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                <div class="col-12">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Email Address:</div>
                                        <div class="profile-info-value">{{ profile.user.email }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Account Type:</div>
                                        <div class="profile-info-value">
                                            <span class="badge-cw-primary">Service Provider</span>
                                        </div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Member Since:</div>
                                        <div class="profile-info-value">{{ profile.user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Last Login:</div>
                                        <div class="profile-info-value">{{ profile.user.last_login|date:"F d, Y H:i"|default:"Never" }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-cog" aria-hidden="true"></i>
                                Account Actions
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-actions">
                                <a href="{% url 'accounts_app:service_provider_change_password' %}" class="btn btn-cw-secondary w-100 mb-2">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </a>
                                <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn btn-cw-primary w-100 mb-2">
                                    <i class="fas fa-edit me-2"></i>Edit Profile
                                </a>
                                <button type="button" class="btn btn-cw-danger w-100" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                                    <i class="fas fa-user-times me-2"></i>Deactivate Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Deactivate Account Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateModalLabel">Deactivate Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate your account? This action can be reversed by contacting support.</p>
                <p class="text-muted small">Your account will be temporarily disabled and you won't be able to access your services or bookings.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="deactivateAccount()">Deactivate Account</button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden deactivate form -->
<form id="deactivate-form" method="post" action="{% url 'accounts_app:service_provider_deactivate' %}" style="display: none;">
    {% csrf_token %}
</form>

<style>
/* Professional sidebar styling with high specificity */
.dashboard-wrapper .dashboard-sidebar {
    width: 280px !important;
    min-width: 280px !important;
}

/* Remove icons and enhance text-only navigation */
.dashboard-wrapper .dashboard-sidebar .nav-link i {
    display: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link {
    font-weight: 500 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    margin: 0.125rem 1rem !important;
    border: 1px solid transparent !important;
    transition: all 0.2s ease !important;
    text-align: left !important;
}

/* Remove "Create Venue" special background styling */
.dashboard-wrapper .dashboard-sidebar .venue-creation-link {
    background: transparent !important;
    border-left: none !important;
    font-weight: 500 !important;
    margin: 0.125rem 1rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid transparent !important;
}

.dashboard-wrapper .dashboard-sidebar .venue-creation-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
    transform: none !important;
    transition: all 0.2s ease !important;
}

/* Remove dashboard button special background */
.dashboard-wrapper .dashboard-sidebar .nav-link.active {
    background: transparent !important;
    color: var(--cw-brand-primary) !important;
    border: 1px solid var(--cw-brand-accent) !important;
    font-weight: 600 !important;
    box-shadow: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
}

/* Keep badges and status icons visible */
.dashboard-wrapper .dashboard-sidebar .nav-link .badge {
    font-size: 0.7rem !important;
    display: inline-block !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-check,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-clock,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-times,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-edit {
    display: inline !important;
    opacity: 0.6 !important;
}

/* Adjust main content to accommodate wider sidebar */
.dashboard-wrapper .col-md-9.col-lg-10 {
    flex: 0 0 calc(100% - 280px) !important;
    max-width: calc(100% - 280px) !important;
}

/* CozyWish Design System - Service Provider Profile */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

/* Profile Section */
.profile-section {
    padding: 2rem 0;
    background: white;
    min-height: 100vh;
}

.profile-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Profile Header */
.profile-hero {
    background: var(--cw-gradient-card-subtle);
    border-radius: 1rem;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: var(--cw-shadow-lg);
    position: relative;
    overflow: hidden;
}

.profile-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="profile-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23profile-pattern)"/></svg>') repeat;
    opacity: 0.6;
    z-index: 1;
}

.profile-hero .content {
    position: relative;
    z-index: 2;
}

.business-logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-md);
    border: 1px solid var(--cw-brand-accent);
    transition: all 0.3s ease;
}

.business-logo-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-primary);
}

.business-logo {
    width: 120px;
    height: 120px;
    border-radius: 0.75rem;
    object-fit: cover;
    border: 2px solid var(--cw-brand-accent);
    transition: all 0.3s ease;
    display: block;
}

.business-logo:hover {
    border-color: var(--cw-brand-primary);
}

.business-name {
    font-family: var(--cw-font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.business-email {
    font-size: 1.125rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0;
}

/* Section Titles */
.section-title {
    font-family: var(--cw-font-heading);
    font-size: 2rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--cw-gradient-brand-button);
    border-radius: 2px;
}

/* Profile Cards */
.card-cw {
    border: 1px solid rgba(250, 225, 215, 0.3);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-md);
    background: white;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    margin-bottom: 2rem;
}

.card-cw:hover {
    transform: translateY(-5px);
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-accent);
}

.card-header-cw {
    background: var(--cw-gradient-card-subtle);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--cw-brand-accent);
}

.card-title-cw {
    font-family: var(--cw-font-heading);
    font-size: 1.375rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0;
}

.card-body-cw {
    padding: 2rem;
}

/* Profile Info Rows */
.profile-info-row {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(250, 225, 215, 0.3);
}

.profile-info-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.profile-info-label {
    min-width: 150px;
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 0.9rem;
}

.profile-info-value {
    flex: 1;
    color: var(--cw-neutral-700);
    font-size: 0.9rem;
}

/* Buttons */
.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: var(--cw-shadow-sm);
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
    box-shadow: var(--cw-shadow-md);
}

.btn-cw-secondary {
    background: white;
    color: var(--cw-brand-primary);
    border: 2px solid var(--cw-brand-primary);
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-cw-secondary:hover {
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-md);
}

.btn-cw-danger {
    background: white;
    color: #dc2626;
    border: 2px solid #dc2626;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-cw-danger:hover {
    background: #dc2626;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: var(--cw-shadow-md);
}

/* Badges */
.badge-cw-primary {
    background: var(--cw-brand-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Team Member Cards */
.team-member-card {
    background: white;
    border: 1px solid var(--cw-brand-accent);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.team-member-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--cw-shadow-md);
    border-color: var(--cw-brand-primary);
}

.member-avatar {
    margin-bottom: 1rem;
}

.member-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--cw-brand-accent);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--cw-brand-accent);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin: 0 auto;
}

.member-name {
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.25rem;
}

.member-role {
    color: var(--cw-neutral-600);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.member-email {
    color: var(--cw-neutral-600);
    font-size: 0.8rem;
    margin-bottom: 0;
}

/* Profile Actions */
.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-section {
        padding: 3rem 0;
    }

    .profile-container {
        padding: 0 1.5rem;
    }

    .profile-hero {
        padding: 2rem;
        text-align: center;
    }

    .top-actions {
        position: static;
        justify-content: center;
        margin-top: 1rem;
    }

    .business-name {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .profile-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-cw-primary,
    .btn-cw-secondary,
    .btn-cw-danger {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .profile-info-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .profile-info-label {
        min-width: auto;
    }

    .profile-info-value {
        text-align: left;
    }
}

@media (max-width: 576px) {
    .profile-container {
        padding: 0 1rem;
    }

    .profile-hero {
        padding: 1.5rem;
    }

    .card-body-cw {
        padding: 1.5rem;
    }

    .business-logo {
        width: 100px;
        height: 100px;
    }

    .business-logo-container {
        padding: 0.75rem;
    }
}
</style>

<script>
/**
 * Service Provider Profile Page JavaScript
 * Handles account deactivation confirmation and animations
 */

function deactivateAccount() {
    if (confirm('Are you sure you want to deactivate your account? This action can be reversed by contacting support.')) {
        document.getElementById('deactivate-form').submit();
    }
}

// Add smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading animation for buttons
    const buttons = document.querySelectorAll('.btn-cw-primary, .btn-cw-secondary, .btn-cw-danger');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('btn-cw-danger')) {
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';
                setTimeout(() => {
                    this.style.opacity = '1';
                    this.style.pointerEvents = 'auto';
                }, 1000);
            }
        });
    });
});
</script>
        </div>
    </div>
</div>
{% endblock %}
