{% extends 'base.html' %}
{% load static %}

{% block title %}Check Your Email - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Provider Signup Done */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Signup Done Section */
    .signup-done-section {
        padding: 5rem 0;
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .signup-done-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="success-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23success-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .signup-done-container {
        max-width: 700px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
        position: relative;
        z-index: 2;
    }

    .signup-done-card {
        background: white;
        border-radius: 1.5rem;
        padding: 3rem;
        box-shadow: var(--cw-shadow-lg);
        text-align: center;
        border: 1px solid var(--cw-brand-accent);
    }

    /* Success Icon */
    .success-icon-container {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        box-shadow: var(--cw-shadow-lg);
        animation: successPulse 2s ease-in-out infinite;
    }

    @keyframes successPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .success-icon {
        color: white;
        font-size: 3rem;
    }

    /* Typography */
    .success-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .success-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
    }

    .success-message {
        font-size: 1rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    /* Steps Card */
    .steps-card {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
        box-shadow: var(--cw-shadow-sm);
    }

    .steps-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .steps-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .steps-list li {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: white;
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .steps-list li:hover {
        background: var(--cw-accent-light);
        transform: translateX(5px);
    }

    .step-number {
        width: 24px;
        height: 24px;
        background: var(--cw-gradient-brand-button);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 600;
        flex-shrink: 0;
    }

    .step-text {
        color: var(--cw-neutral-800);
        font-weight: 500;
        line-height: 1.4;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 1.25rem 2.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.75rem;
        font-size: 1.125rem;
        width: 100%;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Support Info */
    .support-info {
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .support-info p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
    }

    .support-info p:last-child {
        margin-bottom: 0;
    }

    .support-info a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.2s ease;
    }

    .support-info a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .signup-done-section {
            padding: 3rem 0;
        }

        .signup-done-container {
            padding: 0 1.5rem;
        }

        .signup-done-card {
            padding: 2rem;
        }

        .success-title {
            font-size: 2rem;
        }

        .success-icon-container {
            width: 100px;
            height: 100px;
        }

        .success-icon {
            font-size: 2.5rem;
        }

        .btn-cw-primary {
            padding: 1rem 2rem;
            font-size: 1rem;
        }
    }

    @media (max-width: 576px) {
        .signup-done-container {
            padding: 0 1rem;
        }

        .signup-done-card {
            padding: 1.5rem;
        }

        .steps-card {
            padding: 1.5rem;
        }

        .success-title {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="signup-done-section">
    <div class="signup-done-container">
        <div class="signup-done-card">
            <!-- Success Icon -->
            <div class="success-icon-container">
                <i class="fas fa-envelope-open-text success-icon"></i>
            </div>

            <!-- Header -->
            <h1 class="success-title">Account Created Successfully!</h1>
            <p class="success-subtitle">Thank you for joining CozyWish as a service provider</p>

            <!-- Message -->
            <p class="success-message">
                We've sent a verification email to your inbox. Please follow the instructions to complete your registration and start building your business profile.
            </p>

            <!-- Next Steps -->
            <div class="steps-card success-steps">
                <h6 class="steps-title">
                    <i class="fas fa-info-circle"></i>
                    Next Steps:
                </h6>
                <ol class="steps-list">
                    <li>
                        <div class="step-number">1</div>
                        <div class="step-text">Check your email inbox (and spam folder)</div>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <div class="step-text">Click the verification link in the email</div>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <div class="step-text">Complete your business profile setup</div>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <div class="step-text">Start attracting new customers!</div>
                    </li>
                </ol>
            </div>

            <!-- Action Button -->
                                <a href="{% url 'home_app:home' %}" class="btn-cw-primary">
                <i class="fas fa-home"></i>
                Return to Homepage
            </a>

            <!-- Support Information -->
            <div class="support-info">
                <p>
                    <strong>Didn't receive the email?</strong> Check your spam folder or contact our support team.
                </p>
                <p>
                    Need help? Contact us at
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
