{% load static %}

<!-- Profile Completion Progress Bar -->
<link rel="stylesheet" href="{% static 'css/profile_completion.css' %}">

<div class="profile-completion-widget" data-completion-percentage="{{ user.profile_completion_percentage }}">
    {% if user.needs_profile_completion %}
        <div class="completion-badge">{{ user.get_profile_completion_suggestions.priority_actions|length }}</div>
    {% endif %}
    
    <div class="completion-header">
        <h5 class="completion-title">
            <i class="fas fa-user-circle me-2"></i>
            Profile Completion
        </h5>
        <div class="completion-percentage">{{ user.profile_completion_percentage }}%</div>
    </div>
    
    <div class="progress-container">
        <div class="progress-bar-wrapper">
            <div class="progress-bar" style="width: 0%">
                <div class="progress-bar-fill"></div>
            </div>
        </div>
        <div class="progress-steps">
            <div class="progress-step" data-step="25">25%</div>
            <div class="progress-step" data-step="50">50%</div>
            <div class="progress-step" data-step="75">75%</div>
            <div class="progress-step" data-step="100">100%</div>
        </div>
    </div>
    
    {% if user.profile_completion_percentage < 100 %}
        <div class="completion-suggestions">
            <h6 class="suggestions-title">Complete your profile:</h6>
            <div class="suggestions-list">
                {% with suggestions=user.get_profile_completion_suggestions %}
                    {% for suggestion in suggestions.suggestions|slice:":3" %}
                        <div class="suggestion-item priority-{{ suggestion.priority }}">
                            <i class="fas fa-circle-plus suggestion-icon"></i>
                            <span class="suggestion-text">{{ suggestion.message }}</span>
                            <button class="suggestion-action" data-action="{{ suggestion.action }}" aria-label="Complete {{ suggestion.message }}">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    {% endfor %}
                    
                    {% if suggestions.suggestions|length > 3 %}
                        <div class="suggestion-item priority-low">
                            <i class="fas fa-ellipsis-h suggestion-icon"></i>
                            <span class="suggestion-text">{{ suggestions.suggestions|length|add:"-3" }} more suggestions available</span>
                            <button class="suggestion-action" data-action="view_all_suggestions" aria-label="View all suggestions">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        </div>
                    {% endif %}
                {% endwith %}
            </div>
        </div>
    {% else %}
        <div class="completion-success">
            <i class="fas fa-check-circle success-icon"></i>
            <span class="success-text">Profile Complete!</span>
        </div>
    {% endif %}
</div>

<script src="{% static 'js/profile_completion.js' %}"></script> 