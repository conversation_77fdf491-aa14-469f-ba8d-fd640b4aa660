{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}My Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Customer Profile */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Profile Section */
    .profile-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .profile-container {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Profile Header */
    .profile-hero {
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: var(--cw-shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .profile-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="profile-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23profile-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .profile-hero .content {
        position: relative;
        z-index: 2;
    }

    .profile-picture-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
    }

    .profile-picture-container:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .profile-picture {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        display: block;
    }

    .profile-picture:hover {
        border-color: var(--cw-brand-primary);
    }

    /* Loading Animation */
    .profile-loading {
        position: relative;
    }

    .profile-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 40px;
        height: 40px;
        margin: -20px 0 0 -20px;
        border: 3px solid var(--cw-brand-accent);
        border-top: 3px solid var(--cw-brand-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 10;
    }

    .profile-loading .profile-picture {
        opacity: 0.5;
        filter: blur(2px);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .profile-picture-edit-btn {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
    }

    .profile-picture-edit-btn:hover {
        transform: scale(1.1);
        box-shadow: var(--cw-shadow-lg);
    }

    .profile-name {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .profile-email {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Section Titles */
    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        position: relative;
        padding-bottom: 0.5rem;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: var(--cw-gradient-brand-button);
        border-radius: 2px;
    }

    /* Top Action Buttons */
    .top-actions {
        position: absolute;
        top: 1.5rem;
        right: 2rem;
        display: flex;
        gap: 0.75rem;
        z-index: 3;
    }

    .btn-cw-small {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.375rem;
        font-size: 0.8rem;
    }

    .btn-cw-small:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-small-secondary {
        border: 1px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.375rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.375rem;
        font-size: 0.8rem;
    }

    .btn-cw-small-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Profile Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        margin-bottom: 2rem;
    }

    .card-cw:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .card-header-cw {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .card-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-body-cw {
        padding: 2rem;
    }

    /* Profile Info Rows */
    .profile-info-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .profile-info-row:last-child {
        border-bottom: none;
    }

    .profile-info-row:hover {
        background: var(--cw-accent-light);
        margin: 0 -2rem;
        padding-left: 2rem;
        padding-right: 2rem;
        border-radius: 0.5rem;
    }

    .profile-info-label {
        font-weight: 600;
        color: var(--cw-neutral-700);
        font-family: var(--cw-font-heading);
        min-width: 140px;
        flex-shrink: 0;
    }

    .profile-info-value {
        color: var(--cw-neutral-800);
        font-weight: 500;
        text-align: right;
        flex-grow: 1;
    }

    .profile-info-value.empty {
        color: var(--cw-neutral-600);
        font-style: italic;
    }

    /* Badge Styling */
    .badge-cw-primary {
        background: var(--cw-gradient-brand-button);
        color: white;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-family: var(--cw-font-heading);
        font-size: 0.875rem;
        letter-spacing: 0.025em;
    }

    /* Action Buttons Section */
    .profile-actions-section {
        background: var(--cw-accent-light);
        border-radius: 1rem;
        padding: 3rem;
        margin-top: 3rem;
        position: relative;
        overflow: hidden;
        border: 1px solid var(--cw-brand-accent);
    }

    .profile-actions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="actions-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23actions-pattern)"/></svg>') repeat;
        opacity: 0.4;
        z-index: 1;
    }

    .profile-actions-section .content {
        position: relative;
        z-index: 2;
    }

    .actions-title {
        font-family: var(--cw-font-display);
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        text-align: center;
    }

    .profile-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        justify-content: center;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-section {
            padding: 3rem 0;
        }

        .profile-container {
            padding: 0 1.5rem;
        }

        .profile-hero {
            padding: 2rem;
            text-align: center;
        }

        .top-actions {
            position: static;
            justify-content: center;
            margin-top: 1rem;
        }

        .profile-name {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.75rem;
        }

        .actions-title {
            font-size: 1.875rem;
        }

        .profile-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary,
        .btn-cw-danger {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .profile-info-row {
            flex-direction: column;
            gap: 0.5rem;
        }

        .profile-info-label {
            min-width: auto;
        }

        .profile-info-value {
            text-align: left;
        }
    }

    @media (max-width: 576px) {
        .profile-container {
            padding: 0 1rem;
        }

        .profile-hero {
            padding: 1.5rem;
        }

        .profile-actions-section {
            padding: 2rem;
        }

        .card-body-cw {
            padding: 1.5rem;
        }

        .profile-picture {
            width: 120px;
            height: 120px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Customer Profile Page JavaScript
 * Handles profile picture upload, account deactivation, and animations
 */
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture upload functionality
    const profilePictureEdit = document.getElementById('profile-picture-edit');
    const profilePictureInput = document.getElementById('profile-picture-input');
    const profilePictureForm = document.getElementById('profile-picture-form');

    if (profilePictureEdit && profilePictureInput && profilePictureForm) {
        profilePictureEdit.addEventListener('click', function() {
            profilePictureInput.click();
        });

        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG or PNG).');
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    return;
                }

                // Show loading state
                const container = document.querySelector('.profile-picture-container');
                container.classList.add('profile-loading');

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.querySelector('.profile-picture');
                    if (img) {
                        img.src = e.target.result;
                        // Auto-submit the form to save the image
                        setTimeout(() => {
                            profilePictureForm.submit();
                        }, 500);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Account deactivation confirmation
    const deactivateBtn = document.getElementById('deactivate-account-btn');
    if (deactivateBtn) {
        deactivateBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const confirmMessage =
                'Are you sure you want to deactivate your account?\n\n' +
                '• Your profile will be hidden from other users\n' +
                '• Your booking history will be preserved\n' +
                '• You can reactivate your account anytime by logging in\n\n' +
                'Click OK to proceed or Cancel to keep your account active.';

            if (confirm(confirmMessage)) {
                // Add loading state to button
                this.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> Deactivating...';
                this.disabled = true;

                setTimeout(() => {
                    document.getElementById('deactivate-form').submit();
                }, 1000);
            }
        });
    }

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Apply fade-in animation to all profile cards
    document.querySelectorAll('.profile-fade-in').forEach(element => {
        observer.observe(element);
    });
});
</script>
{% endblock %}

{% block content %}
<section class="profile-section">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-hero">
            <!-- Top Action Buttons -->
            <div class="top-actions">
                <a href="{% url 'accounts_app:customer_profile_edit' %}" class="btn-cw-small">
                    <i class="fas fa-edit" aria-hidden="true"></i>
                    Edit Profile
                </a>
                <a href="{% url 'accounts_app:customer_change_password' %}" class="btn-cw-small-secondary">
                    <i class="fas fa-key" aria-hidden="true"></i>
                    Change Password
                </a>
            </div>

            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center mb-3 mb-md-0">
                        <div class="profile-picture-container">
                            {% if profile.profile_picture %}
                                <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-picture">
                            {% else %}
                                <img src="https://via.placeholder.com/140x140/fae1d7/2F160F?text=No+Image" alt="Profile Picture" class="profile-picture">
                            {% endif %}
                            <button type="button" id="profile-picture-edit" class="profile-picture-edit-btn" aria-label="Edit profile picture">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <!-- Hidden form for profile picture upload -->
                        <form id="profile-picture-form" method="post" enctype="multipart/form-data" action="{% url 'accounts_app:customer_profile_edit' %}" style="display: none;">
                            {% csrf_token %}
                            <input type="file" id="profile-picture-input" name="profile_picture" accept="image/*">
                        </form>
                    </div>
                    <div class="col-md-9">
                        <h1 class="profile-name">{{ profile.get_full_name|default:"Welcome!" }}</h1>
                        <p class="profile-email">{{ user.email }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Profile Information</h2>
            <div class="row">
                <!-- Personal Details Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-user" aria-hidden="true"></i>
                                Personal Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">First Name:</div>
                                <div class="profile-info-value {% if not profile.first_name %}empty{% endif %}">
                                    {{ profile.first_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Last Name:</div>
                                <div class="profile-info-value {% if not profile.last_name %}empty{% endif %}">
                                    {{ profile.last_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Phone Number:</div>
                                <div class="profile-info-value {% if not profile.phone_number %}empty{% endif %}">
                                    {{ profile.phone_number|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Gender:</div>
                                <div class="profile-info-value {% if not profile.gender %}empty{% endif %}">
                                    {{ profile.get_gender_display|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Birth Date:</div>
                                <div class="profile-info-value {% if not profile.birth_month or not profile.birth_year %}empty{% endif %}">
                                    {% if profile.birth_month and profile.birth_year %}
                                        {{ profile.get_birth_month_display }} {{ profile.birth_year }}
                                    {% else %}
                                        Not provided
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">Address:</div>
                                <div class="profile-info-value {% if not profile.address %}empty{% endif %}">
                                    {{ profile.address|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">City:</div>
                                <div class="profile-info-value {% if not profile.city %}empty{% endif %}">
                                    {{ profile.city|default:"Not provided" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Account Information</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Account Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Email Address:</div>
                                        <div class="profile-info-value">{{ user.email }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Account Type:</div>
                                        <div class="profile-info-value">
                                            <span class="badge-cw-primary">Customer</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Member Since:</div>
                                        <div class="profile-info-value">{{ user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Last Login:</div>
                                        <div class="profile-info-value">
                                            {{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Actions Section -->
        <div class="profile-actions-section">
            <div class="content">
                <h2 class="actions-title">Account Settings</h2>
                <div class="profile-actions">
                    <button type="button" id="deactivate-account-btn" class="btn-cw-danger">
                        <i class="fas fa-user-times" aria-hidden="true"></i>
                        Deactivate Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hidden deactivate form -->
<form id="deactivate-form" method="post" action="{% url 'accounts_app:customer_deactivate' %}" style="display: none;">
    {% csrf_token %}
</form>
{% endblock %}
