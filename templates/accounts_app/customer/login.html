{% extends 'accounts_app/base_guest.html' %}

{% block title %}Welcome Back - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<link rel="stylesheet" href="{% static 'css/accounts_app/customer_login.css' %}">

<style>
/* ---- CozyWish Design Reference Overrides ---- */
.login-card {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.login-header {
    background: transparent;
    border-bottom: none;
}

.login-header i {
    font-size: 2.5rem;
    color: var(--cw-brand-primary, #2F160F);
}
</style>
{% endblock %}

{% block content %}
<div class="card login-card">
    <div class="login-header">
        <i class="fas fa-user-circle"></i>
        <h2>Welcome Back</h2>
        <p class="text-muted">Sign in to your CozyWish account</p>
    </div>
    <div class="card-body px-4">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger mb-3">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.login.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Email Address
                </label>
                {% if form.login.errors %}
                    {{ form.login|add_class:"form-control is-invalid"|attr:"placeholder:Enter your email address" }}
                    <div class="invalid-feedback">
                        {% for error in form.login.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.login|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <div class="input-group">
                    {% if form.password.errors %}
                        {{ form.password|add_class:"form-control is-invalid"|attr:"placeholder:Enter your password" }}
                    {% else %}
                        {{ form.password|add_class:"form-control"|attr:"placeholder:Enter your password" }}
                    {% endif %}
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                {% if form.password.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Remember me</label>
                </div>
                <a href="#" class="text-decoration-none small">Forgot password?</a>
            </div>
            
            <button type="submit" class="btn btn-dark w-100 btn-custom mb-3">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </button>
        </form>

        <div class="divider"><small class="text-muted">or continue with</small></div>
        <div class="text-center mb-3">
            <button class="btn btn-outline-secondary rounded-circle me-2"><i class="fab fa-google"></i></button>
            <button class="btn btn-outline-secondary rounded-circle me-2"><i class="fab fa-github"></i></button>
            <button class="btn btn-outline-secondary rounded-circle"><i class="fab fa-facebook"></i></button>
            <p class="small mt-2 text-muted">Social login coming soon</p>
        </div>

        <div class="text-center">
            <p class="mb-1">Don't have an account?</p>
            <a href="{% url 'accounts_app:customer_signup' %}" class="btn btn-outline-dark w-100 mb-2 btn-custom">
                <i class="fas fa-user-plus me-2"></i>Sign up as Customer
            </a>
            <p class="mb-1">Are you a service provider?</p>
            <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline-dark w-100 btn-custom">
                <i class="fas fa-briefcase me-2"></i>For Business
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Simple password show/hide toggle
document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.querySelector('.input-group .btn-outline-secondary');
    const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
    
    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
});
</script>
{% endblock %}
