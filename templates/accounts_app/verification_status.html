{% extends 'accounts_app/base_account.html' %}
{% load crispy_forms_tags %}

{% block page_title %}Verification Status{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-lg-10 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-certificate me-2"></i>Verification Status
                    </h4>
                    <p class="mb-0 small">Manage your profile verification badges</p>
                </div>
                
                <div class="card-body">
                    <!-- Overall Verification Score -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-trophy me-2"></i>Verification Score
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="h2 text-success">{{ verification_score }}%</div>
                                            <small class="text-muted">Profile Verified</small>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: {{ verification_score }}%"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="h4 text-info">{{ verified_count }}/{{ total_verifications }}</div>
                                            <small class="text-muted">Badges Earned</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Verification Badges Grid -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-shield-alt me-2"></i>Verification Badges
                            </h5>
                            <div class="row">
                                {% for verification in all_verifications %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 {% if verification.status == 'verified' %}border-success{% elif verification.status == 'pending' %}border-warning{% elif verification.status == 'rejected' %}border-danger{% else %}border-secondary{% endif %}">
                                        <div class="card-body text-center">
                                            <!-- Badge Icon -->
                                            <div class="mb-3">
                                                {% if verification.verification_type == 'email' %}
                                                    <i class="fas fa-envelope fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% elif verification.verification_type == 'phone' %}
                                                    <i class="fas fa-phone fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% elif verification.verification_type == 'identity' %}
                                                    <i class="fas fa-id-card fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% elif verification.verification_type == 'business' %}
                                                    <i class="fas fa-building fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% elif verification.verification_type == 'address' %}
                                                    <i class="fas fa-map-marker-alt fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% elif verification.verification_type == 'payment' %}
                                                    <i class="fas fa-credit-card fa-3x {% if verification.status == 'verified' %}text-success{% elif verification.status == 'pending' %}text-warning{% elif verification.status == 'rejected' %}text-danger{% else %}text-muted{% endif %}"></i>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Badge Title -->
                                            <h6 class="card-title">{{ verification.get_verification_type_display }}</h6>
                                            
                                            <!-- Status Badge -->
                                            {% if verification.status == 'verified' %}
                                                <span class="badge bg-success mb-2">
                                                    <i class="fas fa-check me-1"></i>Verified
                                                </span>
                                            {% elif verification.status == 'pending' %}
                                                <span class="badge bg-warning mb-2">
                                                    <i class="fas fa-clock me-1"></i>Pending
                                                </span>
                                            {% elif verification.status == 'rejected' %}
                                                <span class="badge bg-danger mb-2">
                                                    <i class="fas fa-times me-1"></i>Rejected
                                                </span>
                                            {% elif verification.status == 'expired' %}
                                                <span class="badge bg-secondary mb-2">
                                                    <i class="fas fa-hourglass-end me-1"></i>Expired
                                                </span>
                                            {% else %}
                                                <span class="badge bg-light text-dark mb-2">
                                                    <i class="fas fa-question me-1"></i>Not Requested
                                                </span>
                                            {% endif %}
                                            
                                            <!-- Additional Info -->
                                            <div class="small text-muted">
                                                {% if verification.verified_at %}
                                                    <div><i class="fas fa-check-circle me-1"></i>Verified: {{ verification.verified_at|date:"M d, Y" }}</div>
                                                {% endif %}
                                                {% if verification.expires_at %}
                                                    <div><i class="fas fa-calendar-alt me-1"></i>Expires: {{ verification.expires_at|date:"M d, Y" }}</div>
                                                {% endif %}
                                                {% if verification.submitted_at %}
                                                    <div><i class="fas fa-calendar-plus me-1"></i>Submitted: {{ verification.submitted_at|date:"M d, Y" }}</div>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Action Buttons -->
                                            <div class="mt-3">
                                                {% if verification.status == 'verified' %}
                                                    <button class="btn btn-success btn-sm" disabled>
                                                        <i class="fas fa-check me-1"></i>Verified
                                                    </button>
                                                {% elif verification.status == 'pending' %}
                                                    <button class="btn btn-warning btn-sm" disabled>
                                                        <i class="fas fa-clock me-1"></i>Processing
                                                    </button>
                                                {% elif verification.status == 'rejected' %}
                                                    <a href="{% url 'accounts_app:request_verification' %}" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-redo me-1"></i>Resubmit
                                                    </a>
                                                {% elif verification.status == 'expired' %}
                                                    <a href="{% url 'accounts_app:request_verification' %}" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-refresh me-1"></i>Renew
                                                    </a>
                                                {% else %}
                                                    <a href="{% url 'accounts_app:request_verification' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-plus me-1"></i>Request
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-history me-2"></i>Recent Verification Activity
                            </h5>
                            <div class="card">
                                <div class="card-body">
                                    {% if recent_activities %}
                                        <div class="timeline">
                                            {% for activity in recent_activities %}
                                            <div class="timeline-item mb-3">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0">
                                                        <div class="timeline-marker bg-{% if activity.status == 'verified' %}success{% elif activity.status == 'pending' %}warning{% elif activity.status == 'rejected' %}danger{% else %}secondary{% endif %} rounded-circle"></div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex justify-content-between">
                                                            <h6 class="mb-1">{{ activity.get_verification_type_display }} Verification</h6>
                                                            <small class="text-muted">{{ activity.updated_at|timesince }} ago</small>
                                                        </div>
                                                        <p class="text-muted mb-0">
                                                            Status changed to: 
                                                            <span class="badge bg-{% if activity.status == 'verified' %}success{% elif activity.status == 'pending' %}warning{% elif activity.status == 'rejected' %}danger{% else %}secondary{% endif %}">
                                                                {{ activity.get_status_display }}
                                                            </span>
                                                        </p>
                                                        {% if activity.rejection_reason %}
                                                            <p class="text-danger small mb-0">
                                                                <i class="fas fa-exclamation-triangle me-1"></i>{{ activity.rejection_reason }}
                                                            </p>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center text-muted">
                                            <i class="fas fa-clock fa-2x mb-3"></i>
                                            <p>No recent verification activity</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <a href="{% url 'accounts_app:request_verification' %}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-plus me-2"></i>Request New Verification
                                    </a>
                                </div>
                                <div>
                                    <a href="{% url 'accounts_app:privacy_settings' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-cogs me-2"></i>Privacy Settings
                                    </a>
                                    <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-primary ms-2">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-marker {
    width: 12px;
    height: 12px;
    margin-top: 6px;
}

.border-left-primary {
    border-left: 4px solid #007bff;
}

.border-left-warning {
    border-left: 4px solid #ffc107;
}

.card.border-success {
    border-width: 2px;
}

.card.border-warning {
    border-width: 2px;
}

.card.border-danger {
    border-width: 2px;
}

.card.border-secondary {
    border-width: 2px;
}
</style>

<script>
// Auto-refresh verification status every 30 seconds
setInterval(function() {
    fetch('{% url "accounts_app:verification_status_ajax" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update verification score
                document.querySelector('.h2.text-success').textContent = data.verification_score + '%';
                document.querySelector('.progress-bar').style.width = data.verification_score + '%';
                document.querySelector('.h4.text-info').textContent = data.verified_count + '/' + data.total_verifications;
                
                // Update any status changes
                data.verifications.forEach(verification => {
                    updateVerificationCard(verification);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching verification status:', error);
        });
}, 30000);

function updateVerificationCard(verification) {
    // This would update individual verification cards based on status changes
    // Implementation would depend on specific requirements
}
</script>
{% endblock %} 