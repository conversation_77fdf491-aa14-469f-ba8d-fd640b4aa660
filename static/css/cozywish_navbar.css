/* Guest Navbar Component CSS */

/* ===== GUEST NAVBAR STYLES ===== */

.guest-navbar {
    background: #FFFFFF;
    border-bottom: 1px solid #E0DFDE;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1.5rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

/* Remove underlines from all navbar links */
.guest-navbar a {
    text-decoration: none !important;
}

.guest-navbar a:hover,
.guest-navbar a:focus,
.guest-navbar a:active {
    text-decoration: none !important;
}

/* Override Bootstrap dropdown styles completely */
.guest-navbar .dropdown-menu .dropdown-item,
.guest-navbar .dropdown-menu .dropdown-item:hover,
.guest-navbar .dropdown-menu .dropdown-item:focus,
.guest-navbar .dropdown-menu .dropdown-item:active {
    background: #FFFFFF !important;
    color: #43251B !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

.guest-navbar .dropdown-menu .dropdown-item:hover {
    background: #FEF6F0 !important;
    color: #43251B !important;
    font-weight: 600 !important;
}

/* Bootstrap grid system is used instead of custom container */

/* Brand Section */
.guest-navbar-brand {
    display: flex;
    align-items: center;
}

.guest-navbar-logo {
    display: flex;
    align-items: center;
    text-decoration: none !important;
    color: #43251B;
    font-weight: 700;
    font-size: 1.5rem;
    font-family: serif;
    transition: color 0.2s ease;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.guest-navbar-logo:hover {
    color: #5A342A;
    text-decoration: none !important;
}

.guest-navbar-logo:focus {
    outline: none;
}

.guest-navbar-logo img {
    height: 32px;
    width: auto;
    margin-right: 0.5rem;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

/* Actions Section */
.guest-navbar-actions {
    display: flex;
    align-items: center;
}

/* For Business Button */
.btn-for-business {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #FFFFFF;
    border: 2px solid var(--bs-primary);
    color: var(--bs-primary);
    font-weight: 500;
    text-decoration: none !important;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    cursor: pointer;
}

.btn-for-business i {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
}

.btn-for-business:hover {
    background: var(--bs-primary) !important;
    color: #FFFFFF !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 37, 27, 0.2);
    text-decoration: none !important;
}

.btn-for-business:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

.btn-for-business:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(67, 37, 27, 0.15);
}

/* Menu Button */
.btn-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bs-primary);
    border: 2px solid var(--bs-primary);
    color: #FFFFFF;
    font-weight: 500;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    cursor: pointer;
    font-family: inherit;
}

.btn-menu i {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
}

.btn-menu:hover {
    background: var(--bs-secondary);
    border-color: var(--bs-secondary);
    color: #FFFFFF !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 37, 27, 0.2);
}

.btn-menu:focus {
    outline: 2px solid #FFFFFF;
    outline-offset: 2px;
}

.btn-menu:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(67, 37, 27, 0.15);
}

/* === CozyWish override: Make For Business & Menu text bold === */
.btn-for-business,
.btn-menu {
    font-weight: 700 !important;
    padding: 0.625rem 1.25rem !important; /* increase size */
    font-size: 1rem !important;           /* slightly larger text */
}

/* Bootstrap Dropdown Overrides for Guest Navbar */
.guest-navbar .dropdown-menu {
    background: #FFFFFF;
    border: 1px solid #E0DFDE;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 0.5rem 0;
    margin-top: 0.25rem;
    min-width: 200px;
    z-index: 1001;
}

.guest-navbar .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem;
    color: #43251B !important;
    text-decoration: none !important;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 600 !important;
    border: none;
    background: #FFFFFF !important;
    width: 100%;
    text-align: left;
    cursor: pointer;
    margin: 0;
    border-radius: 0;
}

.guest-navbar .dropdown-item:hover {
    background: #FEF6F0 !important;
    text-decoration: none !important;
    color: #43251B !important;
    transform: translateX(2px);
}

.guest-navbar .dropdown-item:focus {
    outline: 2px solid #43251B;
    outline-offset: -2px;
    background: #FEF6F0;
}

.guest-navbar .dropdown-item i {
    width: 18px;
    text-align: center;
    flex-shrink: 0;
    margin-right: 0.75rem;
    font-size: 0.95rem;
    color: #43251B;
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
}

.guest-navbar .dropdown-divider {
    border-color: #E0DFDE;
    margin: 0.75rem 0;
    border-width: 1px;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet and smaller desktop */
@media (max-width: 768px) {
    .guest-navbar {
        padding: 0.5rem 1rem;
    }

            /* Bootstrap handles responsive spacing */

    .btn-for-business,
    .btn-menu {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .guest-navbar .dropdown-menu {
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}

/* Mobile devices */
@media (max-width: 480px) {
    .guest-navbar-logo {
        font-size: 1.25rem;
    }

    .guest-navbar-logo img {
        height: 24px;
    }

    .btn-for-business span,
    .btn-menu span {
        display: none;
    }

    .btn-for-business,
    .btn-menu {
        padding: 0.5rem;
        min-width: 40px;
        justify-content: center;
    }

                /* Bootstrap handles responsive spacing */
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .guest-navbar {
        border-bottom: 2px solid #000000;
    }

    .btn-for-business {
        border-width: 3px;
    }

    .btn-menu {
        border-width: 3px;
    }

    .menu-dropdown {
        border-width: 2px;
        border-color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .guest-navbar-logo,
    .btn-for-business,
    .btn-menu,
    .menu-dropdown,
    .menu-item {
        transition: none;
    }

    .btn-for-business:hover,
    .btn-menu:hover {
        transform: none;
    }
}

/* ===== FOCUS VISIBILITY ===== */

/* Ensure focus is visible for keyboard navigation */
.guest-navbar *:focus {
    outline: 2px solid #43251B;
    outline-offset: 2px;
}

.guest-navbar *:focus:not(.btn-menu) {
    outline-color: #43251B;
}

.guest-navbar .btn-menu:focus {
    outline-color: #FFFFFF;
}

/* ===== CUSTOMER NAVBAR STYLES ===== */

/* Customer Action Buttons (Cart, Notifications) */
.btn-customer-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #FFFFFF;
    border: 2px solid #E0DFDE;
    color: #43251B;
    text-decoration: none !important;
    border-radius: 50%;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
}

.btn-customer-action:hover {
    background: #43251B !important;
    border-color: #43251B !important;
    color: #FFFFFF !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 37, 27, 0.2);
    text-decoration: none !important;
}

.btn-customer-action:focus {
    outline: 2px solid #43251B;
    outline-offset: 2px;
}

.btn-customer-action i {
    font-size: 1.1rem;
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
}

/* Customer Menu Button */
.btn-customer-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background: #43251B;
    border: 2px solid #43251B;
    color: #FFFFFF;
    font-weight: 500;
    text-decoration: none !important;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    cursor: pointer;
    font-family: inherit;
    min-width: 100px;
}

.btn-customer-menu i {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
}

.btn-customer-menu:hover {
    background: #5A342A;
    border-color: #5A342A;
    color: #FFFFFF !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 37, 27, 0.2);
}

.btn-customer-menu:focus {
    outline: 2px solid #FFFFFF;
    outline-offset: 2px;
}

.btn-customer-menu:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(67, 37, 27, 0.15);
}

/* User Avatar and Info */
.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: #FFFFFF;
    border-radius: 50%;
    color: #43251B;
}

.user-avatar i {
    font-size: 1.2rem;
}

.user-name {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

/* Badge Styling for Cart and Notifications */
.btn-customer-action .badge {
    font-size: 0.6rem;
    padding: 0.25em 0.5em;
    font-weight: 600;
}

/* Dropdown Items with Badges */
.guest-navbar .dropdown-item .badge {
    font-size: 0.7rem;
    padding: 0.2em 0.5em;
    font-weight: 600;
}

/* Dropdown Header Styling (for Provider navbar) */
.guest-navbar .dropdown-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: #43251B !important;
    font-weight: 700 !important;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #FEF6F0 !important;
    margin: 0;
    border-radius: 0;
}

.guest-navbar .dropdown-header i {
    color: #43251B !important;
    font-size: 1rem;
    margin-right: 0.75rem;
}

/* Logout Item Styling */
.guest-navbar .dropdown-item.text-danger {
    color: #8B2635 !important;
    font-weight: 600 !important;
}

.guest-navbar .dropdown-item.text-danger:hover {
    background: #FDE8E8 !important;
    color: #8B2635 !important;
}

.guest-navbar .dropdown-item.text-danger i {
    color: #8B2635 !important;
}

/* ===== CUSTOMER NAVBAR RESPONSIVE ===== */

/* Tablet and smaller desktop */
@media (max-width: 768px) {
    .btn-customer-action {
        width: 36px;
        height: 36px;
    }

    .btn-customer-action i {
        font-size: 1rem;
    }

    .btn-customer-menu {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        min-width: 140px;
    }

    .user-name {
        max-width: 60px;
    }
}

/* Mobile devices */
@media (max-width: 480px) {
    .btn-customer-action {
        width: 32px;
        height: 32px;
    }

    .btn-customer-action i {
        font-size: 0.9rem;
    }

    .btn-customer-menu {
        padding: 0.375rem 0.5rem;
        min-width: auto;
    }

    .user-name {
        display: none;
    }

    .user-avatar {
        width: 24px;
        height: 24px;
    }

    .user-avatar i {
        font-size: 1rem;
    }
}

/* ===== PRINT STYLES ===== */

@media print {
    .guest-navbar {
        display: none;
    }
} 