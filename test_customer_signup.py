#!/usr/bin/env python
"""
Test script to verify customer signup functionality.
This script tests the customer registration flow including:
1. Customer can register with valid email and password
2. Customer receives welcome email after registration  
3. Customer can login and logout with valid credentials
4. Customer receives error for invalid login credentials
5. After customer signup they can automatically login and redirect to home page
"""

import os
import sys
import django
import requests
from bs4 import BeautifulSoup

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts_app.models import CustomUser

User = get_user_model()

def test_customer_signup_flow():
    """Test the complete customer signup flow"""
    
    # Test data
    test_email = "<EMAIL>"
    test_password = "TestPassword123!"
    base_url = "http://127.0.0.1:8001"
    
    # Clean up any existing test user
    try:
        existing_user = User.objects.get(email=test_email)
        existing_user.delete()
        print(f"Cleaned up existing test user: {test_email}")
    except User.DoesNotExist:
        pass
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("=" * 60)
    print("TESTING CUSTOMER SIGNUP FLOW")
    print("=" * 60)
    
    # Step 1: Get the signup page and extract CSRF token
    print("\n1. Getting signup page...")
    signup_url = f"{base_url}/accounts/customer/signup/"
    response = session.get(signup_url)
    
    if response.status_code != 200:
        print(f"❌ Failed to get signup page. Status: {response.status_code}")
        return False
    
    print(f"✅ Signup page loaded successfully (Status: {response.status_code})")
    
    # Extract CSRF token
    soup = BeautifulSoup(response.content, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})
    
    if not csrf_token:
        print("❌ CSRF token not found in signup form")
        return False
    
    csrf_value = csrf_token.get('value')
    print(f"✅ CSRF token extracted: {csrf_value[:20]}...")
    
    # Step 2: Submit signup form
    print(f"\n2. Submitting signup form for {test_email}...")
    signup_data = {
        'csrfmiddlewaretoken': csrf_value,
        'email': test_email,
        'password1': test_password,
        'password2': test_password,
        'role': 'customer',  # Ensure customer role
    }
    
    response = session.post(signup_url, data=signup_data, allow_redirects=False)
    
    print(f"Signup response status: {response.status_code}")
    print(f"Signup response headers: {dict(response.headers)}")
    
    # Check if user was created
    try:
        user = User.objects.get(email=test_email)
        print(f"✅ User created successfully: {user.email} (Role: {user.role})")
    except User.DoesNotExist:
        print("❌ User was not created in database")
        return False
    
    # Step 3: Check redirect behavior
    if response.status_code == 302:
        redirect_url = response.headers.get('Location', '')
        print(f"✅ Signup redirected to: {redirect_url}")
        
        # Follow the redirect to see if user is logged in
        if redirect_url:
            if redirect_url.startswith('/'):
                redirect_url = base_url + redirect_url
            
            print(f"\n3. Following redirect to: {redirect_url}")
            redirect_response = session.get(redirect_url)
            print(f"✅ Redirect response status: {redirect_response.status_code}")
            
            # Check if we're on the home page
            if 'home' in redirect_url.lower() or redirect_url == base_url + '/':
                print("✅ Successfully redirected to home page")
            else:
                print(f"⚠️  Redirected to unexpected URL: {redirect_url}")
    else:
        print(f"❌ Expected redirect (302), got {response.status_code}")
        return False
    
    # Step 4: Test login functionality
    print(f"\n4. Testing login with created account...")
    login_url = f"{base_url}/accounts/customer/login/"
    
    # Get login page and CSRF token
    login_response = session.get(login_url)
    if login_response.status_code != 200:
        print(f"❌ Failed to get login page. Status: {login_response.status_code}")
        return False
    
    soup = BeautifulSoup(login_response.content, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})
    
    if not csrf_token:
        print("❌ CSRF token not found in login form")
        return False
    
    csrf_value = csrf_token.get('value')
    
    # Submit login form
    login_data = {
        'csrfmiddlewaretoken': csrf_value,
        'login': test_email,
        'password': test_password,
    }
    
    login_response = session.post(login_url, data=login_data, allow_redirects=False)
    
    if login_response.status_code == 302:
        print("✅ Login successful (redirected)")
        redirect_url = login_response.headers.get('Location', '')
        print(f"✅ Login redirected to: {redirect_url}")
    else:
        print(f"❌ Login failed. Status: {login_response.status_code}")
        return False
    
    # Step 5: Test invalid login
    print(f"\n5. Testing invalid login credentials...")
    invalid_login_data = {
        'csrfmiddlewaretoken': csrf_value,
        'login': test_email,
        'password': 'WrongPassword123!',
    }
    
    invalid_response = session.post(login_url, data=invalid_login_data, allow_redirects=True)
    
    if invalid_response.status_code == 200 and 'error' in invalid_response.text.lower():
        print("✅ Invalid login correctly rejected")
    else:
        print("⚠️  Invalid login behavior unclear")
    
    print("\n" + "=" * 60)
    print("CUSTOMER SIGNUP FLOW TEST COMPLETED")
    print("=" * 60)
    
    # Clean up test user
    try:
        user.delete()
        print(f"✅ Test user cleaned up: {test_email}")
    except:
        print(f"⚠️  Could not clean up test user: {test_email}")
    
    return True

if __name__ == "__main__":
    success = test_customer_signup_flow()
    if success:
        print("\n🎉 All tests completed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
