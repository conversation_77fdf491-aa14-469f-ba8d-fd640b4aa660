"""App configuration for notifications_app."""

# --- Third-Party Imports ---
from django.apps import AppConfig


class NotificationsAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'notifications_app'

    def ready(self):
        """Import signal handlers when the app is ready."""
        import notifications_app.signals
        # Connect all signals, including conditional ones
        notifications_app.signals.connect_all_signals()
