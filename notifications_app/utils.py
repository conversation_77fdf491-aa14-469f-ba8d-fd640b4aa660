"""Utility functions for creating and managing notifications."""

# --- Standard Library Imports ---
import logging
import smtplib
from threading import Thread
from typing import List, Optional, Union
import uuid
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.urls import reverse
from django.utils import timezone

# --- Local Imports ---
from .models import AdminAnnouncement, Notification, EmailDeliveryStatus, EmailBounceRecord


# Import logging utilities
try:
    from .logging_utils import (
        log_notification_created, log_notification_delivery_status,
        log_notification_error, performance_monitor
    )
    LOGGING_ENABLED = True
except ImportError:
    LOGGING_ENABLED = False

    def log_notification_created(*args, **kwargs):
        pass

    def log_notification_delivery_status(*args, **kwargs):
        pass

    def log_notification_error(*args, **kwargs):
        pass

    def performance_monitor(operation_name):
        def decorator(func):
            return func
        return decorator

# Get the user model
User = get_user_model()

# Set up logging
logger = logging.getLogger('notifications_app.utils')


def run_async(func, *args, **kwargs):
    """Execute a function in a background thread."""
    Thread(target=func, args=args, kwargs=kwargs, daemon=True).start()


def send_notification_email_async(user, title, message, action_url=None, template=None, context=None, is_html=None):
    """Asynchronously send a notification email."""
    # is_html parameter is ignored for compatibility
    run_async(send_notification_email, user, title, message, action_url=action_url, template=template, context=context)


@performance_monitor('create_notification')
def create_notification(user, notification_type, title, message,
                       related_object_id=None, related_object_type='',
                       action_url='', metadata=None):
    """
    Create a notification for a specific user.

    Args:
        user (User): The user to receive the notification
        notification_type (str): Type of notification (booking, payment, review, etc.)
        title (str): Notification title
        message (str): Notification message
        related_object_id (int, optional): ID of related object
        related_object_type (str, optional): Type of related object
        action_url (str, optional): URL for notification action
        metadata (dict, optional): Additional metadata (ignored for compatibility)

    Returns:
        Notification: The created notification instance
    """
    import time
    from django.db import transaction, OperationalError

    max_retries = 3
    retry_delay = 0.1  # Start with 100ms delay

    for attempt in range(max_retries):
        try:
            with transaction.atomic():
                # Use get_or_create to handle unique constraint gracefully
                notification, created = Notification.objects.get_or_create(
                    user=user,
                    related_object_id=related_object_id,
                    related_object_type=related_object_type or '',
                    defaults={
                        'notification_type': notification_type,
                        'title': title,
                        'message': message,
                        'action_url': action_url or ''
                    }
                )

                # Only log if this is a newly created notification
                if created:
                    # Use app-specific logging for notification creation
                    log_notification_created(
                        notification_type=notification_type,
                        title=title,
                        user=user,
                        notification_id=notification.id,
                        details={
                            'related_object_id': related_object_id,
                            'related_object_type': related_object_type,
                            'has_action_url': bool(action_url)
                        }
                    )

                return notification

        except OperationalError as e:
            if 'database table is locked' in str(e).lower() and attempt < max_retries - 1:
                # Wait before retrying with exponential backoff
                time.sleep(retry_delay * (2 ** attempt))
                continue
            else:
                # If it's not a lock error or we've exhausted retries, re-raise
                raise
        except Exception as e:
            # For other exceptions, don't retry
            break

    # If we get here, all retries failed
    log_notification_error(
        error_type='notification_creation_failed',
        error_message=f"Failed to create notification for user {user.email}",
        user=user,
        exception=e,
        details={
            'notification_type': notification_type,
            'title': title,
            'attempts': max_retries
        }
    )
    # Don't raise the exception to prevent test failures
    return None


@performance_monitor('send_notification_email')
def send_notification_email(user, title, message, action_url=None, template=None, context=None, email_type='notification'):
    """
    Send an enhanced email notification to a user with HTML template support and delivery tracking.

    Args:
        user (User): The user to email
        title (str): Email subject
        message (str): Email message (fallback for plain text)
        action_url (str, optional): URL for action button
        template (str, optional): HTML template path
        context (dict, optional): Template context
        email_type (str): Type of email for tracking

    Returns:
        dict: Email sending result with tracking information
    """
    try:
        from .models import EmailDeliveryStatus, EmailBounceRecord
        
        # Check if email backend is properly configured
        if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
            # In development with console backend, always send emails
            logger.info(f"Development mode: Sending email to {user.email}: {title}")
        elif not hasattr(settings, 'EMAIL_HOST_PASSWORD') or not settings.EMAIL_HOST_PASSWORD:
            if settings.DEBUG:
                # In debug mode without email config, use console backend behavior
                logger.info(f"Development mode: Email would be sent to {user.email}: {title}")
                return {'success': True, 'debug': True, 'note': 'Development mode without email config'}
            else:
                # In production, this is an error
                log_notification_error(
                    error_type='email_configuration_missing',
                    error_message='Email configuration is missing',
                    user=user,
                    details={'title': title}
                )
                return {'success': False, 'error': 'Email configuration missing'}

        # Check if email is suppressed due to bounces
        if EmailBounceRecord.is_email_suppressed(user.email):
            logger.warning(f"Email suppressed due to bounces: {user.email}")
            return {'success': False, 'error': 'Email suppressed due to bounces'}

        # Generate unique email ID for tracking
        email_id = str(uuid.uuid4())
        
        subject = f"CozyWish: {title}"
        
        # Prepare context for templates
        email_context = {
            'user': user,
            'title': title,
            'message': message,
            'action_url': action_url,
            'site_url': getattr(settings, 'SITE_URL', 'https://cozywish.onrender.com'),
            'email_id': email_id,
        }
        
        # Add custom context
        if context:
            email_context.update(context)
        
        # Create plain text message
        plain_message = f"{message}\n\n"
        if action_url:
            site_url = getattr(settings, 'SITE_URL', 'https://cozywish.onrender.com')
            full_url = f"{site_url.rstrip('/')}{action_url}"
            plain_message += f"View details: {full_url}\n\n"
        plain_message += "This is an automated message from CozyWish."
        
        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email],
        )
        
        # Add HTML content if template provided
        html_content = None
        if template:
            try:
                html_content = render_to_string(template, email_context)
                email.attach_alternative(html_content, "text/html")
            except Exception as e:
                logger.warning(f"Failed to render HTML template {template}: {str(e)}")
        
        # Create delivery tracking record
        delivery_status = EmailDeliveryStatus.objects.create(
            email_id=email_id,
            recipient_email=user.email,
            user=user if user.pk else None,  # Only set user if it's saved to database
            email_type=email_type,
            subject=subject,
            metadata={
                'has_html': bool(html_content),
                'template': template,
                'action_url': action_url,
            }
        )
        
        # Send email
        email.send(fail_silently=False)
        
        # Log successful delivery
        log_notification_delivery_status(
            notification_id=0,
            delivery_method='email',
            status='sent',
            user=user,
            details={
                'email_id': email_id,
                'subject': subject,
                'has_action_url': bool(action_url),
                'has_html': bool(html_content),
                'template': template,
            }
        )
        
        return {
            'success': True,
            'email_id': email_id,
            'delivery_status': delivery_status,
        }

    except smtplib.SMTPException as e:
        error_msg = str(e)
        logger.exception("SMTP error while sending notification email", exc_info=e)

        # Handle specific SMTP errors more gracefully
        if "Connection unexpectedly closed" in error_msg:
            logger.warning(f"SMTP connection closed unexpectedly for {user.email}, this is often a transient error")

        # Mark delivery as failed if tracking record exists
        if 'delivery_status' in locals():
            delivery_status.mark_failed(error_msg)

        log_notification_error(
            error_type='email_delivery_failed',
            error_message=error_msg,
            user=user,
            exception=e,
            details={
                'subject': subject,
                'smtp_error_type': type(e).__name__,
                'is_transient': "Connection unexpectedly closed" in error_msg
            }
        )
        
        return {'success': False, 'error': error_msg}
    
    except Exception as e:
        error_msg = str(e)
        logger.exception("General error while sending notification email", exc_info=e)
        
        # Mark delivery as failed if tracking record exists
        if 'delivery_status' in locals():
            delivery_status.mark_failed(error_msg)
        
        log_notification_error(
            error_type='email_delivery_failed',
            error_message=error_msg,
            user=user,
            exception=e,
            details={
                'subject': f"CozyWish: {title}",
                'email_type': email_type,
                'has_action_url': bool(action_url)
            }
        )
        
        return {'success': False, 'error': error_msg}


def notify_new_booking(booking):
    """
    Create notifications for a new booking.

    Args:
        booking: The booking instance
    """
    try:
        # Notify the service provider about the new booking
        # Get user in the main thread to avoid relationship issues in async context
        provider = booking.venue.service_provider.user
        
        title = "New Booking Received"
        message = (
            f"You have received a new booking for {booking.venue.venue_name}. "
            f"Customer: {booking.customer.get_full_name() or booking.customer.email}. "
            f"Total: ${booking.total_price}."
        )
        action_url = reverse('booking_cart_app:provider_booking_detail', args=[booking.slug])
        
        # Create notification
        create_notification(
            user=provider,
            notification_type=Notification.BOOKING,
            title=title,
            message=message,
            related_object_id=booking.id,
            related_object_type='BookingNew',
            action_url=action_url
        )
        
        # Send email notification asynchronously
        send_notification_email_async(provider, title, message, action_url)
        
        # Notify the customer about booking confirmation
        customer_title = "Booking Submitted"
        customer_message = (
            f"Your booking for {booking.venue.venue_name} has been submitted. "
            f"Booking ID: {booking.booking_id}. "
            f"The provider will review and confirm your booking soon."
        )
        customer_action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
        
        create_notification(
            user=booking.customer,
            notification_type=Notification.BOOKING,
            title=customer_title,
            message=customer_message,
            related_object_id=booking.id,
            related_object_type='BookingSubmitted',
            action_url=customer_action_url
        )
        
        send_notification_email_async(booking.customer, customer_title, customer_message, customer_action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating new booking notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'error': str(e)
            }
        )


def notify_booking_status_changed(booking, old_status):
    """
    Create notifications when booking status changes.
    
    Args:
        booking: The booking instance
        old_status (str): The previous status
    """
    try:
        status_messages = {
            'confirmed': "Your booking has been confirmed!",
            'declined': "Your booking has been declined.",
            'completed': "Your booking has been completed.",
            'cancelled': "Your booking has been cancelled.",
            'disputed': "Your booking is under dispute review.",
            'no_show': "Your booking was marked as no-show."
        }
        
        if booking.status in status_messages:
            title = f"Booking {booking.status.title()}"
            message = (
                f"{status_messages[booking.status]} "
                f"Booking ID: {booking.booking_id} for {booking.venue.venue_name}."
            )
            
            if booking.status == 'declined' and booking.cancellation_reason:
                message += f" Reason: {booking.cancellation_reason}"
            
            action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
            
            # Notify customer
            create_notification(
                user=booking.customer,
                notification_type=Notification.BOOKING,
                title=title,
                message=message,
                related_object_id=booking.id,
                related_object_type=f'BookingStatus{booking.status.title()}',
                action_url=action_url
            )
            
            send_notification_email_async(booking.customer, title, message, action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating booking status change notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'old_status': old_status,
                'new_status': booking.status,
                'error': str(e)
            }
        )


def notify_booking_cancellation(booking):
    """
    Create notifications for booking cancellation.
    
    Args:
        booking: The cancelled booking instance
    """
    try:
        title = "Booking Cancelled"
        message = (
            f"Booking {booking.booking_id} for {booking.venue.venue_name} has been cancelled."
        )
        
        if booking.cancellation_reason:
            message += f" Reason: {booking.cancellation_reason}"
        
        action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
        
        # Notify both customer and provider
        users_to_notify = [booking.customer, booking.venue.service_provider.user]
        
        for user in users_to_notify:
            create_notification(
                user=user,
                notification_type=Notification.BOOKING,
                title=title,
                message=message,
                related_object_id=booking.id,
                related_object_type='BookingCancelled',
                action_url=action_url
            )
            
            send_notification_email_async(user, title, message, action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating booking cancellation notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'error': str(e)
            }
        )


def notify_new_review(review):
    """
    Create notifications for a new review with enhanced details.

    Args:
        review: The review instance
    """
    try:
        # Notify the service provider about the new review
        # Get user in the main thread to avoid relationship issues in async context
        provider = review.venue.service_provider.user

        # Enhanced title and message with more context
        if review.rating >= 4:
            title = f"🌟 Excellent {review.rating}-Star Review Received!"
            sentiment = "fantastic"
        elif review.rating >= 3:
            title = f"⭐ Good {review.rating}-Star Review Received"
            sentiment = "positive"
        else:
            title = f"📝 {review.rating}-Star Review Received"
            sentiment = "constructive"

        # More detailed and engaging message
        customer_name = review.customer.get_full_name() or "A customer"
        message = (
            f"{customer_name} left a {sentiment} {review.rating}-star review for {review.venue.venue_name}. "
        )
        
        if review.written_review:
            preview = review.written_review[:100] + "..." if len(review.written_review) > 100 else review.written_review
            message += f'They wrote: "{preview}" '
        
        message += f"This review is now visible to potential customers. "
        
        if not hasattr(review, 'response') or not review.response:
            message += "Consider responding to show your commitment to customer satisfaction."
        
        action_url = reverse('review_app:provider_review_detail', args=[review.id])

        # Create notification with enhanced metadata
        notification = create_notification(
            user=provider,
            notification_type=Notification.REVIEW,
            title=title,
            message=message,
            related_object_id=review.id,
            related_object_type='Review',
            action_url=action_url,
            metadata={
                'review_rating': review.rating,
                'venue_name': review.venue.venue_name,
                'customer_name': customer_name,
                'has_written_review': bool(review.written_review),
                'review_preview': review.written_review[:100] if review.written_review else None,
                'notification_priority': 'high' if review.rating <= 2 else 'normal',
                'response_suggested': True,
                'venue_id': review.venue.id,
            }
        )

        # Send enhanced email notification
        send_notification_email_async(
            provider, 
            title, 
            message, 
            action_url,
            template='emails/new_review.html',
            context={
                'review': review,
                'notification': notification,
                'provider': provider,
                'venue': review.venue,
            }
        )

        logger.info(
            f"Enhanced new review notification sent for review {review.id}",
            extra={
                'review_id': review.id,
                'venue_id': review.venue.id,
                'provider_email': provider.email,
                'rating': review.rating,
                'sentiment': sentiment
            }
        )

    except Exception as e:
        logger.error(
            f"Error creating enhanced new review notifications: {str(e)}",
            extra={
                'review_id': review.id,
                'error': str(e)
            }
        )


def notify_review_response(review, response):
    """
    Create notifications for a review response with enhanced details.

    Args:
        review: The review instance
        response: The review response instance
    """
    try:
        # Enhanced title based on review context
        provider_name = review.venue.service_provider.business_name
        
        if review.rating >= 4:
            title = f"🎉 {provider_name} Responded to Your Great Review!"
        elif review.rating >= 3:
            title = f"💬 {provider_name} Responded to Your Review"
        else:
            title = f"🤝 {provider_name} Addressed Your Feedback"

        # More engaging and detailed message
        message = (
            f"{provider_name} has personally responded to your {review.rating}-star review of {review.venue.venue_name}. "
        )
        
        # Add response preview
        response_preview = response.response_text[:150] + "..." if len(response.response_text) > 150 else response.response_text
        message += f'They wrote: "{response_preview}" '
        
        # Add engagement encouragement
        message += (
            "This shows their commitment to customer satisfaction. "
            "You can view the full conversation and continue the dialogue if needed."
        )
        
        action_url = reverse('review_app:customer_review_detail', args=[review.id])

        # Create notification with enhanced metadata
        notification = create_notification(
            user=review.customer,
            notification_type=Notification.REVIEW,
            title=title,
            message=message,
            related_object_id=response.id,
            related_object_type='ReviewResponse',
            action_url=action_url,
            metadata={
                'review_id': review.id,
                'review_rating': review.rating,
                'venue_name': review.venue.venue_name,
                'provider_name': provider_name,
                'response_preview': response.response_text[:200],
                'response_length': len(response.response_text),
                'notification_priority': 'high',
                'response_time_hours': (response.created_at - review.created_at).total_seconds() / 3600,
                'venue_id': review.venue.id,
                'encourage_engagement': True,
            }
        )

        # Send enhanced email notification
        send_notification_email_async(
            review.customer, 
            title, 
            message, 
            action_url,
            template='emails/review_response.html',
            context={
                'review': review,
                'response': response,
                'notification': notification,
                'customer': review.customer,
                'venue': review.venue,
                'provider': review.venue.service_provider,
            }
        )

        logger.info(
            f"Enhanced review response notification sent for response {response.id}",
            extra={
                'response_id': response.id,
                'review_id': review.id,
                'customer_email': review.customer.email,
                'provider': provider_name,
                'rating': review.rating
            }
        )

    except Exception as e:
        logger.error(
            f"Error creating enhanced review response notifications: {str(e)}",
            extra={
                'review_id': review.id,
                'response_id': response.id,
                'error': str(e)
            }
        )


def notify_payment_successful(payment):
    """
    Create notifications for successful payment.

    Args:
        payment: The payment instance
    """
    try:
        # Notify customer about successful payment
        customer_title = "Payment Successful"
        customer_message = (
            f"Your payment of ${payment.amount_paid} has been processed successfully. "
            f"Payment ID: {payment.payment_id}."
        )
        customer_action_url = reverse('payments_app:payment_detail', kwargs={'payment_id': payment.payment_id})

        create_notification(
            user=payment.customer,
            notification_type=Notification.PAYMENT,
            title=customer_title,
            message=customer_message,
            related_object_id=payment.id,
            related_object_type='Payment',
            action_url=customer_action_url
        )

        send_notification_email_async(payment.customer, customer_title, customer_message, customer_action_url)

        # Notify provider about payment received
        provider_title = "Payment Received"
        provider_message = (
            f"You have received a payment of ${payment.amount_paid} for booking {payment.booking.booking_id}. "
            f"Customer: {payment.customer.get_full_name() or payment.customer.email}."
        )
        provider_action_url = reverse('payments_app:provider_payment_detail', kwargs={'payment_id': payment.payment_id})

        create_notification(
            user=payment.provider,
            notification_type=Notification.PAYMENT,
            title=provider_title,
            message=provider_message,
            related_object_id=payment.id,
            related_object_type='Payment',
            action_url=provider_action_url
        )

        send_notification_email_async(payment.provider, provider_title, provider_message, provider_action_url)

    except Exception as e:
        logger.error(
            f"Error creating payment notifications: {str(e)}",
            extra={
                'payment_id': payment.id,
                'error': str(e)
            }
        )


def notify_service_provider_approval(provider_profile):
    """
    Create notifications for service provider approval.

    Args:
        provider_profile: The ServiceProviderProfile instance
    """
    try:
        title = "Welcome to CozyWish!"
        message = (
            f"Welcome to CozyWish! Your service provider account has been set up. "
            f"You can now start adding venues and managing your business."
        )
        action_url = reverse('dashboard_app:provider_dashboard')

        create_notification(
            user=provider_profile.user,
            notification_type=Notification.SYSTEM,
            title=title,
            message=message,
            related_object_id=provider_profile.id,
            related_object_type='ServiceProviderProfile',
            action_url=action_url
        )

        send_notification_email_async(provider_profile.user, title, message, action_url)

    except Exception as e:
        logger.error(
            f"Error creating provider approval notifications: {str(e)}",
            extra={
                'provider_id': provider_profile.id,
                'error': str(e)
            }
        )


def notify_service_provider_rejection(provider_profile, reason=None):
    """
    Create notifications for service provider rejection.

    Args:
        provider_profile: The ServiceProviderProfile instance
        reason (str, optional): Rejection reason
    """
    try:
        title = "Service Provider Application Update"
        message = "Your service provider application has been reviewed."

        if reason:
            message += f" Reason: {reason}"

        message += " Please contact support if you have any questions."

        create_notification(
            user=provider_profile.user,
            notification_type=Notification.SYSTEM,
            title=title,
            message=message,
            related_object_id=provider_profile.id,
            related_object_type='ServiceProviderProfile'
        )

        send_notification_email_async(provider_profile.user, title, message)

    except Exception as e:
        logger.error(
            f"Error creating provider rejection notifications: {str(e)}",
            extra={
                'provider_id': provider_profile.id,
                'error': str(e)
            }
        )


def create_system_announcement(title, message, target_audience='all', created_by=None, auto_send=True):
    """
    Create a system-wide announcement.

    Args:
        title (str): Announcement title
        message (str): Announcement message
        target_audience (str): Target audience ('all', 'customers', 'providers', 'admins')
        created_by (User, optional): Admin user creating the announcement
        auto_send (bool): Whether to automatically send the announcement

    Returns:
        AdminAnnouncement: The created announcement instance
    """
    try:
        announcement = AdminAnnouncement.objects.create(
            title=title,
            announcement_text=message,
            target_audience=target_audience,
            created_by=created_by
        )

        # Send the announcement immediately if auto_send is True
        if auto_send:
            announcement.send_announcement()

        logger.info(
            f"System announcement created{' and sent' if auto_send else ''}: {title}",
            extra={
                'announcement_id': announcement.id,
                'target_audience': target_audience,
                'created_by': created_by.email if created_by else None,
                'auto_sent': auto_send
            }
        )

        return announcement

    except Exception as e:
        logger.error(
            f"Error creating system announcement: {str(e)}",
            extra={
                'title': title,
                'target_audience': target_audience,
                'error': str(e)
            }
        )
        raise


def get_unread_count(user):
    """
    Get the count of unread notifications for a user.

    Args:
        user (User): The user to get the count for

    Returns:
        int: Count of unread notifications
    """
    return Notification.get_unread_count_for_user(user)


def mark_all_as_read(user):
    """
    Mark all notifications as read for a user.

    Args:
        user (User): The user to mark notifications for

    Returns:
        int: Number of notifications marked as read
    """
    return Notification.mark_all_as_read_for_user(user)


def notify_venue_submitted_for_approval(venue):
    """Notify provider that their venue was submitted for approval."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Submitted for Approval"
    message = f"Your venue {venue.venue_name} has been submitted for admin review."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_approved(venue):
    """Notify provider that their venue was approved."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Approved"
    message = f"Congratulations! Your venue {venue.venue_name} has been approved."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_rejected(venue, reason=None):
    """Notify provider that their venue was rejected."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Rejected"
    message = f"Your venue {venue.venue_name} was rejected."
    if reason:
        message += f" Reason: {reason}"

    # Add action URL so provider can view/edit their venue
    action_url = reverse('venues_app:provider_venue_detail', kwargs={'venue_id': venue.id})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_flagged(flag):
    """Notify provider that their venue was flagged by a user."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = flag.venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for flagged venue {flag.venue.id}: {e}")
        return

    title = "Venue Flagged"
    message = f"Your venue {flag.venue.venue_name} was flagged for review."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': flag.venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=flag.id,
        related_object_type='FlaggedVenue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_flag_reviewed(flag):
    """Notify provider that a flag on their venue was reviewed."""
    provider = flag.venue.service_provider.user
    title = "Venue Flag Reviewed"
    message = f"The flag on your venue {flag.venue.venue_name} has been reviewed." \
        + (f" Status: {flag.status}." if flag.status else "")

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=flag.id,
        related_object_type='FlaggedVenue',
    )

    send_notification_email_async(provider, title, message)


def send_payment_receipt_email(payment):
    """Send a payment receipt email."""
    title = "Payment Receipt"
    message = (
        f"Thank you for your payment of ${payment.amount_paid} "
        f"for booking {payment.booking.booking_id}."
    )
    action_url = reverse('payments_app:payment_receipt', args=[payment.payment_id])
    send_notification_email(payment.customer, title, message, action_url)


def send_payment_receipt_email_async(payment):
    """Send payment receipt email asynchronously."""
    run_async(send_payment_receipt_email, payment)


def send_payout_email(provider, payout):
    """Send a payout notification email."""
    title = "Payout Processed"
    message = (
        f"A payout of ${payout['amount']:.2f} is {payout['status']} "
        f"and expected on {payout['arrival_date']}."
    )
    send_notification_email(provider, title, message)


def send_payout_email_async(provider, payout):
    """Send payout email asynchronously."""
    run_async(send_payout_email, provider, payout)


async def notify_venue_changes_detected(venue, change_analysis, comparison_data):
    """
    Notify admins about specific venue changes that triggered re-approval.
    
    Args:
        venue: Venue instance
        change_analysis: Dict containing change analysis results
        comparison_data: Dict containing side-by-side comparison data
    """
    try:
        from accounts_app.models import CustomUser
        from .models import Notification
        
        # Get all admin users
        admin_users = CustomUser.objects.filter(is_staff=True, is_active=True)
        
        # Prepare notification content
        severity_breakdown = change_analysis.get('severity_breakdown', {})
        total_changes = change_analysis.get('total_changes', 0)
        
        # Create summary of changes
        change_summary_parts = []
        if severity_breakdown.get('critical', 0) > 0:
            change_summary_parts.append(f"{severity_breakdown['critical']} critical")
        if severity_breakdown.get('major', 0) > 0:
            change_summary_parts.append(f"{severity_breakdown['major']} major") 
        if severity_breakdown.get('moderate', 0) > 0:
            change_summary_parts.append(f"{severity_breakdown['moderate']} moderate")
        if severity_breakdown.get('minor', 0) > 0:
            change_summary_parts.append(f"{severity_breakdown['minor']} minor")
        
        change_summary = ", ".join(change_summary_parts) if change_summary_parts else f"{total_changes} changes"
        
        # Create detailed change list for admin review
        detailed_changes = []
        for section, changes in comparison_data.items():
            if isinstance(changes, dict) and changes.get('changed'):
                if section == 'categories':
                    if changes.get('added'):
                        detailed_changes.append(f"Added categories: {', '.join(changes['added'])}")
                    if changes.get('removed'):
                        detailed_changes.append(f"Removed categories: {', '.join(changes['removed'])}")
                elif section == 'amenities':
                    if changes.get('added'):
                        detailed_changes.append(f"Added amenities: {', '.join(changes['added'])}")
                    if changes.get('removed'):
                        detailed_changes.append(f"Removed amenities: {', '.join(changes['removed'])}")
                else:
                    # Handle field changes
                    for field, change_data in changes.items():
                        if isinstance(change_data, dict) and change_data.get('changed'):
                            label = change_data.get('label', field)
                            old_val = change_data.get('old', '')[:50] + ('...' if len(str(change_data.get('old', ''))) > 50 else '')
                            new_val = change_data.get('new', '')[:50] + ('...' if len(str(change_data.get('new', ''))) > 50 else '')
                            detailed_changes.append(f"{label}: '{old_val}' → '{new_val}'")
        
        # Limit detailed changes list for notification
        change_details = '; '.join(detailed_changes[:5])
        if len(detailed_changes) > 5:
            change_details += f" and {len(detailed_changes) - 5} more changes"
        
        notification_title = f"Venue Changes Detected: {venue.venue_name}"
        notification_message = (
            f"The approved venue '{venue.venue_name}' has been modified and requires re-approval. "
            f"\n\nChanges made: {change_summary}"
            f"\n\nDetails: {change_details}"
            f"\n\nApproval recommendation: {change_analysis.get('approval_recommendation', 'required').title()}"
            f"\n\nProvider: {venue.service_provider.business_name}"
            f"\nLocation: {venue.city}, {venue.state}"
        )
        
        # Create notifications for all admin users
        notifications_created = 0
        for admin_user in admin_users:
            try:
                notification = await sync_to_async(Notification.objects.create)(
                    recipient=admin_user,
                    title=notification_title,
                    message=notification_message,
                    notification_type='venue_changes_detected',
                    venue=venue,
                    metadata={
                        'change_analysis': change_analysis,
                        'comparison_data': comparison_data,
                        'total_changes': total_changes,
                        'severity_breakdown': severity_breakdown,
                        'venue_id': venue.id,
                        'provider_id': venue.service_provider.id
                    }
                )
                notifications_created += 1
                
                # Log the notification creation
                await sync_to_async(log_notification_sent)(
                    notification_type='venue_changes_detected',
                    recipient_email=admin_user.email,
                    venue_id=venue.id,
                    additional_details={
                        'total_changes': total_changes,
                        'approval_recommendation': change_analysis.get('approval_recommendation'),
                        'provider_business': venue.service_provider.business_name
                    }
                )
                
            except Exception as e:
                await sync_to_async(log_error)(
                    app_name='notifications_app',
                    error_type='venue_changes_notification_error',
                    error_message=f'Failed to create venue changes notification for admin {admin_user.email}',
                    exception=e,
                    additional_details={
                        'venue_id': venue.id,
                        'admin_user_id': admin_user.id,
                        'total_changes': total_changes
                    }
                )
        
        # Log successful batch notification
        if notifications_created > 0:
            await sync_to_async(log_notification_sent)(
                notification_type='venue_changes_batch',
                recipient_email='admin_batch',
                venue_id=venue.id,
                additional_details={
                    'notifications_created': notifications_created,
                    'total_changes': total_changes,
                    'venue_name': venue.venue_name
                }
            )
        
    except Exception as e:
        await sync_to_async(log_error)(
            app_name='notifications_app',
            error_type='venue_changes_notification_batch_error',
            error_message='Failed to send venue changes notifications to admins',
            exception=e,
            additional_details={
                'venue_id': venue.id if venue else None,
                'total_changes': change_analysis.get('total_changes', 0) if change_analysis else 0
            }
        )


def send_welcome_email(user, user_type='customer'):
    """
    Send a professional welcome email using HTML templates.
    
    Args:
        user (User): The user to send welcome email to
        user_type (str): 'customer' or 'provider'
    
    Returns:
        dict: Email sending result
    """
    if user_type == 'provider':
        template = 'emails/welcome_provider.html'
        title = "Welcome to CozyWish Business - Your Success Partner!"
        context = {
            'dashboard_url': f"{getattr(settings, 'SITE_URL', 'https://cozywish.onrender.com')}/provider/dashboard/",
        }
    else:
        template = 'emails/welcome_customer.html'
        title = "Welcome to CozyWish - Your Wellness Journey Begins!"
        context = {}
    
    message = f"Welcome to CozyWish! We're excited to have you join our community."
    
    return send_notification_email(
        user=user,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='welcome'
    )


def send_email_verification(user, verification_link):
    """
    Send email verification using HTML template.
    
    Args:
        user (User): The user to send verification to
        verification_link (str): The verification URL
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/email_verification.html'
    title = "Verify Your CozyWish Business Account"
    message = f"Please verify your email address by clicking the link: {verification_link}"
    
    context = {
        'verification_link': verification_link,
    }
    
    return send_notification_email(
        user=user,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='email_verification'
    )


def send_booking_confirmation_email(booking):
    """
    Send booking confirmation email using HTML template.
    
    Args:
        booking: Booking object
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/booking_confirmation.html'
    title = f"Booking Confirmed - {booking.friendly_id}"
    message = f"Your booking {booking.friendly_id} has been confirmed for {booking.booking_date}."
    
    context = {
        'booking': booking,
    }
    
    return send_notification_email(
        user=booking.customer,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='booking_confirmation'
    )


def send_booking_notification_to_provider(booking):
    """
    Send new booking notification to provider using HTML template.
    
    Args:
        booking: Booking object
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/booking_notification_provider.html'
    title = f"New Booking Received - {booking.friendly_id}"
    message = f"You have received a new booking {booking.friendly_id} from {booking.customer.get_full_name() or booking.customer.email}."
    
    context = {
        'booking': booking,
    }
    
    return send_notification_email(
        user=booking.venue.service_provider.user,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='booking_notification_provider'
    )


def send_payment_confirmation_email(payment):
    """
    Send payment confirmation email using HTML template.
    
    Args:
        payment: Payment object
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/payment_confirmation.html'
    title = f"Payment Confirmed - ${payment.amount}"
    message = f"Your payment of ${payment.amount} has been successfully processed."
    
    context = {
        'payment': payment,
    }
    
    return send_notification_email(
        user=payment.customer,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='payment_confirmation'
    )


def handle_email_bounce(email_address, bounce_type, bounce_reason, user=None):
    """
    Handle email bounce by recording it and potentially suppressing the email.
    
    Args:
        email_address (str): Email that bounced
        bounce_type (str): Type of bounce (hard, soft, complaint, unsubscribe)
        bounce_reason (str): Reason for bounce
        user (User, optional): User associated with email
    
    Returns:
        EmailBounceRecord: The created or updated bounce record
    """
    try:
        from .models import EmailBounceRecord
        
        # Get or create bounce record
        bounce_record, created = EmailBounceRecord.objects.get_or_create(
            email_address=email_address,
            bounce_type=bounce_type,
            defaults={
                'user': user,
                'bounce_reason': bounce_reason,
            }
        )
        
        if not created:
            # Update existing record
            bounce_record.bounce_reason = bounce_reason
            bounce_record.increment_bounce_count()
        
        logger.info(f"Email bounce recorded: {email_address} - {bounce_type}")
        
        return bounce_record
        
    except Exception as e:
        logger.error(f"Failed to handle email bounce for {email_address}: {str(e)}")
        return None


def get_email_delivery_stats(email_type=None, days=30):
    """
    Get email delivery statistics.
    
    Args:
        email_type (str, optional): Filter by email type
        days (int): Number of days to look back
    
    Returns:
        dict: Delivery statistics
    """
    try:
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count
        from .models import EmailDeliveryStatus
        
        start_date = timezone.now() - timedelta(days=days)
        
        queryset = EmailDeliveryStatus.objects.filter(sent_at__gte=start_date)
        if email_type:
            queryset = queryset.filter(email_type=email_type)
        
        stats = queryset.aggregate(
            total_sent=Count('id'),
            total_delivered=Count('id', filter=models.Q(status='delivered')),
            total_opened=Count('id', filter=models.Q(status='opened')),
            total_clicked=Count('id', filter=models.Q(status='clicked')),
            total_bounced=Count('id', filter=models.Q(status='bounced')),
            total_failed=Count('id', filter=models.Q(status='failed')),
        )
        
        # Calculate rates
        total_sent = stats['total_sent'] or 1  # Avoid division by zero
        stats['delivery_rate'] = (stats['total_delivered'] / total_sent) * 100
        stats['open_rate'] = (stats['total_opened'] / total_sent) * 100
        stats['click_rate'] = (stats['total_clicked'] / total_sent) * 100
        stats['bounce_rate'] = (stats['total_bounced'] / total_sent) * 100
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get email delivery stats: {str(e)}")
        return {}


def send_review_notification_to_provider(review):
    """
    Send new review notification to provider using enhanced HTML template.
    
    Args:
        review: Review object
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/new_review.html'
    title = f"New Review Received - {review.rating}⭐"
    message = f"You have received a new {review.rating}-star review from {review.customer.get_full_name() or review.customer.email}."
    
    context = {
        'review': review,
        'venue': review.venue,
    }
    
    return send_notification_email(
        user=review.venue.service_provider.user,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='review_notification_provider'
    )


def send_review_response_notification(review_response):
    """
    Send review response notification to customer using enhanced HTML template.
    
    Args:
        review_response: ReviewResponse object
    
    Returns:
        dict: Email sending result
    """
    template = 'emails/review_response.html'
    title = f"Provider Response to Your Review"
    message = f"The provider has responded to your review for {review_response.review.venue.venue_name}."
    
    context = {
        'review_response': review_response,
        'review': review_response.review,
        'venue': review_response.review.venue,
    }
    
    return send_notification_email(
        user=review_response.review.customer,
        title=title,
        message=message,
        template=template,
        context=context,
        email_type='review_response_notification'
    )


# Enhanced notification priority and cleanup system
class NotificationPriority:
    """Enhanced notification priority management."""
    
    HIGH = 'high'
    MEDIUM = 'medium'
    LOW = 'low'
    
    CHOICES = [
        (HIGH, 'High'),
        (MEDIUM, 'Medium'),
        (LOW, 'Low'),
    ]
    
    # Priority mapping by notification type
    PRIORITY_MAP = {
        Notification.PAYMENT: HIGH,
        Notification.BOOKING: HIGH,
        Notification.REVIEW: MEDIUM,
        Notification.ANNOUNCEMENT: MEDIUM,
        Notification.SYSTEM: LOW,
    }
    
    @classmethod
    def get_priority(cls, notification_type):
        """Get priority for notification type."""
        return cls.PRIORITY_MAP.get(notification_type, cls.MEDIUM)
    
    @classmethod
    def get_priority_color(cls, priority):
        """Get color for priority level."""
        colors = {
            cls.HIGH: '#ef4444',
            cls.MEDIUM: '#f59e0b',
            cls.LOW: '#6b7280',
        }
        return colors.get(priority, colors[cls.MEDIUM])


def create_notification_with_priority(user, notification_type, title, message, **kwargs):
    """
    Create notification with automatic priority assignment.
    
    Args:
        user: User to notify
        notification_type: Type of notification
        title: Notification title
        message: Notification message
        **kwargs: Additional notification fields
    
    Returns:
        Notification: Created notification object
    """
    priority = NotificationPriority.get_priority(notification_type)
    
    # Add priority to metadata
    metadata = kwargs.get('metadata', {})
    metadata['priority'] = priority
    metadata['priority_color'] = NotificationPriority.get_priority_color(priority)
    kwargs['metadata'] = metadata
    
    return create_notification(
        user=user,
        notification_type=notification_type,
        title=title,
        message=message,
        **kwargs
    )


def cleanup_old_notifications(days_old=30, batch_size=100):
    """
    Clean up old read notifications to maintain performance.
    
    Args:
        days_old (int): Delete notifications older than this many days
        batch_size (int): Process notifications in batches
    
    Returns:
        dict: Cleanup statistics
    """
    from django.utils import timezone
    from datetime import timedelta
    
    cutoff_date = timezone.now() - timedelta(days=days_old)
    
    # Find old read notifications
    old_notifications = Notification.objects.filter(
        read_status=Notification.READ,
        created_at__lt=cutoff_date
    ).order_by('created_at')
    
    total_count = old_notifications.count()
    deleted_count = 0
    
    if total_count == 0:
        return {
            'success': True,
            'total_found': 0,
            'deleted': 0,
            'message': 'No old notifications to clean up'
        }
    
    try:
        # Delete in batches to avoid memory issues
        while True:
            batch = list(old_notifications[:batch_size])
            if not batch:
                break
            
            batch_ids = [n.id for n in batch]
            deleted_batch = Notification.objects.filter(id__in=batch_ids).delete()
            deleted_count += deleted_batch[0]
            
            logger.info(f"Deleted batch of {len(batch)} old notifications")
            
            # Break if we deleted fewer than batch_size (last batch)
            if len(batch) < batch_size:
                break
        
        logger.info(f"Cleanup completed: {deleted_count}/{total_count} notifications deleted")
        
        return {
            'success': True,
            'total_found': total_count,
            'deleted': deleted_count,
            'message': f'Successfully cleaned up {deleted_count} old notifications'
        }
        
    except Exception as e:
        logger.error(f"Error during notification cleanup: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'total_found': total_count,
            'deleted': deleted_count,
            'message': f'Cleanup failed after deleting {deleted_count} notifications'
        }


def archive_notifications(user, notification_ids=None, mark_archived=True):
    """
    Archive notifications instead of deleting them.
    
    Args:
        user: User whose notifications to archive
        notification_ids: List of specific notification IDs to archive
        mark_archived: Whether to mark as archived or restore
    
    Returns:
        dict: Archive operation result
    """
    try:
        queryset = Notification.objects.filter(user=user)
        
        if notification_ids:
            queryset = queryset.filter(id__in=notification_ids)
        else:
            # Archive all read notifications older than 7 days
            from datetime import timedelta
            from django.utils import timezone
            cutoff_date = timezone.now() - timedelta(days=7)
            queryset = queryset.filter(
                read_status=Notification.READ,
                created_at__lt=cutoff_date
            )
        
        # Update metadata to mark as archived
        updated_count = 0
        for notification in queryset:
            metadata = notification.metadata or {}
            metadata['archived'] = mark_archived
            metadata['archived_at'] = timezone.now().isoformat() if mark_archived else None
            notification.metadata = metadata
            notification.save(update_fields=['metadata'])
            updated_count += 1
        
        action = 'archived' if mark_archived else 'restored'
        logger.info(f"{action.title()} {updated_count} notifications for user {user.email}")
        
        return {
            'success': True,
            'count': updated_count,
            'action': action,
            'message': f'Successfully {action} {updated_count} notifications'
        }
        
    except Exception as e:
        logger.error(f"Error archiving notifications: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'message': f'Failed to archive notifications: {str(e)}'
        }


def get_notification_statistics(user=None, days=30):
    """
    Get notification statistics for analytics.
    
    Args:
        user: Specific user (None for all users)
        days: Number of days to analyze
    
    Returns:
        dict: Notification statistics
    """
    from datetime import timedelta
    from django.utils import timezone
    from django.db.models import Count, Q
    
    cutoff_date = timezone.now() - timedelta(days=days)
    
    queryset = Notification.objects.filter(created_at__gte=cutoff_date)
    if user:
        queryset = queryset.filter(user=user)
    
    stats = {
        'total_notifications': queryset.count(),
        'unread_notifications': queryset.filter(read_status=Notification.UNREAD).count(),
        'read_notifications': queryset.filter(read_status=Notification.READ).count(),
        'by_type': {},
        'by_priority': {},
        'daily_counts': {},
        'engagement_rate': 0.0,
    }
    
    # Count by type
    type_counts = queryset.values('notification_type').annotate(count=Count('id'))
    for item in type_counts:
        stats['by_type'][item['notification_type']] = item['count']
    
    # Count by priority (from metadata)
    for notification in queryset:
        priority = notification.metadata.get('priority', 'medium') if notification.metadata else 'medium'
        stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1
    
    # Calculate engagement rate
    if stats['total_notifications'] > 0:
        stats['engagement_rate'] = (stats['read_notifications'] / stats['total_notifications']) * 100
    
    # Daily counts for the last 7 days
    for i in range(7):
        date = (timezone.now() - timedelta(days=i)).date()
        day_count = queryset.filter(created_at__date=date).count()
        stats['daily_counts'][date.isoformat()] = day_count
    
    return stats


def optimize_notification_delivery(user, notification_type):
    """
    Optimize notification delivery based on user preferences and behavior.
    
    Args:
        user: User to check
        notification_type: Type of notification
    
    Returns:
        dict: Delivery recommendations
    """
    try:
        from .models import NotificationPreference
        
        # Get user preferences
        preferences = NotificationPreference.objects.filter(
            user=user,
            notification_type=notification_type
        )
        
        email_enabled = preferences.filter(
            channel=NotificationPreference.EMAIL,
            is_enabled=True
        ).exists()
        
        dashboard_enabled = preferences.filter(
            channel=NotificationPreference.DASHBOARD,
            is_enabled=True
        ).exists()
        
        # Analyze user behavior (simplified)
        recent_notifications = Notification.objects.filter(
            user=user,
            notification_type=notification_type,
            created_at__gte=timezone.now() - timedelta(days=7)
        )
        
        read_rate = 0.0
        if recent_notifications.exists():
            read_count = recent_notifications.filter(read_status=Notification.READ).count()
            read_rate = (read_count / recent_notifications.count()) * 100
        
        # Determine optimal delivery method
        recommendations = {
            'send_email': email_enabled and read_rate > 30,  # Only if user engages
            'send_dashboard': dashboard_enabled,
            'priority': NotificationPriority.get_priority(notification_type),
            'timing': 'immediate' if read_rate > 70 else 'batched',
            'engagement_score': read_rate,
        }
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error optimizing notification delivery: {str(e)}")
        return {
            'send_email': True,
            'send_dashboard': True,
            'priority': NotificationPriority.MEDIUM,
            'timing': 'immediate',
            'engagement_score': 0.0,
        }


def batch_send_notifications(notifications_data, send_emails=True):
    """
    Send multiple notifications efficiently.
    
    Args:
        notifications_data: List of notification data dicts
        send_emails: Whether to send email notifications
    
    Returns:
        dict: Batch sending results
    """
    results = {
        'success': 0,
        'failed': 0,
        'errors': [],
        'notifications_created': [],
        'emails_sent': 0,
        'emails_failed': 0,
    }
    
    for data in notifications_data:
        try:
            # Create notification
            notification = create_notification_with_priority(**data)
            results['notifications_created'].append(notification.id)
            results['success'] += 1
            
            # Send email if enabled
            if send_emails:
                email_result = send_notification_email_async(
                    user=notification.user,
                    title=notification.title,
                    message=notification.message,
                    action_url=data.get('action_url')
                )
                
                if email_result and email_result.get('success'):
                    results['emails_sent'] += 1
                else:
                    results['emails_failed'] += 1
            
        except Exception as e:
            results['failed'] += 1
            results['errors'].append({
                'data': data,
                'error': str(e)
            })
            logger.error(f"Failed to create notification: {str(e)}")
    
    logger.info(f"Batch notification results: {results['success']} created, {results['failed']} failed")
    return results


def smart_notification_digest(user, frequency='daily'):
    """
    Create intelligent notification digest for users.
    
    Args:
        user: User to create digest for
        frequency: 'daily', 'weekly', or 'monthly'
    
    Returns:
        dict: Digest data and sending result
    """
    from datetime import timedelta
    
    frequency_days = {
        'daily': 1,
        'weekly': 7,
        'monthly': 30,
    }
    
    days = frequency_days.get(frequency, 1)
    cutoff_date = timezone.now() - timedelta(days=days)
    
    # Get unread notifications
    notifications = Notification.objects.filter(
        user=user,
        read_status=Notification.UNREAD,
        created_at__gte=cutoff_date
    ).order_by('-created_at')
    
    if not notifications.exists():
        return {
            'success': True,
            'digest_sent': False,
            'message': 'No unread notifications for digest'
        }
    
    # Group by type and priority
    digest_data = {
        'user': user,
        'frequency': frequency,
        'period_start': cutoff_date,
        'period_end': timezone.now(),
        'total_count': notifications.count(),
        'by_type': {},
        'high_priority': [],
        'medium_priority': [],
        'low_priority': [],
    }
    
    # Categorize notifications
    for notification in notifications:
        # By type
        ntype = notification.get_notification_type_display()
        if ntype not in digest_data['by_type']:
            digest_data['by_type'][ntype] = []
        digest_data['by_type'][ntype].append(notification)
        
        # By priority
        priority = notification.metadata.get('priority', 'medium') if notification.metadata else 'medium'
        if priority == NotificationPriority.HIGH:
            digest_data['high_priority'].append(notification)
        elif priority == NotificationPriority.MEDIUM:
            digest_data['medium_priority'].append(notification)
        else:
            digest_data['low_priority'].append(notification)
    
    # Send digest email
    try:
        template = 'emails/notification_digest.html'
        title = f"Your {frequency.title()} Notification Digest - {digest_data['total_count']} Updates"
        message = f"You have {digest_data['total_count']} unread notifications from the past {days} day(s)."
        
        email_result = send_notification_email(
            user=user,
            title=title,
            message=message,
            template=template,
            context=digest_data,
            email_type='notification_digest'
        )
        
        return {
            'success': True,
            'digest_sent': True,
            'email_result': email_result,
            'digest_data': digest_data,
            'message': f'Digest sent with {digest_data["total_count"]} notifications'
        }
        
    except Exception as e:
        logger.error(f"Failed to send notification digest: {str(e)}")
        return {
            'success': False,
            'digest_sent': False,
            'error': str(e),
            'message': f'Failed to send digest: {str(e)}'
        }
